package com.allcore.common.constant;

/**
 * （登录管理常量）
 * <AUTHOR>
 * @date 2022/09/02 15:23
 */
public interface LoginManageConstant {


	/**
	 * 登录失败次数
	 */
	String ACCOUNT_FAIL_COUNT_KEY = "account.failCount";

	/**
	 * 允许用户登录IP
	 */
	String ALLOW_IP_KEY = "allow.ip";

	/**
	 * 审计日志存储上限
	 */
	String AUDIT_LOG_SAVE_SIZE_KEY = "audit.log.save.size";

	/**
	 * 临时账户周期
	 */
	String TEMP_ACCOUNT_DAY_KEY = "temp.account.day";

	/**
	 * 长期账户休眠时长
	 */
	String LONG_ACCOUNT_SLEEP_TIME_KEY = "long.account.sleep.time";

	/**
	 * 长期账户休眠周期
	 */
	String LONG_ACCOUNT_SLEEP_DAY_KEY = "long.account.sleep.day";

	/**
	 * 是否限制IP
	 */
	String ALLOW_FLAG_KEY = "allow.flag";

	/**
	 * 允许同时在线用户数
	 */
	String ALLOW_THREAD_USER_COUNT_KEY = "allow.thread.user.count";

	/**
	 * 连续登录失败锁定时间
	 */
	String LOGIN_ERROR_LOCK_TIME_KEY = "login.error.lock.time";

	/**
	 * 休眠到期提醒
	 */
	String ACCOUNT_SLEEP_WARN_DAY_KEY = "account.sleep.warn.day";

	/**
	 * 允许用户登录结束时间
	 */
	String ALLOW_LOGIN_END_TIME_KEY = "allow.login.endTime";

	/**
	 * 用户会话超时时间
	 */
	String USER_SESSION_TIME_OUT_KEY = "user.session.timeout";

	/**
	 * 允许用户登录开始时间
	 */
	String ALLOW_LOGIN_START_TIME_KEY = "allow.login.startTime";

	/**
	 * 用户口令有效日期
	 */
	String USER_PASSWORD_USE_DAY_KEY = "user.password.use.day";

	/**
	 * 允许用户登陆IP段
	 */
	String ALLOW_IP_RANGE_KEY = "allow.ip.range";
	/**
	 * 备份日志后库里存留数据时间
	 */
	String BAL_LOG_TIME = "bak.log.time";
}
