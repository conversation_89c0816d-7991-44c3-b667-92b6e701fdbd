package com.allcore.common.config;


import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RefreshScope
public class HuNanQuanJingconfig {

    public static boolean ToHNQuanJing;

    @Value("${DefectToHNQuanJing_Server:false}")
    public void HuNan_SERVICE_URL(boolean DefectToHNQuanJing_Server) {
        this.ToHNQuanJing = DefectToHNQuanJing_Server;
    }

    public static boolean ToHNZhongTai;

    @Value("${ToHNZhongTai_Server:false}")
    public void HuNan_SERVICE_URL_ZhongTai(boolean ToHNZhongTai_Server) {
        this.ToHNZhongTai = ToHNZhongTai_Server;
    }

    public static boolean FileRenameStatus;

    @Value("${HuNan_File_Rename_Status:false}")
    public void HuNan_File_Rename_Status(boolean HuNan_File_Rename_Status) {
        this.FileRenameStatus = HuNan_File_Rename_Status;
    }

    public static boolean ImageWaterStatus;

    @Value("${HnNan_Image_Water_Status:false}")
    public void ImageWaterStatus(boolean HnNan_Image_Water_Status) {
        this.ImageWaterStatus = HnNan_Image_Water_Status;
    }

    public static boolean IsLocalRecognition;
    @Value("${IS_LOCAL_RECOGNITION:false}")
    public void IsLocalRecognition(boolean IS_LOCAL_RECOGNITION) {
        this.IsLocalRecognition = IS_LOCAL_RECOGNITION;
    }

    public static boolean IsHnConfig;
    @Value("${IS_HN_CONFIG:false}")
    public void IsHnConfig(boolean IS_HN_CONFIG) {
        this.IsHnConfig = IS_HN_CONFIG;
    }

}
