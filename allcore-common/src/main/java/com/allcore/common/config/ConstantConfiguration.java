package com.allcore.common.config;


import lombok.AllArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 公共封装包配置类
 *
 * <AUTHOR>
 */
@RefreshScope
@Component
public class ConstantConfiguration {
	public static List<String> firstFilter;

	public static List<String> secondFilter;

	public static List<String> thirdFilter;

	@Value("${dept.firstFilter}")
	public void setFirstFilter(List<String> firstFilter) {
		ConstantConfiguration.firstFilter = firstFilter;
	}

	@Value("${dept.secondFilter}")
	public void setSecondFilter(List<String> secondFilter) {
		ConstantConfiguration.secondFilter = secondFilter;
	}

	@Value("${dept.thirdFilter}")
	public void setThirdFilter(List<String> thirdFilter) {
		ConstantConfiguration.thirdFilter = thirdFilter;
	}
}
