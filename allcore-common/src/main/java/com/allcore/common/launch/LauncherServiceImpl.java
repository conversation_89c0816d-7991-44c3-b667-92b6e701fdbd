package com.allcore.common.launch;

import com.alibaba.cloud.commons.lang.StringUtils;
import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.auto.service.AutoService;
import com.allcore.core.launch.constant.AppConstant;
import com.allcore.core.launch.service.LauncherService;
import com.allcore.core.launch.utils.PropsUtil;
import org.springframework.boot.builder.SpringApplicationBuilder;

import java.util.Properties;

/**
 * 启动参数拓展
 *
 * <AUTHOR>
 */
@AutoService(LauncherService.class)
public class LauncherServiceImpl implements LauncherService {

	@Override
	public void launcher(SpringApplicationBuilder builder, String appName, String profile, boolean isLocalDev) {
		Properties props = System.getProperties();
		String nacosDiscovery = System.getenv("NACOS_DISCOVERY");
		String nacosConfig = System.getenv("NACOS_CONFIG");
		String nacosNamespace = System.getenv("NACOS_NAMESPACE");
		String nacosUser = System.getenv("NACOS_USER");
		String nacosPassword = System.getenv("NACOS_PASSWORD");

		// 通用注册
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.username",StringUtils.isNotBlank(nacosUser)?nacosUser:LauncherConstant.nacosUser(profile));
		PropsUtil.setProperty(props,"spring.cloud.nacos.config.password",StringUtils.isNotBlank(nacosPassword)?nacosPassword:LauncherConstant.nacosPassword(profile));
		PropsUtil.setProperty(props,"spring.cloud.nacos.discovery.username",StringUtils.isNotBlank(nacosUser)?nacosUser:LauncherConstant.nacosUser(profile));
		PropsUtil.setProperty(props,"spring.cloud.nacos.discovery.password",StringUtils.isNotBlank(nacosPassword)?nacosPassword:LauncherConstant.nacosPassword(profile));

		PropsUtil.setProperty(props, "spring.cloud.nacos.username",
				StringUtils.isNotBlank(nacosUser)?nacosUser:LauncherConstant.nacosUser(profile));

		PropsUtil.setProperty(props, "spring.cloud.nacos.password",
				StringUtils.isNotBlank(nacosPassword)?nacosPassword:LauncherConstant.nacosPassword(profile));

		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.server-addr",
			StringUtils.isNotBlank(nacosDiscovery)?nacosDiscovery:LauncherConstant.nacosAddr(profile));
		PropsUtil.setProperty(props, "spring.cloud.nacos.config.server-addr",
			StringUtils.isNotBlank(nacosConfig)?nacosConfig:LauncherConstant.nacosAddr(profile));
		PropsUtil.setProperty(props, "spring.cloud.nacos.discovery.namespace",
				StringUtils.isNotBlank(nacosNamespace)?nacosNamespace:LauncherConstant.nacosNameSpace(profile));
		PropsUtil.setProperty(props, "spring.cloud.sentinel.transport.dashboard", LauncherConstant.sentinelAddr(profile));

		PropsUtil.setProperty(props, "spring.cloud.nacos.config.namespace",
				StringUtils.isNotBlank(nacosNamespace)?nacosNamespace:LauncherConstant.nacosNameSpace(profile));


		// dubbo注册
		PropsUtil.setProperty(props, "dubbo.application.name", appName);
		PropsUtil.setProperty(props, "dubbo.application.qos.enable", "false");
		PropsUtil.setProperty(props, "dubbo.protocol.name", "dubbo");
		PropsUtil.setProperty(props, "dubbo.registry.address", "nacos://" + LauncherConstant.nacosAddr(profile));
		PropsUtil.setProperty(props, "dubbo.version", AppConstant.APPLICATION_VERSION);
		PropsUtil.setProperty(props, "dubbo.scan.base-packages", AppConstant.BASE_PACKAGES);

		// seata注册地址
		PropsUtil.setProperty(props, "seata.service.grouplist.default", LauncherConstant.seataAddr(profile));
		// seata注册group格式
		PropsUtil.setProperty(props, "seata.tx-service-group", LauncherConstant.seataServiceGroup(appName));
		// seata配置服务group
		PropsUtil.setProperty(props, "seata.service.vgroup-mapping.".concat(LauncherConstant.seataServiceGroup(appName)), LauncherConstant.DEFAULT_MODE);
		// seata注册模式配置
		// PropsUtil.setProperty(props, "seata.registry.type", LauncherConstant.NACOS_MODE);
		// PropsUtil.setProperty(props, "seata.registry.nacos.server-addr", LauncherConstant.nacosAddr(profile));
		// PropsUtil.setProperty(props, "seata.config.type", LauncherConstant.NACOS_MODE);
		// PropsUtil.setProperty(props, "seata.config.nacos.server-addr", LauncherConstant.nacosAddr(profile));
		PropsUtil.setProperty(props, "thumbnailator.conserveMemoryWorkaround", "true");


	}

}
