package com.allcore.netty.vo;

import com.allcore.netty.entity.SpareEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 配件返回VO
 * <AUTHOR>
 * @date 2022/11/23 09:41
 **/
@Data
public class SpareForPageVO extends SpareEntity {

	@ApiModelProperty("配件类别Zh")
	private String spareTypeZh;

	@ApiModelProperty("配件型号Zh")
	private String spareModelZh;

	@ApiModelProperty("RFID码Zh")
	private String rfidGuidZh;

	@ApiModelProperty("所属部门Zh")
	private String deptCodeZh;

	@ApiModelProperty("厂家Zh")
	private String factoryCodeZh;

	@ApiModelProperty("状态Zh")
	private String stateZh;

	@ApiModelProperty("维修状态")
	private String status;

	@ApiModelProperty("维修状态Zh")
	private String statusZh;
}
