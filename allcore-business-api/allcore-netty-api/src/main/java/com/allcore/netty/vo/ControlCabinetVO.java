package com.allcore.netty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 主控柜VO
 *
 * <AUTHOR>
 * @date 2022/11/22 15:53
 **/
@Data
public class ControlCabinetVO {
	/**
	 * 主控柜GUID
	 */
	@ApiModelProperty(value = "主控柜GUID")
	private String cabinetGuid;
	/**
	 * 库房GUID
	 */
	@ApiModelProperty(value = "库房GUID")
	private String storeRoomGuid;
	/**
	 * 设备型号
	 */
	@ApiModelProperty(value = "设备型号")
	private String cabinetModel;

	@ApiModelProperty(value = "设备型号Zh")
	private String cabinetModelZh;
	/**
	 * 设备编号
	 */
	@ApiModelProperty(value = "设备编号")
	private String cabinetNo;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String cabinetName;
	/**
	 * SN码
	 */
	@ApiModelProperty(value = "SN码")
	private String sn;
	/**
	 * RFID码
	 */
	@ApiModelProperty(value = "RFID码")
	private String rfidGuid;
	/**
	 * 厂家
	 */
	@ApiModelProperty(value = "厂家")
	private String factory;

	@ApiModelProperty(value = "厂家name")
	private String factoryName;
	/**
	 * 状态 0=不启用 1=启用
	 */
	@ApiModelProperty(value = "状态 0=不启用 1=启用")
	private String status;

	@ApiModelProperty(value = "状态Zh")
	private String statusZh;

	@ApiModelProperty(value = "备注")
	private String remarks;
}
