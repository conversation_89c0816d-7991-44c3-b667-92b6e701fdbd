package com.allcore.netty.vo;

import com.allcore.netty.entity.BatteryEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 开柜返回实体类
 *
 * <AUTHOR>
 * @date 2022-11-22 10:21:29
 */
@Data
public class CabinetBatteryVO extends BatteryEntity {
	/**
	 * 电芯电压
	 */
	@ApiModelProperty(value = "电芯电压")
	private String batteryVoltage ;

	/**
	 * 容量百分比
	 */
	@ApiModelProperty(value = "容量百分比")
	private String capacityPer;

	/**
	 * 温度
	 */
	@ApiModelProperty(value = "温度")
	private String temp;

	/**
	 * 图片地址
	 */
	@ApiModelProperty(value = "图片地址")
	private String picUrl;
}
