package com.allcore.netty.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 告警信息dto
 *
 * <AUTHOR>
 * @date 2022/11/23 09:44
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WarningSearchDTO implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "GUID")
	private String warnGuid;

	@ApiModelProperty(value = "阅读状态（0未读 1已读）")
	private String warnStatus;

	@ApiModelProperty(value = "消息类型（1告警消息 2预警消息）")
	private String warnType;

	@ApiModelProperty(value = "消息内容")
	private String warnText;

	@ApiModelProperty(value = "开始时间")
	private String beginTime;

	@ApiModelProperty(value = "结束时间")
	private String endTime;

	@ApiModelProperty(value = "设备告警GUID")
	private String warnDeviceGuid;

	@ApiModelProperty(value = "接收人")
	private String receiveUser;
}
