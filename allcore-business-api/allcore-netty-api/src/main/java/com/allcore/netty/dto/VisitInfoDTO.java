package com.allcore.netty.dto;

import lombok.Data;

import java.util.List;

/**
 *
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-12-27 13:50:57
 */
@Data
public class VisitInfoDTO {
	private static final long serialVersionUID = 1L;


	/**
	 * 访问类别(0:全员访问  1部门访问)
	 */
	private String visitType;
	/**
	 * 访问所属部门
	 */
	private String visitDeptCode;
	/**
	 * 访问所属部门数组
	 */
	private List[] visitDeptCodeArr;

}
