package com.allcore.netty.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2022/11/30 0030 14:47
 */
@Data
public class NettyCabinetDTO {

	@ApiModelProperty("机柜SN码")
	private String cabinetSnCode;

	@ApiModelProperty("位置")
	private String position;

	@ApiModelProperty("温度")
	private String temperature;

	@ApiModelProperty("湿度")
	private String humidity;

	@ApiModelProperty("柜门开启状态")
	private String doorStatus;

	@ApiModelProperty("在线状态")
	private String deviceStatus;

}
