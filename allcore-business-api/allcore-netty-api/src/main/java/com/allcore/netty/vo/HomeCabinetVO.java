package com.allcore.netty.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 首页机柜VO
 *
 * <AUTHOR>
 * @date 2022/11/22 15:00
 **/
@Data
public class HomeCabinetVO {
	/**
	 * 机柜GUID
	 */
	@ApiModelProperty(value = "机柜GUID")
	private String equipmentCabinetGuid;

	/**
	 * 库房GUID
	 */
	@ApiModelProperty(value = "库房GUID")
	private String storeRoomGuid;

	/**
	 * 主控柜GUID
	 */
	@ApiModelProperty(value = "主控柜GUID")
	private String cabinetGuid;

	/**
	 * 设备类型 1:无人机；2：主控电池；3：标准电池
	 */
	@ApiModelProperty(value = "设备类型 1:无人机；2：主控电池；3：标准电池")
	private String cabinetType;

	@ApiModelProperty(value = "设备类型Zh")
	private String cabinetTypeZh;

	/**
	 * 温度
	 */
	@ApiModelProperty(value = "温度")
	private String temp;

	/**
	 * 湿度
	 */
	@ApiModelProperty(value = "湿度")
	private String hum;

	/**
	 * 状态 0=不在线 1=在线
	 */
	@ApiModelProperty(value = "状态 0=不在线 1=在线")
	private String status;
	/**
	 * 设备名称
	 */
	@ApiModelProperty(value = "设备名称")
	private String cabinetName;
}
