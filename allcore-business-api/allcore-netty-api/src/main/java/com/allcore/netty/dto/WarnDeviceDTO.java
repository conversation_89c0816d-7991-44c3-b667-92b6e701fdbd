package com.allcore.netty.dto;

import com.allcore.common.base.ZxhcEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 告警阈值表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-23 11:35:38
 */
@Data
public class WarnDeviceDTO extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = 1L;

	@ApiModelProperty(value = "GUID")
	private String warnDeviceGuid;

	@ApiModelProperty(value = "库房GUID")
	private String storeRoomGuid;

	@ApiModelProperty(value = "设备类型")
	private String deviceType;

	@ApiModelProperty(value = "设备型号")
	private String deviceModel;

	@ApiModelProperty(value = "设备编号")
	private String deviceNo;

	@ApiModelProperty(value = "位置")
	private String position;

	@ApiModelProperty(value = "维修告警次数")
	private Integer maintanceCount;

	@ApiModelProperty(value = "循环告警次数")
	private Integer cirCount;

	@ApiModelProperty(value = "设备告警类型（1.告警 2.预警）")
	private String warnDeviceType;
}
