package com.allcore.netty.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 电池信息dto
 *
 * <AUTHOR>
 * @date 2022/11/22 11:44
 **/
@Data
public class BatteryDTO {
	/**
	 * GUID
	 */
	@ApiModelProperty(value = "GUID")
	private String batteryGuid;
	/**
	 * 电池类别
	 */
	@ApiModelProperty(value = "电池类别")
	private String batteryType;
	/**
	 * 电池型号
	 */
	@ApiModelProperty(value = "电池型号")
	private String batteryModel;
	/**
	 * 电池编号
	 */
	@ApiModelProperty(value = "电池编号")
	private String batteryCode;
	/**
	 * 电池名称
	 */
	@ApiModelProperty(value = "电池名称")
	private String batteryName;
	/**
	 * SN码
	 */
	@ApiModelProperty(value = "SN码")
	private String snCode;
	/**
	 * RFID码
	 */
	@ApiModelProperty(value = "RFID码")
	private String rfidGuid;
	/**
	 * 所属部门
	 */
	@ApiModelProperty(value = "所属部门")
	private String deptCode;
	/**
	 * 厂家
	 */
	@ApiModelProperty(value = "厂家")
	private String factoryCode;
	/**
	 * 状态
	 */
	@ApiModelProperty(value = "状态")
	private String state;
	/**
	 * 循环次数
	 */
	@ApiModelProperty(value = "循环次数")
	private Integer circleCount;
	/**
	 * 维修次数
	 */
	@ApiModelProperty(value = "维修次数")
	private Integer maintainCount;
	/**
	 * 电池寿命
	 */
	@ApiModelProperty(value = "电池寿命")
	private String batteryLife;
	/**
	 * 电池寿命百分比
	 */
	@ApiModelProperty(value = "电池寿命百分比")
	private String batteryLifePer;
	/**
	 * 电池电量
	 */
	@ApiModelProperty(value = "电池电量")
	private Integer batteryLevel;
	/**
	 * 电池容量
	 */
	@ApiModelProperty(value = "电池容量")
	private String fullCapacity;
	/**
	 * 位置
	 */
	@ApiModelProperty(value = "位置")
	private String position;
	/**
	 * 工单预留字段
	 */
	@ApiModelProperty(value = "工单预留字段")
	private String orderGuid;

	@ApiModelProperty(value = "guid集合")
	private List<String> guids;


	@ApiModelProperty(value = "最小循环次数")
	private Integer minCircleCount;

	@ApiModelProperty(value = "最大循环次数")
	private Integer maxCircleCount;

	@ApiModelProperty(value = "插槽GUID")
	private String cellGuid;

	@ApiModelProperty(value = "机柜GUID")
	private String equipmentCabinetGuid;

	@ApiModelProperty(value = "设备状态")
	private String status;
}
