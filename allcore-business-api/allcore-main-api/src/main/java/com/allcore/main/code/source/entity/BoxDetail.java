package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
@TableName("main_box_detail")
@ApiModel(value = "BoxDetail对象", description = "BoxDetail对象")
public class BoxDetail extends ZxhcEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "箱变ID")
    private String boxId;

    @ApiModelProperty(value = "逆变器ID")
    private String inverterId;

}
