package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.Size;

/**
 * <AUTHOR>
 * @date 2025/5/21 16:04
 **/
@Data
@TableName("main_booster_region")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "region对象", description = "region对象")
public class BoosterRegion extends ZxhcEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "升压站id,就是deptId")
    private String boosterStationId;

    @ApiModelProperty(value = "区域名称")
    @Size(min = 1, max = 30, message = "区域名称长度范围 1-30")
    private String regionName;
}
