package com.allcore.main.code.source.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * @program: bl
 * @description: 二维码管理请求类
 * @author: fanxiang
 * @create: 2025-06-25 10:21
 **/

@Data
public class QrCodeDeviceDTO {

    @ApiModelProperty(value="设备id")
    private String deviceId;

    @ApiModelProperty(value="设备类型")
    private String deviceType;

    @ApiModelProperty(value="设备名称")
    private String deviceName;

    @ApiModelProperty(value="设备型号")
    private String deviceModel;

    @ApiModelProperty(value="文件guid")
    private String qrCodeFileGuid;

    @ApiModelProperty(value="文件名称")
    private String qrCodeFileName;

    @ApiModelProperty(value="二维码链接")
    private String qrCodeUrl;


}
