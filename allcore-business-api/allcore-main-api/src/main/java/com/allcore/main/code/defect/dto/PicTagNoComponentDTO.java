package com.allcore.main.code.defect.dto;

import com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 审核页展示
 *
 * <AUTHOR>
 * @date 2023/11/07 11:26
 **/
@Data
public class PicTagNoComponentDTO {

    @ApiModelProperty(value = "任务id")
    private String recognitionTaskId;

    @ApiModelProperty(value = "原图路径")
    private String filePath;
    private String fileName;
    private String isLight;
    private String originalName;

    @ApiModelProperty(value = "缺陷标注")
    private List<InspectionPictureTaggingVO> defectTaggings;


}