package com.allcore.main.code.fences.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class EffectPeriodDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 开始日期 年-月-日
     */
    
    private String startDate;

    /**
     * 结束日期 年-月-日
     */
    private String endDate;

    /**
     * 生效时间段列表
     */
    private List<TimeRangeDTO> timeRanges;
}
