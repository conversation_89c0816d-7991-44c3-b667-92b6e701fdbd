package com.allcore.main.code.source.vo;

import com.allcore.main.code.source.entity.Tower;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "TowerVO对象", description = "TowerVO对象")
public class TowerVO extends Tower {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单位名称Zh")
    private String deptCodeZh;

    @ApiModelProperty(value = "杆塔性质ZH")
    private String towerNatureZh;

    @ApiModelProperty(value = "是否转角(1:是 0:否 , 默认0)")
    private String isCornerZh;
    @ApiModelProperty(value = "是否适航(1:是 0:否 , 默认0)")
    private String isAirworthyZh;
    @ApiModelProperty(value = "是否同杆(1:是 0:否 , 默认0)")
    private String isSamePoleZh;

}
