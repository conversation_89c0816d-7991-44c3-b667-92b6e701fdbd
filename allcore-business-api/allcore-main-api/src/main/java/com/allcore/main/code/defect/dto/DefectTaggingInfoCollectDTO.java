package com.allcore.main.code.defect.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serializable;
import java.util.List;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 2024/3/4
 */
@Data
@ApiModel("缺陷标注信息收集DTO")
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DefectTaggingInfoCollectDTO implements Serializable {

    private static final long serialVersionUID = -4051636381476953593L;

    @ApiModelProperty(value = "图片名称")
    @NotBlank(message = "图片名称不能为空")
    private String name;

    private String incompleteName;

    @ApiModelProperty(value = "图片hash")
    @NotBlank(message = "图片hash不能为空")
    private String imgMd5;

    @ApiModelProperty(value = "缺陷数量")
    @NotNull(message = "缺陷数量不能为空")
    @Min(value = 1, message = "缺陷数量不能小于1")
    private Integer num;

    @Valid
    @ApiModelProperty(value = "缺陷标注信息收集项列表")
    @NotNull(message = "缺陷标注信息收集项列表不能为空")
    @Size(min = 1, message = "缺陷标注信息收集项列表不能为空")
    private List<DefectTaggingInfoCollectItemDTO> itemList;

}
