package com.allcore.main.code.fences.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class FencesRequest implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 站点code
     */
    private String deptCode;
    /**
     * 站点code
     */
    private String deptName;
    /**
     * 围栏id
     */
    private String id;
    /**
     * 围栏业务类型名称 区域,静默区,巡检点,⾏政区,停⻋场,作业点
     */
    private String bizName;

    /**
     * 围栏空间类型INDOOR 室内 OUTDOOR 室外
     */
    private String spaceType;

    /**
     * 名称 最大长度 30
     */
    private String name;

    /**
     * 静态围栏多边形边界
     */
    private List<List<List<Float>>> polygon;

    /**
     * 是否为静默区域
     */
    private Boolean silence;


    /**
     * 生效时间
     */
    private EffectPeriodDTO effectPeriod;

    /**
     * 是否生效
     */
    private Boolean effective;
}
