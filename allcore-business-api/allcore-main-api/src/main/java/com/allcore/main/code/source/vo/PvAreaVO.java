package com.allcore.main.code.source.vo;

import com.allcore.main.code.source.entity.PvArea;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PvAreaVO对象", description = "PvAreaVO对象")
public class PvAreaVO extends PvArea {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单位名称Zh")
    private String deptCodeZh;

    @ApiModelProperty(value = "组件厂商")
    private String componentManufacturerZh;

    @ApiModelProperty(value = "光伏组件型号")
    private String componentModelZh;

    @ApiModelProperty(value = "支架类型")
    private String supportTypeZh;

    @ApiModelProperty(value = "支架组串排列方式")
    private String supportArrangementMethodZh;

}
