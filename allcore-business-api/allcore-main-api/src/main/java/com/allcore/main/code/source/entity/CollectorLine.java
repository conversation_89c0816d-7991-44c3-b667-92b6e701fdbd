package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 集电线路
 */
@Data
@TableName("main_collector_line")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "CollectorLine对象", description = "CollectorLine对象")
public class CollectorLine extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "线路名称")
    @NotBlank(message = "设备名称不能为空", groups = {update.class, save.class})
    @Size(min = 1, max = 64, message = "风机名称长度范围 1-64", groups = {update.class, save.class})
    private String lineName;

    @ApiModelProperty(value = "区域特征/地区特征")
    @NotBlank(message = "区域特征不能为空", groups = {update.class, save.class})
    @Size(min = 1, max = 64, message = "区域特征长度范围 1-64", groups = {update.class, save.class})
    private String regionFeatures;

    @ApiModelProperty(value = "电压等级")
    @NotBlank(message = "电压等级不能为空", groups = {update.class, save.class})
    @Size(min = 1, max = 64, message = "电压等级长度范围 1-64", groups = {update.class, save.class})
    private String voltageLevel;

    @ApiModelProperty(value = "架设方式")
    @NotBlank(message = "架设方式不能为空", groups = {update.class, save.class})
    @Size(min = 1, max = 64, message = "架设方式长度范围 1-64", groups = {update.class, save.class})
    private String erectionMethod;

    @ApiModelProperty(value = "线路总长度（km）")
    @NotBlank(message = "线路总长度（km）不能为空", groups = {update.class, save.class})
    @Size(min = 1, max = 64, message = "线路总长度（km）长度范围 1-64", groups = {update.class, save.class})
    private String lineTotalLength;

    @NotNull(message = "投运时间不能为空", groups = {update.class, save.class})
    @ApiModelProperty(value = "投运日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date putDate;

    private String sortOrder;

    @ApiModelProperty("备注")
    private String remark;

    public interface save {}

    public interface update {}

}
