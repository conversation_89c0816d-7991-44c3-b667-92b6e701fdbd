package com.allcore.main.code.source.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @description:查询设备组台账详细信息(杆塔、风机、区域号)
 * @author: wp
 * @date: 2022/4/25
 */
@Data
@ApiModel(value = "AppLedgerDeviceDetailVO对象", description = "设备组台账详细信息")
public class AppLedgerDetailVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备组guid
     */
    @ApiModelProperty(value = "设备组guid")
    private String groupGuid;

    /**
     * 设备组名
     */
    @ApiModelProperty(value = "设备详细信息")
    private String groupName;

    /**
     * 分组类型
     * 杆塔 风机 光伏等
     * 其中：
     * 0：杆塔分组
     * 1：风机分组
     * 2：光伏分组
     * 3：网格分组(多种设备构成的分组，分组内可同时拥有多个设备)
     */
    @ApiModelProperty(value = "分组类型")
    public Integer groupType;

    /**
     * 分组内的设备 guid集合 全部数据
     */
    @ApiModelProperty(value = "设备guid集合")
    private List<String> deviceNumbers;
}
