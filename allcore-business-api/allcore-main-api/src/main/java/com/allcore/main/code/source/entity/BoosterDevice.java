package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.Size;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2025/5/21 16:04
 **/
@Data
@TableName("main_booster_device")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "boosterDevice对象", description = "boosterDevice对象")
public class BoosterDevice extends ZxhcEntity {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "升压站id,就是deptId")
    private String boosterStationId;

    @ApiModelProperty(value = "区域id")
    private String regionId;

    @ApiModelProperty(value = "间隔id")
    private String intervalId;

    @ApiModelProperty(value = "设备名称")
    @Size(min = 1, max = 30, message = "设备名称长度范围 1-30")
    private String deviceName;

    @ApiModelProperty(value = "设备类型")
    @Size(min = 1, max = 30, message = "设备类型长度范围 1-30")
    private String deviceType;

    @ApiModelProperty(value = "设备型号")
    @Size(min = 1, max = 30, message = "设备型号长度范围 1-30")
    private String deviceModel;

    @ApiModelProperty(value = "生产厂家")
    @Size(min = 1, max = 30, message = "生产厂家长度范围 1-30")
    private String manufacturer;

    @ApiModelProperty(value="设备性质")
    @Size(min=1,max=30,message = "设备性质长度范围 1-30")
    private String deviceNature;

    @ApiModelProperty(value = "投入日期")
    @JsonFormat(
            pattern = "yyyy-MM-dd"
    )
    @DateTimeFormat(
            pattern = "yyyy-MM-dd"
    )
    private Date inputDate;


}
