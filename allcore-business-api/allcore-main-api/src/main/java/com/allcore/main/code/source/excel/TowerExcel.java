package com.allcore.main.code.source.excel;

import javax.validation.constraints.*;

import com.allcore.main.code.common.dto.BaseExcelDTO;

import cn.afterturn.easypoi.excel.annotation.Excel;
import com.allcore.main.code.source.entity.Tower;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TowerExcel extends BaseExcelDTO {

    @Excel(name = "*杆塔名称")
    @NotBlank(message = "不能为空")
    @Size(min = 1, max = 255, message = "长度范围 1-255")
    private String towerName;

    @Excel(name = "杆塔编码")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String towerNo;

    @Excel(name = "*纬度")
    @NotNull(message = "不能为空")
    @DecimalMin(value = "-90", message = "最小值为 -90")
    @DecimalMax(value = "90", message = "最大值90")
    private String latitude;

    @Excel(name = "*经度")
    @NotNull(message = "不能为空")
    @DecimalMin(value = "-180", message = "最小值 -180")
    @DecimalMax(value = "180", message = "最大值 180")
    @ApiModelProperty(value = "经度")
    private String longitude;

    @Excel(name = "*高程")
    @NotNull(message = "不能为空")
    private String elevation;

    @Excel(name = "*PMS编码")
    @NotNull(message = "不能为空")
    @Size(min = 1, max = 255, message = "长度范围 1-255")
    private String pmsCode;

    @Excel(name = "详细地址")
    @Size(min = 1, max = 100, message = "长度范围 1-100")
    private String towerPosition;

    @Excel(name = "杆塔型号")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String towerModel;

    @Excel(name = "*档距")
    @NotNull(message = "不能为空")
    private String span;

    @Excel(name = "是否终端")
    private String isTerminal;

    @Excel(name = "代表档距")
    private String rulingspSpan;

    @Excel(name = "是否在适航区")
    private Integer isAirworthy;

    @Excel(name = "地质")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String geology;

    @Excel(name = "*杆塔性质")
    @NotNull(message = "不能为空")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String towerNature;

    @Excel(name = "*同杆回数")
    @NotNull(message = "不能为空")
    private Integer loopNumber;

    @Excel(name = "耐张长度")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String strainLength;

    @Excel(name = "地区特征")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String regionalType;

    @Excel(name = "呼称高")
    private String normalHeight;

    @Excel(name = "杆塔高")
    private String towerHeight;

    @Excel(name = "杆头高")
    private String towerHeadHeight;

    @Excel(name = "是否同杆")
    private Integer isSamePole;

    @Excel(name = "海拔高度")
    private String altitude;

    @Excel(name = "是否转角")
    private Integer isCorner;

    @Excel(name = "地形")
    @Size(min = 1, max = 32, message = "长度范围 1-32")
    private String topography;

    @Excel(name = "备注")
    @Size(min = 1, max = 255, message = "长度范围 1-255")
    private String remark;

    @Excel(name = "排序")
    private Integer sort;

    private String deptCode;

}
