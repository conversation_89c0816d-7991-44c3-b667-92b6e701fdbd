package com.allcore.main.code.source.entity;

import java.util.Date;

import javax.validation.constraints.*;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 实体类
 *
 * <AUTHOR>
 * @since 2023-10-12
 */
@Data
@TableName("main_pv_string")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "PvString对象", description = "PvString对象")
public class PvString extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    private String sortOrder;

    @ApiModelProperty(value = "区域id")
    @NotBlank(message = "区域id不能为空", groups = {update.class, save.class})
    private String pvAreaId;

    @ApiModelProperty(value = "设备名称")
    @NotBlank(message = "设备名称不能为空", groups = {update.class, save.class})
    @Size(min = 1, max = 64, message = "设备名称长度范围 1-64", groups = {update.class, save.class})
    private String deviceName;

    @ApiModelProperty(value = "组串编号")
    @NotBlank(message = "组串编号不能为空", groups = {update.class, save.class})
    @Size(min = 2, max = 64, message = "组串编号范围 2-64", groups = {update.class, save.class})
    private String stringNumber;

    @ApiModelProperty(value = "经度")
    @NotNull(message = "经度不能为空", groups = {update.class, save.class})
    @DecimalMin(value = "-180", message = "经度最小值 -180", groups = {update.class, save.class})
    @DecimalMax(value = "180", message = "经度最大值 180", groups = {update.class, save.class})
    private String longitude;

    @ApiModelProperty(value = "纬度")
    @NotNull(message = "纬度不能为空", groups = {update.class, save.class})
    @DecimalMin(value = "-90", message = "纬度最小值为 -90", groups = {update.class, save.class})
    @DecimalMax(value = "90", message = "纬度最大值90", groups = {update.class, save.class})
    private String latitude;

    @ApiModelProperty(value = "高程")
    @NotBlank(message = "高程不能为空", groups = {update.class, save.class})
    private String elevation;

    @ApiModelProperty(value = "坐标")
    @NotBlank(message = "坐标不能为空", groups = {update.class, save.class})
    private String coordinates;

    @NotNull(message = "投运时间不能为空", groups = {PvString.update.class, PvString.save.class})
    @ApiModelProperty(value = "投运日期")
    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    private Date putDate;


    public interface save {}

    public interface update {}
}
