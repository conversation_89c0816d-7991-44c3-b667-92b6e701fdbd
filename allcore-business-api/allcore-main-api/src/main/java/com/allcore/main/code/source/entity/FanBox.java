package com.allcore.main.code.source.entity;

import com.allcore.common.base.ZxhcEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.*;
import java.util.Date;

/**
 * @program: bl
 * @description: 风机箱变实体类
 * @author: fanxiang
 * @create: 2025-06-11 15:30
 **/

@TableName("main_fan_box")
@EqualsAndHashCode(callSuper = true)
@Data
@ApiModel(value = "风机箱变对象", description = "风机箱变对象")
public class FanBox extends ZxhcEntity {
    private static final long serialVersionUID = 1L;


    @NotBlank(message = "箱变型号不能为空")
    @Size(min = 1, max = 64, message = "箱变型号长度范围 1-64")
    @ApiModelProperty("箱变型号")
    private String fanBoxModel;

    @NotBlank(message = "箱变编号不能为空")
    @Size(min = 1, max = 64, message = "箱变编号长度范围 1-64")
    @ApiModelProperty("箱变编号")
    private String fanBoxNumber;

    @ApiModelProperty("设备名称")
    @Size(min=1,max=30,message = "设备名称长度范围 1-30")
    private String deviceName;

    @DecimalMin(value = "-180", message = "经度最小值 -180")
    @DecimalMax(value = "180", message = "经度最大值 180")
    @ApiModelProperty("经度")
    private String longitude;

    @DecimalMin(value = "-90", message = "纬度最小值为 -90")
    @DecimalMax(value = "90", message = "纬度最大值90")
    @ApiModelProperty("纬度")
    private String latitude;

    @ApiModelProperty("额定功率(KVA)")
    private String ratedPower;

    @JsonFormat(locale = "zh", timezone = "GMT+8", pattern = "yyyy-MM-dd")
    @ApiModelProperty("投入使用日期")
    @NotNull(message = "投入使用时间不能为空")
    private Date usageTime;

    @ApiModelProperty("关联风机id")
    private String fanId;

    @ApiModelProperty("关联风机名称")
    private String fanName;

}
