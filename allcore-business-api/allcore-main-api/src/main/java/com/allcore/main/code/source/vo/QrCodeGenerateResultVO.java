package com.allcore.main.code.source.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: bl
 * @description: 二维码生成结果VO
 * @author: fanxiang
 * @create: 2025-06-20 14:05
 **/

@Data
@ApiModel(value="QrCodeGenerateResultVO对象",description = "二维码生成结果VO")
public class QrCodeGenerateResultVO implements Serializable {

    private static final long serialVersionUID = 12315484131541L;


    /**
     * 生成是否成功
     */
    @ApiModelProperty(value = "生成是否成功")
    private Boolean success;

    /**
     * 成功生成的数量
     */
    @ApiModelProperty(value = "成功生成的数量")
    private Integer successCount;

    /**
     * 失败的数量
     */
    @ApiModelProperty(value = "失败的数量")
    private Integer failureCount;

    /**
     * 结果消息
     */
    @ApiModelProperty(value = "结果消息")
    private String message;

    /**
     * 失败的设备信息
     */
    @ApiModelProperty(value = "失败的设备信息")
    private List<FailureDetail> failureDetails;

    /**
     * 二维码链接信息列表（新增：用于返回生成的链接信息）
     */
    @ApiModelProperty(value = "二维码链接信息列表")
    private List<QrCodeLinkInfo> linkInfoList;

    /**
     * 失败设备详情
     */
    @Data
    @ApiModel(value = "FailureDetail", description = "失败设备详情")
    public static class FailureDetail implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 设备名称
         */
        @ApiModelProperty(value = "设备名称")
        private String deviceName;

        /**
         * 设备ID
         */
        @ApiModelProperty(value = "设备ID")
        private String deviceId;

        /**
         * 错误信息
         */
        @ApiModelProperty(value = "错误信息")
        private String errorMessage;
    }

    /**
     * 二维码链接信息
     */
    @Data
    @ApiModel(value = "QrCodeLinkInfo", description = "二维码链接信息")
    public static class QrCodeLinkInfo implements Serializable {

        private static final long serialVersionUID = 1L;

        /**
         * 设备ID
         */
        @ApiModelProperty(value = "设备ID")
        private String deviceId;

        /**
         * 设备名称
         */
        @ApiModelProperty(value = "设备名称")
        private String deviceName;

        /**
         * 设备类型
         */
        @ApiModelProperty(value = "设备类型")
        private String deviceType;

        /**
         * 二维码内容（URL链接）
         */
        @ApiModelProperty(value = "二维码内容（URL链接）")
        private String qrCodeContentURL;

        /**
         * 二维码文件名（建议文件名）
         */
        @ApiModelProperty(value = "二维码文件名（建议文件名）")
        private String qrCodeFileName;
    }

    /**
     * 创建成功结果
     *
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param message      消息
     * @return 结果对象
     */
    public static QrCodeGenerateResultVO success(Integer successCount, Integer failureCount, String message) {
        QrCodeGenerateResultVO result = new QrCodeGenerateResultVO();
        result.setSuccess(true);
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setMessage(message);
        return result;
    }

    /**
     * 创建失败结果
     *
     * @param message 错误消息
     * @return 结果对象
     */
    public static QrCodeGenerateResultVO failure(String message) {
        QrCodeGenerateResultVO result = new QrCodeGenerateResultVO();
        result.setSuccess(false);
        result.setSuccessCount(0);
        result.setFailureCount(0);
        result.setMessage(message);
        return result;
    }

    /**
     * 创建部分成功结果
     *
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param message      消息
     * @return 结果对象
     */
    public static QrCodeGenerateResultVO partial(Integer successCount, Integer failureCount, String message) {
        QrCodeGenerateResultVO result = new QrCodeGenerateResultVO();
        result.setSuccess(successCount > 0);
        result.setSuccessCount(successCount);
        result.setFailureCount(failureCount);
        result.setMessage(message);
        return result;
    }

}
