package com.allcore.main.code.source.dto;

import com.allcore.main.code.source.entity.CollectorTower;
import com.allcore.main.code.source.entity.Tower;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-17
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class CollectorTowerSaveDTO extends CollectorTower {
	private static final long serialVersionUID = 1L;

}
