package com.allcore.main.code.source.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@ApiModel(value = "", description = "")
public class DeviceWithParentVO {
    private static final long serialVersionUID = 1L;

    /**
     * 设备id
     */
    @ApiModelProperty(value = "设备id")
    private String deviceId;
    /**
     * 父设备id
     */
    @ApiModelProperty(value = "父设备id")
    private String parentDeviceId;

    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    private String parentDeviceName;

    private String pvAreaId;
    private String pvAreaName;
    private String defectDescription;
    private String bigFileGuid;
    private String bigFilePath;
    private String lightFileGuid;
    private String lightFilePath;
    private String temperature;
    private int num;

    private String coordinates;
    private String inspectionReportPic;
    private String inspectionReportPicPath;
    private String manufacturer;
    private String model;
    private String bladeManufacturer;
    private String defectLevel;
    private String fileGuid;
    private String filePath;
    private String fileName;
    private String partOneName;
    private String partTwoName;

    /**
     * 原图
     */
    private String picFileGuid;
    /**
     * 是否可见光
     */
    private String isZ;
    /**
     * 是否算法识别
     */
    private String isAlgorithm;




}
