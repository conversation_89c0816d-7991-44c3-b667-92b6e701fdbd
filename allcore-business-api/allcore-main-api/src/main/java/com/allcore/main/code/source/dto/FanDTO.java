package com.allcore.main.code.source.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
@Data
public class FanDTO {

    private static final long serialVersionUID = 1L;
    /**
     * 机构code
     */
    @ApiModelProperty(value = "机构code")
    private String deptCode;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 风机号
     */
    @ApiModelProperty(value = "风机号")
    private String fanNum;
    /**
     * 型号
     */
    @ApiModelProperty(value = "型号")
    private String model;

}
