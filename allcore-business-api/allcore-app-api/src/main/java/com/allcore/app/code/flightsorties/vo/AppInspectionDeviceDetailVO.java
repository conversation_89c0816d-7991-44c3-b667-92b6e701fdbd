package com.allcore.app.code.flightsorties.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectionDeviceDetailVO对象", description = "InspectionDeviceDetailVO对象")
public class AppInspectionDeviceDetailVO extends AppInspectionDeviceDetail {
    private static final long serialVersionUID = 1L;
    private String height;
    /**
     * 设备名称
     */
    @ApiModelProperty(value = "设备名称")
    private String deviceName;
    /**
     * 航迹文件名称
     */
    @ApiModelProperty(value = "航迹文件名称")
    private String routeFileId;

    @ApiModelProperty(value = "纬度")
    private String latitude;

    @ApiModelProperty(value = "经度")
    private String longitude;
    private String coordinates;


}
