package com.allcore.app.code.flightsorties.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:飞行任务信息数据
 * @author: wp
 * @date: 2022/4/25
 */
@Data
@ApiModel(value = "AppTaskStateParamDTO实体类",description = "飞行任务信息数据")
public class AppTaskStateParamDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 飞行架次guid
     * 每次起飞时，更新该字段
     */
    @ApiModelProperty(value = "飞行架次guid")
    private String flyGuid;

    /**
     * 起飞时间 毫秒
     * 最近的一次起飞时间
     */
    @ApiModelProperty(value = "起飞时间",notes = "最近的一次起飞时间，单位毫秒")
    private Long launchTime;

    /**
     * 降落时间 毫秒
     * 如果当前飞机正在飞行，则该字段为0
     * 如果当前飞机着陆后，该字段为着陆时的时间
     * 如果当前飞机刚打开，未起飞过，该字段为0
     */
    @ApiModelProperty(value = "降落时间",notes = "正在飞行，则该字段为0;飞机着陆后，该字段为着陆时的时间;机刚打开，未起飞过，该字段为0")
    private Long landTime;

    /**
     * 工单guid 如果当前正在执行工单，则为工单id  否则为空
     */
    @ApiModelProperty(value = "工单guid")
    private String inspectGuid;

    /**
     * 设备识别码  设备信息  如果有的话。否则为空
     * 可作为 杆塔识别码  如果是新能源  可以作为 风机识别码
     */
    @ApiModelProperty(value = "设备guid",notes = "杆塔guid、风机guid、光伏guid")
    private String deviceGuid;

    /**
     * 当前巡检的设备类型
     *  杆塔 光伏 风机等
     */
    @ApiModelProperty(value = "设备类型",notes = "0 杆塔; 1风机 2光伏；")
    private Integer deviceType;

    /**
     * 线路识别码
     */
    @ApiModelProperty(value = "设备组guid",notes = "如线路guid")
    private String groupGuid;

    /**
     * 当前航点已执行数 如果有的话
     * 支持模式参考{@link #missionType}
     * 注意当前支持模式为：
     * 精细巡检，精细巡检红外，通道巡检，通道巡检红外
     * 否则，该字段值无效
     */
    @ApiModelProperty(value = "已执行航点总数")
    private Integer missionCount;

    /**
     * 当前执行的航线任务 航点总数
     * 支持模式参考{@link #missionType}
     * 注意当前支持模式为：
     * 精细巡检，精细巡检红外，通道巡检，通道巡检红外
     * 否则，该字段值无效
     */
    @ApiModelProperty(value = "航点总数")
    private Integer totalMissionCount;

    /**
     * 当前任务执行类型：
     * 0：精细巡检
     * 1、精细巡检红外
     * 2、采集航线
     * 3、通道巡检
     * 4、通道巡检红外
     * 5、手动巡检
     * 6、手动巡检红外
     */
    @ApiModelProperty(value = "任务类型")
    private Integer missionType;

    /**
     * 画面实时最高温度
     */
    @ApiModelProperty(value = "画面实时最高温度")
    private Double highTemperature;

    /**
     * 画面实时最高温度 坐标(像素坐标)
     */
    @ApiModelProperty(value = "画面实时最高温度X坐标")
    private Double hPositionX;

    /**
     * 画面实时最高温度 坐标(像素坐标)
     */
    @ApiModelProperty(value = "画面实时最高温度Y坐标")
    private Double hPositionY;

    /**
     * 画面实时最低温度
     */
    @ApiModelProperty(value = "画面实时最低温度")
    private Double lowTemperature;

    /**
     * 画面实时最低温度 坐标(像素坐标)
     */
    @ApiModelProperty(value = "画面实时最低温度X坐标")
    private Double lPositionX;

    /**
     * 画面实时最低温度 坐标(像素坐标)
     */
    @ApiModelProperty(value = "画面实时最低温度Y坐标")
    private Double lPositionY;

    /**
     * 当前任务已执行了的拍照点数量
     */
    public int takePhotoIndex;
    /**
     * 当前任务的拍照点数量总数
     */
    public int takePhotoCount;
    /**
     * 当前飞机正在飞往的航点的类型
     *     1：拍照点，2：安全点，0：无(未起飞或任务执行完毕)
     */
    public int nowPointType;

}
