package com.allcore.app.code.flightsorties.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * @description:飞机电池信息
 * @author: wp
 * @date: 2022/4/25
 */
@Data
@ApiModel(value = "AppBatteryStateParam实体类",description = "飞机电池信息")
public class AppBatteryStateParamDTO implements Serializable{

    private static final long serialVersionUID = 1L;

    /**
     * 电池唯一识别码
     */
    @ApiModelProperty(value = "电池唯一识别码")
    private String serialNumber;

    /**
     * 电压 mv
     */
    @ApiModelProperty(value = "电压")
    private Integer voltage;

    /**
     * 电流 负数放电 ma
     */
    @ApiModelProperty(value = "电流",notes = "正数为充电，负数为放电")
    private Integer current;

    /**
     *剩余电量百分比
     */
    @ApiModelProperty(value = "电池剩余电量百分比")
    private Integer chargeRemainingInPercent;

    /**
     * 最大电量
     */
    @ApiModelProperty(value = "电池实际最大电量")
    private Integer fullChargeCapacity;

    /**
     * 充电次数
     */
    @ApiModelProperty(value = "电池充电次数")
    private Integer numberOfDischarges;

    /**
     * 完美最大电量  设计电量   ma
     */
    @ApiModelProperty(value = "电池设计最大电量")
    private Integer designCapacity;

    /**
     * 寿命百分比
     */
    @ApiModelProperty(value = "电池寿命",notes = "在不受支持的产品中，该值始终为 0")
    private Integer lifetimeRemaining;

    /**
     * 电池温度
     */
    @ApiModelProperty(value = "电池温度")
    private Double temperature;
}
