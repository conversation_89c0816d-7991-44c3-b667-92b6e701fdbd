package com.allcore.app.code.flightsorties.dto;

import javax.validation.constraints.NotNull;

import com.allcore.app.code.flightsorties.entity.FlyFlightSorties;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.Date;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlyFlightSortiesDTO extends FlyFlightSorties {
    private static final long serialVersionUID = 1L;
    /**
     * 飞行时长（分钟）
     */
    @NotNull(message = "飞行时长不能为空")
    @ApiModelProperty(value = "飞行时长（分钟）")
    private Double flyMinutes;

    /**
     * 开始时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /**
     * 结束时间
     */
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
