package com.allcore.app.code.flightsorties.vo;

import com.allcore.common.utils.NullToEmptyStringSerializer;
import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "InspectionTaskVO对象", description = "InspectionTaskVO对象")
public class AppInspectionTaskVO extends AppInspectionTask {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "单位名称Zh")
    private String deptCodeZh;

    @ApiModelProperty(value = "巡检方式Zh")
    private String inspectionMethodZh;
    @ApiModelProperty(value = "巡检区块图Url")
    private String inspectionAreaGuidUrl;
    @ApiModelProperty(value = "状态Zh")
    private String taskStatusZh;

    @ApiModelProperty(value = "计划名称")
    private String planName;

    @ApiModelProperty(value = "巡检类型Zh")
    private String inspectionTypeZh;
    /**
     * 父级单位名称
     */
    @ApiModelProperty("父级单位名称(区域公司)")
    private String parentDeptName;
    /**
     * 设备名称集
     */
    @ApiModelProperty("设备名称集")
    private String deviceNames;


    @ApiModelProperty(value = "设备数量")
    private String deviceNum;
    /**
     * 无人机操作员集
     */
    @ApiModelProperty("无人机操作员集")
    private String uavUsers;
    /**
     * 巡检时间
     */
    @ApiModelProperty("巡检时间")
    private String startToEndTime;
    /**
     * 设备类型
     */
    @ApiModelProperty(value = "设备类型")
    private String deviceType;
    /**
     * 图片数量
     */
    @ApiModelProperty(value = "图片数量")
    private String picNum;
    /**
     * 飞行时长（分钟）
     */
    @ApiModelProperty(value = "飞行时长（分钟）")
    private Double flyMinutes;
    /**
     * 架次飞行的总距离
     */
    @ApiModelProperty(value = "架次飞行的总距离")
    private Double flyDistance;
    /**
     * 详情列表
     */
    @ApiModelProperty(value = "详情列表")
    private List<AppInspectionDeviceDetailVO> deviceDetailList;



    private String recognitionTaskId;
    private String recognitionTaskStatus;


    @ApiModelProperty(value = "地形夸张")
    private String terrainExaggeration;

    @ApiModelProperty("责任人名称")
    @JsonInclude(JsonInclude.Include.ALWAYS) // 确保 null 字段也参与序列化
    @JsonSerialize(nullsUsing = NullToEmptyStringSerializer.class)
    private String responsibleUserName;

    @ApiModelProperty("是否为智能处理(0否 1是)")
    private Integer smartProcess;

    @ApiModelProperty("智能处理内容")
    private String processContent;


}
