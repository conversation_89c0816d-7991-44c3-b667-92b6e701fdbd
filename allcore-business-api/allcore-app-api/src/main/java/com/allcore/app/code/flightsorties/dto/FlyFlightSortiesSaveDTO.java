package com.allcore.app.code.flightsorties.dto;

import javax.validation.constraints.NotNull;

import com.allcore.app.code.flightsorties.entity.FlyFlightSorties;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlyFlightSortiesSaveDTO extends FlyFlightSorties {
    private static final long serialVersionUID = 1L;
    /**
     * 飞行时长（分钟）
     */
    @NotNull(message = "飞行时长不能为空")
    @ApiModelProperty(value = "飞行时长（分钟）")
    private Double flyMinutes;

}
