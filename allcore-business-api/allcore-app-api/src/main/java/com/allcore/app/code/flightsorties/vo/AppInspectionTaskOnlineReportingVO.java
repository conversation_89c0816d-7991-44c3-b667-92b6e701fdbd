package com.allcore.app.code.flightsorties.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-11-26
 */
@Data
public class AppInspectionTaskOnlineReportingVO implements Serializable {
	private static final long serialVersionUID = 1L;


	@ApiModelProperty(value = "缺陷信息列表")
	private List<AppDefectTypeNameNumVO> defectInfo;

	@ApiModelProperty(value = "全部缺陷数量")
	private String allDefectNum;

}
