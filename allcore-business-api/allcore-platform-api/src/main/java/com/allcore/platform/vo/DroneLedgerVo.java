package com.allcore.platform.vo;

import com.baomidou.mybatisplus.annotation.SqlCondition;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.util.Date;

@Data
public class DroneLedgerVo implements Serializable {

    /**
     * 运维单位
     */
    @ApiModelProperty(value = "运维单位")
    @TableField(value = "operationUnit")
    private String operationUnit;

    /**
     * 运维中心
     */
    @ApiModelProperty(value = "运维中心")
    @TableField(value = "operationCenter")
    private String operationCenter;

    /**
     * 厂家名称
     */
    @ApiModelProperty(value = "厂家名称")
    @TableField(value = "manufactorName")
    private String manufactorName;

    /**
     * 品牌名称
     */
    @ApiModelProperty(value = "品牌名称")
    @TableField(value = "brandName", condition = SqlCondition.LIKE)
    private String brandName;

    /**
     * 型号
     */
    @ApiModelProperty(value = "型号")
    @TableField(value = "nestModel")
    private String nestModel;

    /**
     * 责任人
     */
    @ApiModelProperty(value = "责任人")
    @TableField(value = "personLiable")
    private String personLiable;

    /**
     * 经度
     */
    @ApiModelProperty(value = "经度")
    @TableField(value = "longitude")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度")
    @TableField(value = "latitude")
    private String latitude;

    /**
     * 起飞经度
     */
    @ApiModelProperty(value = "起飞经度")
    @TableField(value = "takeOffLon")
    private String takeOffLon;

    /**
     * 起飞纬度
     */
    @ApiModelProperty(value = "起飞纬度")
    @TableField(value = "takeOffLat")
    private String takeOffLat;

    /**
     * 所属单位
     */
    @ApiModelProperty(value = "所属单位")
    @TableField(value = "operationTeam", condition = SqlCondition.LIKE_RIGHT)
    private String operationTeam;

    /**
     * 投运日期
     */
    @ApiModelProperty(value = "投运日期")
    @TableField(value = "commisDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date commisDate;

    /**
     * 生产日期
     */
    @ApiModelProperty(value = "生产日期")
    @TableField(value = "productDate")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    @DateTimeFormat(pattern = "yyyy-MM-dd")
    private Date productDate;

    /**
     * 机巢类型
     */
    @ApiModelProperty(value = "机巢类型")
    @TableField(value = "nestType")
    private String nestType;

    /**
     * 机巢SN码
     */
    @ApiModelProperty(value = "机巢SN码")
    @TableField(value = "nestSNCode")
    private String nestSNCode;

    /**
     * 机巢名称
     */
    @ApiModelProperty(value = "机巢名称")
    @TableField(value = "nestName", condition = SqlCondition.LIKE)
    private String nestName;

    /**
     * 无人机供电方式
     */
    @ApiModelProperty(value = "无人机供电方式")
    @TableField(value = "uavPowerMode")
    private String uavPowerMode;

    /**
     * 无人机飞控编号
     */
    @ApiModelProperty(value = "无人机飞控编号")
    @TableField(value = "uavControlNum")
    private String uavControlNum;

    /**
     * 机巢供电方式
     */
    @ApiModelProperty(value = "机巢供电方式")
    @TableField(value = "nestPowerMode")
    private String nestPowerMode;

    /**
     * 巡检半径
     */
    @ApiModelProperty(value = "巡检半径")
    @TableField(value = "patrolRadius")
    private String patrolRadius;

    /**
     * 最高点高度
     */
    @ApiModelProperty(value = "最高点高度")
    @TableField(value = "highestHeight")
    private String highestHeight;

    /**
     * 海拔高度
     */
    @ApiModelProperty(value = "海拔高度")
    @TableField(value = "altitude")
    private String altitude;

    /**
     * 机巢状态
     */
    @ApiModelProperty(value = "机巢状态")
    @TableField(value = "nestState")
    private String nestState;

    /**
     * 起飞高度
     */
    @ApiModelProperty(value = "起飞高度")
    @TableField(value = "takeOffHeight")
    private String takeOffHeight;

    /**
     * 通信方式
     */
    @ApiModelProperty(value = "通信方式")
    @TableField(value = "communicationMode")
    private String communicationMode;

    /**
     * 舱门开合类型
     */
    @ApiModelProperty(value = "舱门开合类型")
    @TableField(value = "doorOpenType")
    private String doorOpenType;

    /**
     * IP地址
     */
    @ApiModelProperty(value = "IP地址")
    @TableField(value = "ipAddress")
    private String ipAddress;

    /**
     * 端口号
     */
    @ApiModelProperty(value = "端口号")
    @TableField(value = "portNum")
    private String portNum;

    /**
     * SIM卡号
     */
    @ApiModelProperty(value = "SIM卡号")
    @TableField(value = "simCode")
    private String simCode;

}

