package com.allcore.platform.vo;

import lombok.Data;

import java.util.List;

@Data
public class PartrolRecordVo {
    //objId
    private String objId;

    private String patrolRange;

    private String patrolStaffName;
    // 工单 ID
    private String workOrder;
    // 计划 ID
    private String patrolPlanId;
    // 站线名称
    private String containerName;
    // 站线 ID
    private String container;
    // 站线类型
    private String containerType;
    // 巡视结果
    private String patrolResult;
    // 是否巡视完成
    private String isCompleted;
    // 巡视未完成原因
    private String noFinishReason;
    // 巡视人员
    private List<PatrolPerson> patrolPerson;
    // 实际开始时间
    private String startedDateTime;
    // 实际结束时间
    private String completedDateTime;
    // 工作班组 ID
    private String workCrewId;
    // 工作班组名称
    private String workCrewName;
    // 所属地市 ID
    private String cityOrgId;
    // 所属地市名称
    private String cityOrgName;
    // 维护班组 ID
    private String maintcrewId;
    // 维护班组名称
    private String maintcrewName;
    // 运维单位 ID
    private String maintainerId;
    // 运维单位名称
    private String maintainerName;
    // 创建人 ID
    private String createrId;
    // 创建人名称
    private String createrName;
    // 备注
    private String remark;
    // 天气
    private String weather;
    // 气温
    private String temperature;
    // 是否 app 登记
    private String isApp;
    // 附件
    private List<RecordAttachment> attachments;
    // 巡视类型
    private String patrolType;
    // 巡视方式
    private String patrolMode;
    // 发现缺陷数
    private String defectCount;
    // 电压等级
    private String containerVoltageLevel;
    // 巡视内容
    private String patrolNorm;
    // 扩展字段
    private String extend;
    // 巡视长度（km）
    private String patrolLen;
    // 巡视总杆塔数
    private String towerCount;
    // 应用来源
    private String systemSource;
    // 数据来源
    private String dataSource;
    // 关联业务id
    private String relateBusiId;
    // 应用来源id
    private String systemSourceId;
    // 关联业务类型
    private String relateBusiType;
    // 站线名称
    private String lineName;
    // 站线id
    private String lineId;
    // 修改人id
    private String editorId;
    // 修改人
    private String editorName;
}
