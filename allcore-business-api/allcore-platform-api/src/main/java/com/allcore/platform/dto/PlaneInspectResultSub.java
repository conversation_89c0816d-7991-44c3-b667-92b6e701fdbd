package com.allcore.platform.dto;
import lombok.Data;
import org.springframework.web.multipart.MultipartFile;
import java.util.List;

/**
 * 无人机巡视结果子集
 * <AUTHOR>
 * @date 2023/3/23 15:38
 */
@Data
public class PlaneInspectResultSub {

	/**
	 * 资源id
	 */
	private String psrId;

	/**
	 * 设备类型
	 */
	private String equipType;

	/**
	 * 飞手名称
	 */
	private String patrolTime;

	/**
	 * 巡视照片 base64地址
	 */
	private List<FileDTO> picture;

	/**
	 * 飞手id
	 */
	private String patrolStaffId;

	/**
	 * 飞手名称
	 */
	private String patrolStaffName;

	/**
	 * 签到经度
	 */
	private String signLongitude;

	/**
	 * 签到纬度
	 */
	private String signLatitude;
}
