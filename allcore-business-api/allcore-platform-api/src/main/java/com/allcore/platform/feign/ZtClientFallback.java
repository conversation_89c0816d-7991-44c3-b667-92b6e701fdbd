package com.allcore.platform.feign;

import com.allcore.platform.dto.*;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * Feign失败配置
 *
 * <AUTHOR>
 */
@Component
public class ZtClientFallback implements ZtClient {


	@Override
	public void GetEquipByType(String id) {

	}

	@Override
	public void GetEquipByCode(String id) {

	}

	@Override
	public void SmartDeviceQuery(String id) {

	}

	@Override
	public void findSmartDevice(String id) {

	}

	@Override
	public void queryLineList() {

	}

	@Override
	public void QueryByLineContainer(String id) {

	}

	@Override
	public void workPlanCreate(WorkPlanRequest workPlanRequest) {

	}

	@Override
	public void workPlanChange(WorkPlanRequest workPlanRequest) {

	}

	@Override
	public void workPlanList(String page, String perPage) {

	}

	@Override
	public void workPlanQuery(String msg) {

	}

	@Override
	public void workorderCreate(WorkOrderRequest workOrderRequest) {

	}

	@Override
	public void workorderChange(WorkOrderRequest workOrderRequest) {

	}

	@Override
	public void workorderQuerycxByPmsCode(String unitpmscode, Integer page, Integer limit) {

	}

	@Override
	public void workorderQuerycxByGuid(String inspectGuidListStr) {

	}

	@Override
	public void workRecordCreate(WorkRecordRequest workRecordRequest) {

	}

	@Override
	public void workRecordUpdate(WorkRecordRequest workRecordRequest) {

	}

	@Override
	public void workRecordList(String msg) {

	}

	@Override
	public void workRecordQuery(String msg) {

	}

	@Override
	public void syncDefectData(List<DefectPictureDetailDTO> detailDTOList) {

	}

	@Override
	public void getDefectDataFromZT(String unitGUID) {

	}

	@Override
	public void getSelectDefectOneDataByDetail(String detailId) {

	}
}
