package com.allcore.platform.cqbean;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
* 中台巡视计划实体类
*/
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PatrolPlanVo extends BaseEntity {
    //巡视计划ID 1
    private String mRID;
    //变电站名称 0
    private String substationName;
    //所选范围 0
    private String selectedScope;
    //巡视类型 0 建工单的时候才会有巡视类型
    private String patrolType;
    //巡视班组名称 0
    private String patrolTeamName;
    //巡视周期 0
    private String patrolCycle;
    //周期单位 0
    private String patrolCycleUnit;
    //巡视内容 0
    private String inspectionContent;
    //上次巡视完成时间 0
    private Date lastVisitTime;
    //提前报警天数 0
    private String numberOfEarlyWarningDays;
    //巡视线路图 0
    private String patrolRoute;
    //运维单位 0
    private String maintenaner;
    //专业分类 1
    private String professionalClass;
    //电站电压等级名称 0
    private String stationVoltage;
    //电站ID 0
    private String substation;
    //间隔单元 0
    private String spacerUnit;
    //间隔单元名称 0
    private String spacerUnitName;
    //所选范围ID(多个设备id) 0
    private String patrolEquipment;
    //所选范围名称(多个设备) 0
    private String patrolEquipmentName;
    //设备类型 0
    private String equipmentType;
    //巡视班组ID 0
    private String patrolTeam;
    //记录巡视到期时间 0
    private Date recordExpirationTime;
    //状态评价结果ID 0
    private String theResultOfStateEvaluationID;
    //重点巡视原因 0
    private String keyInspectionReasons;
    //编制人ID 0
    private String compilerId;
    //编制人名称 0
    private String compactorName;
    //编制时间 0
    private Date compilationTime;
    //备注 0
    private String remarks;
    //数据来源 0
    private String dataSource;
    //记录状态 0
    private String recordState;
    //应用领域 0
    private String applicationArea;
    //编制单位ID 0
    private String compileUnitId;
    //编制单位名称 0
    private String compileUnitName;
    //所属市局ID 0
    private String cityBureauId;
    //所属市局名称 0
    private String cityBureauName;
    //巡视分类 0
    private String patrolClassification;
    //计划开始时间 0
    private Date planStartTime;
    //计划结束时间 0
    private Date inspectionExpirationTime;
    //计划创建时间 0
    private Date createTime;
    //计划更新时间 0
    private Date updateTime;
    //计划修改时间 0
    private String modiferName;
    //修改人ID 0
    private String modiferID;

}


