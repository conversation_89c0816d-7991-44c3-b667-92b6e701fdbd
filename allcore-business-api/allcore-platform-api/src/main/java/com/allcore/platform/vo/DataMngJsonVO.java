package com.allcore.platform.vo;

import com.allcore.platform.entity.DataMngJson;
import com.allcore.platform.entity.DataMngJsonPic;
import com.allcore.platform.entity.DataMngJsonReport;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

import java.util.List;

/**
 * 数据处理json表视图实体类
 * 树障属于隐患点
 * <AUTHOR>
 * @since 2022-10-08
 */
@Data
@ApiModel(value = "DataMngJsonVO对象", description = "数据处理json表")
public class DataMngJsonVO {
	private static final long serialVersionUID = 1L;

	/**
	 * json的guid
	 */
	@ApiModelProperty(value = "json的guid")
	private String jsonGuid;
	/**
	 * 原json内容
	 */
	@ApiModelProperty(value = "原json内容")
	private String content;
	/**
	 * 线路id
	 */
	@ApiModelProperty(value = "线路id")
	private String lineId;
	/**
	 * 线路名称
	 */
	@ApiModelProperty(value = "线路名称")
	private String lineName;
	/**
	 * 杆塔范围
	 */
	@ApiModelProperty(value = "杆塔范围")
	private String towerRange;

	/**
	 * 档距段guid
	 */
	@ApiModelProperty(value = "档距段guid")
	private String towerGuidRange;


	/**
	 * 采集时间
	 */
	@ApiModelProperty(value = "采集时间")
	private String cjTime;

	/*************************************隐患点**start********************************/

	/**
	 * 隐患点guid
	 */
	@ApiModelProperty(value = "隐患点guid")
	private String dangerGuid;

	/**
	 * 隐患类型
	 */
	@ApiModelProperty(value = "隐患类型")
	private String dangerType;
	/**
	 * 缺陷类型
	 */
	@ApiModelProperty(value = "缺陷类型")
	private String defectLevel;

	/**
	 * 距小号塔距离
	 */
	@ApiModelProperty(value = "距小号塔距离")
	private Double disToSmallTower;
	/*************************************隐患点**end*********************************/


	/*************************************交跨点**start********************************/

	/**
	 * 交跨点guid
	 */
	@ApiModelProperty(value = "交跨点guid")
	private String crossGuid;

	/**
	 * 交跨类型
	 */
	@ApiModelProperty(value = "交跨类型")
	private String crossType;



	/*************************************交跨点**end********************************/

	/*************************************交跨点与隐患点公共部分**start********************************/

	/**
	 * 净空距离
	 */
	@ApiModelProperty(value = "净空距离")
	private Double disC;
	/**
	 * 水平距离
	 */
	@ApiModelProperty(value = "水平距离")
	private Double disH;
	/**
	 * 垂直距离
	 */
	@ApiModelProperty(value = "垂直距离")
	private Double disV;
	/**
	 * 线路经度
	 */
	@ApiModelProperty(value = "线路经度")
	private Double linePointLng;
	/**
	 * 线路纬度
	 */
	@ApiModelProperty(value = "线路纬度")
	private Double linePointLat;
	/**
	 * 线路高程
	 */
	@ApiModelProperty(value = "线路高程")
	private Double linePointAlt;
	/**
	 * 点经度
	 */
	@ApiModelProperty(value = "点经度")
	private Double locationLng;
	/**
	 * 点纬度
	 */
	@ApiModelProperty(value = "点纬度")
	private Double locationLat;
	/**
	 * 点高程
	 */
	@ApiModelProperty(value = "点高程")
	private Double locationAlt;

	/*************************************交跨点与隐患点公共部分**end*******************************/


	/**
	 * 图片list
	 */
	private List<DataMngJsonPicVO> dataMngJsonPicVOList;

	/**
	 * 报告list
	 */
	private List<DataMngJsonReportVO> dataMngJsonReportVOList;

}
