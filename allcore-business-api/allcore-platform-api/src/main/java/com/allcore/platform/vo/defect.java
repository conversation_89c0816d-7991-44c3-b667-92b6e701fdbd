package com.allcore.platform.vo;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 同步中台缺陷数据
 */
@Data
public class defect {

    private String professionalKind;

    /**
     * 缺陷编号
     */
    private String defectNo;

    /**
     * 发现班组id
     */
    private String discoveryGroup;

    /**
     * 发现班组名称
     */
    private String discoveryGroupName;

    /**
     * 发现人id
     */
    private String discoverer;

    /**
     * 发现人姓名
     */
    private String diccovererName;

    /**
     * 发现人单位id
     */
    private String discoveryOrg;

    /**
     * 发现人单位名称
     */
    private String discoveryOrgName;

    /**
     * 来源id
     */
    private String sourceId;

    /**
     * 数据来源(巡视、现场作业、其他)
     */
    private String dataSource;

    /**
     * 应用来源
     */
    private String systemSource;

    /**
     * 应用来源id
     */
    private String systemSourceId;

    /**
     * 发现日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date discoveryDate;

    /**
     * 站线类型(电站、线路)
     */
    private String containerType;

    /**
     * 站线名称
     */
    private String containerName;

    /**
     * 站线id
     */
    private String container;

    /**
     * 设备类型
     */
    private String equipTypeCode;

    /**
     * 设备类型名称
     */
    private String equipTypeName;

    /**
     * 设备名称
     */
    private String euqipName;

    /**
     * 设备资源id
     */
    private String psrId;

    /**
     * 设备资产id
     */
    private String astId;

    /**
     * 设备电压等级(110KV/220KV)
     */
    private String VoltageLevel;

    /**
     * 厂家名称
     */
    private String manufacturerName;

    /**
     * 生产厂家编码
     */
    private String manufacturer;

    /**
     * 设备种类
     */
    private String equipSpeciesCode;

    /**
     * 设备种类名称
     */
    private String equipSpeciesName;

    /**
     * 部件类型
     */
    private String componentType;

    /**
     * 部件类型名称
     */
    private String componentTypeName;

    /**
     * 部件种类
     */
    private String componentSpecies;

    /**
     * 部件种类名称
     */
    private String componentSpeciesName;

    /**
     * 缺陷部位
     */
    private String defectLocation;

    /**
     * 缺陷部位名称
     */
    private String defectLocationName;
    /**
     * 缺陷描述
     */
    private String defectDescriptionCode;

    /**
     * 缺陷部位描述名称
     */
    private String defectDescName;

    /**
     * 分类依据
     */
    private String sortRef;

    /**
     * 分类依据名称
     */
    private String sortRefName;

    /**
     * 是否消缺
     */
    private String eliminateOrNot;

    /**
     * 是否自行消缺
     */
    private String eliminateByOneseOrNot;

    /**
     * 缺陷性质(一般、严重、危及)
     */
    private String defectProperties;

    /**
     * 缺陷内容
     */
    private String defectContent;

    /**
     * 备注
     */
    private String remark;

    /**
     * 缺陷状态
     */
    private String state;

    /**
     * 流程id
     */
    private String prciId;

    /**
     * 所属地址id
     */
    private String cityOrgId;

    /**
     * 所属地址名称
     */
    private String cityOrgName;

    /**
     * 维护班组id
     */
    private String maintcrewId;

    /**
     * 维护班组名称
     */
    private String maintGroupName;

    /**
     * 运维单位id
     */
    private String maintainerId;

    /**
     * 运维单位名称
     */
    private String maintOrgName;

    /**
     * 创建人id
     */
    private String createrId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 是否app登记
     */
    private String appState;

    /**
     * 投运日期
     */
	@JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date commissioningDate;

    /**
     * 扩展字段(json格式)
     */
    private String extend;

    /**
     * 汇报调情况
     */
    private String rptSchedule;

    /**
     * 汇报监控情况
     */
    private String rptMonitor;

    /**
     * 是否发送调度
     */
    private String sendSchedulingOrNot;

    /**
     * 调度接收单位
     */
    private String omsReceiveOrg;

    /**
     * 调度接收单位名称
     */
    private String omsReceiveOrgName;

    /**
     * 缺陷处理情况
     */
    private String defectTreatment;

    /**
     * 缺陷附件
     */
    private List<defectAttachment> defectAttachment;

    /**
     * 审核人信息
     */
    private List<defectCheck> defectCheck;

    /**
     *  id
     */
    private String objId;
    /**
     * 站线ID
     */
    private String lineId;
    /**
     * 站线名称
     */
    private String lineName;
    // 电压等级
    private String containerVoltageLevel;
    // 电压等级名称
    private String voltageLevelName;
    // 缺陷性质
    private String defectClass;
    // 缺陷原因
    private String defectReason;
    // 发现来源
    private String findSource;
    // 缺陷程度
    private String defectLevel;
    // 设备电压等级
    private String equipVoltageLevel;
    // 设备型号
    private String equipModel;
    // 投运日期
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date operationTime;
    // 方位
    private String orientation;
    // 相位
    private String phasePosition;
    // 杆塔编号
    private String towerNo;
    // 方向
    private String turnDirection;
    // 分类依据
    private String classificationBasisCode;
    // 分类依据名称
    private String classificationBasisName;
    // 典型特征
    private String typicalCharacteristics;
    // 是否自行消缺（是/否）
    private String eliminateByOneselfOrNot;
    // 汇报监控情况
    private String reportMonitorCondition;
    // 部件种类
    private String componentSpeciesCode;
    // 缺陷部位
    private String defectLocationCode;
    // 缺陷描述名称
    private String defectDescriptionName;
    // distance
    private String distance;
    // 缺陷附件
    private String defectAttachmentId;
    // 发现人ID
    private String discovererId;
    // 发现人姓名
    private String discovererName;
    /**
     * 发现日期
     */
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    private Date registerTime;
    // 发现班组ID
    private String discovererCrewId;
    // 发现班组名称
    private String discovererCrewName;
    // 发现单位ID
    private String discovererUnitId;
    // 发现单位名称
    private String discovererUnitName;
    // 调度接收单位
    private String dispOrgId;
    // 调度接收单位名称
    private String dispOrgName;
    // 发现业务类型
    private String taskType;
    // 汇报调度情况
    private String reportScheduleCondition;
    // 审核意见
    private String reviewOpinion;
    // 是否发送调度
    private String sendOmsOrNot;
    // 序号
    private String sequenueNum;
    // 子序号
    private String sonSequenueNum;
    // 发现业务ID
    private String task;
    // 创建人名称
    private String createrName;
    // 修改人ID
    private String editorId;
    // 修改人名称
    private String editorName;
    // 修改时间
    private String mtime;
    // 是否删除（是/否）0 未删除 1 已删除
    private String isDeleted;
    // 维护班组名称
    private String maintcrewName;
    // 运维单位名称
    private String maintainerName;
    // 任务来源ID
    private String taskSourceId;
    // 任务来源
    private String taskSource;
    // dataSourceId
    private String dataSourceId;
    // 附件类型
    private String attachmentType;
    // 缺陷附件名称
    private String attachmentName;
    // 任务id
    private String taskId;


}
