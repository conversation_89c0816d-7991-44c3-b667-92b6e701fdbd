package com.allcore.platform.dto;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;
import org.hibernate.validator.constraints.Length;
import org.hibernate.validator.constraints.NotBlank;

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class TbUavPlaneinfoDTO {

    private String planeguid;

    @NotBlank(message = "制造厂商不能为空")
    @Length(max = 50,message = "制造厂商最长支持50个字符")
    private String planeuavmake;

    @NotBlank(message = "无人机型号不能为空")
    @Length(max = 50,message = "无人机型号最长支持50个字符")
    private String planeuavmodel;

    @Length(min = 1, max = 32,message = "无人机设备号最长支持32个字符")
    @NotBlank(message = "设备资产编号不能为空")
    private String planeequipmentno;

    @Length(max = 20,message = "注册时间最长支持20个字符")
    private String planeregistertime;

    @Length(max = 50,message = "资产编号最长支持50个字符")
    private String planeassetno;

    @Length(max = 20,message = "采购日期最长支持20个字符")
    private String planepurchasetime;

    @Length(max = 32,message = "所属单位最长支持32个字符")
    private String planereceivingunit;

    @Length(max = 50,message = "设备来源最长支持50个字符")
    private String planeequipmentsource;

    @Length(max = 20,message = "出厂日期最长支持20个字符")
    private String planefactorytime;

    private String teamguid;


}
