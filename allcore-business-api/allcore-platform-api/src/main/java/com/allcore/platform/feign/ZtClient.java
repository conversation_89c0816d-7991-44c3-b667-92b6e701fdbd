package com.allcore.platform.feign;

import com.allcore.core.launch.constant.AppConstant;
import com.allcore.platform.dto.DefectPictureDetailDTO;
import com.allcore.platform.dto.WorkOrderRequest;
import com.allcore.platform.dto.WorkPlanRequest;
import com.allcore.platform.dto.WorkRecordRequest;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * （中台 1-6 都是固定入参调用
 * 中台 7-8 作业计划新增和修改 重构
 * 中台 9-10都是固定入参调用
 * 中台 11-12 工单新增修改 重构
 * 中台 13 是固定入参调用
 * 中台 14 工单查询 重构
 * 中台 15 巡检结果新增 重构
 * 中台 16 巡检结果修改 不会有实际修改
 * 中台 17-18 都是固定入参调用
 * 中台 19 缺陷新增 重构 无和工单或计划新增 类似的回调代码
 * 中台 20 是固定入参调用
 * 中台 21 缺陷数据查看 重构 虽然传了guid但可能没流转 新增的时候 没看到保存中台objId代码
 * ）
 * <AUTHOR>
 * @date 2022/09/19 11:00
 **/
@FeignClient(
	value = AppConstant.APPLICATION_PLATFORM_NAME,
	fallback = ZtClientFallback.class
)
public interface ZtClient {

	/**
	 * （重庆中台21-1 设备型号编码精确查询--按设备型号编码精确查询设备型号信息）
	 * <AUTHOR>
	 * @date 2022/10/09 16:23
	 * @param id
	 * @return void
	 */
	@RequestMapping("/inner/InnerCqSyncZtController/GetEquipByType")
	void GetEquipByType(@RequestParam("id") String id);

	/**
	 * （重庆中台21-2 生产厂家精确查询-按生产厂家编码精确查询生产厂家信息）
	 * <AUTHOR>
	 * @date 2022/10/09 16:25
	 * @param id
	 * @return void
	 */
	@RequestMapping("/inner/InnerCqSyncZtController/GetEquipByCode")
	void GetEquipByCode(@RequestParam("id") String id);

	/**
	 * （重庆中台21-3 智能装备台帐查看--提供查看无人机、机器人等智能装备台帐参数详情的服务能力）
	 * <AUTHOR>
	 * @date 2022/10/09 16:27
	 * @param id
	 * @return void
	 */
	@RequestMapping("/inner/Ztcontroller/SmartDeviceQuery")
	void SmartDeviceQuery(@RequestParam(value = "id") String id);

	/**
	 * （重庆中台21-4 智能装备台帐查询-提供查询无人机、机器人等智能装备台帐列表的服务能力）
	 * <AUTHOR>
	 * @date 2022/10/09 16:29
	 * @param id
	 * @return void
	 */
	@RequestMapping("/inner/Ztcontroller/findSmartDevice")
	void findSmartDevice(@RequestParam(value = "id") String id);

	/**
	 * （重庆中台21-5 输电线路列表查询
	 * 按照运维单位、所属城市、所属网省查询输电线路清单，
	 * 考虑跨区线路数量统计不准情况问题，如跨市线路，
	 * 在省公司层面查询统计线路时，只算一条，
	 * 在地市公司层面查询统计时，每个地方个算一条）
	 * <AUTHOR>
	 * @date 2022/10/09 16:32
	 * @return void
	 */
	@RequestMapping("/inner/Equipment/QueryLineList")
	void queryLineList();

	/**
	 * （重庆中台21-6 输电线路下辖设备查询-
	 *  按照指定输电线路ID、下辖设备类型查询输电线路下设备信息列表，支持多设备类型查询）
	 * <AUTHOR>
	 * @date 2022/10/09 16:32
	 * @return void
	 */
	@RequestMapping("/inner/Ztcontroller/QueryByLineContainerForBJ")
	void QueryByLineContainer(@RequestParam(value = "id") String id);

	/**
	 * （重庆中台21-7 巡视计划编制）
	 * <AUTHOR>
	 * @date 2022/10/10 10:40
	 * @param workPlanRequest
	 * @return void
	 */
	@PostMapping("/api/externaldata/ztplan/workPlanCreate")
	void workPlanCreate(@RequestBody WorkPlanRequest workPlanRequest);

	/**
	 * （重庆中台21-8 巡视计划变更--提供电站、线路巡视计划修改、删除服务能力）
	 * <AUTHOR>
	 * @date 2022/10/10 10:43
	 * @param workPlanRequest
	 * @return void
	 */
	@PostMapping("/api/externaldata/ztplan/workPlanChange")
	void workPlanChange(@RequestBody WorkPlanRequest workPlanRequest);

	/**
	 * （重庆中台21-9 巡视计划查询--按照巡视类型、巡视班组、计划巡视时间等参数进行查询）
	 * <AUTHOR>
	 * @date 2022/10/10 10:44
	 * @param page
	 * @param perPage
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztplan/workPlanList")
	void workPlanList(@RequestParam(value = "page",required = false)String page,@RequestParam(value = "perPage",required = false)String perPage);


	/**
	 * （重庆中台21-10 巡视计划查看）
	 * <AUTHOR>
	 * @date 2022/10/10 10:44
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztplan/workPlanQuery")
	void workPlanQuery(@RequestParam(value = "msg",required = false)String msg);

	/**
	 * （重庆中台21-11 巡视工单编制）
	 * <AUTHOR>
	 * @date 2022/10/10 14:38
	 * @param workOrderRequest
	 * @return void
	 */
	@PostMapping("/api/externaldata/ztorder/workorderCreate")
	void workorderCreate(@RequestBody WorkOrderRequest workOrderRequest);


	/**
	 * （重庆中台21-12 巡检工单变更）
	 * <AUTHOR>
	 * @date 2022/10/10 10:46
	 * @param workOrderRequest
	 * @return void
	 */
	@PostMapping("/api/externaldata/ztorder/workorderChange")
	void workorderChange(@RequestBody WorkOrderRequest workOrderRequest);


	/**
	 * （重庆中台21-13 巡检工单查询）
	 * <AUTHOR>
	 * @date 2022/10/10 10:47
	 * @param unitpmscode
	 * @param page
	 * @param limit
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztorder/workorderQuerycxByPmsCode")
	void workorderQuerycxByPmsCode(@RequestParam(value = "unitpmscode",required = false)String unitpmscode,@RequestParam(value = "page",defaultValue = "1")Integer page,@RequestParam(value = "limit",defaultValue = "10")Integer limit);



	/**
	 * （重庆中台21-14 巡检工单查看）
	 * <AUTHOR>
	 * @date 2022/10/10 10:48
	 * @param inspectGuidListStr
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztorder/workorderQuerycxByGuid")
	void workorderQuerycxByGuid(@RequestParam(value = "inspectGuidListStr",required = false)String inspectGuidListStr);

	/**
	 * （重庆中台21-15 巡视记录创建）
	 * <AUTHOR>
	 * @date 2022/10/10 10:51
	 * @param workRecordRequest
	 * @return void
	 */
	@PostMapping("/api/externaldata/ztrecord/workRecordCreate")
	void workRecordCreate(@RequestBody WorkRecordRequest workRecordRequest);

	/**
	 * （重庆中台21-16 巡视记录变更）
	 * <AUTHOR>
	 * @date 2022/10/10 10:52
	 * @param workRecordRequest
	 * @return void
	 */
	@PostMapping("/api/externaldata/ztrecord/workRecordUpdate")
	void workRecordUpdate(@RequestBody WorkRecordRequest workRecordRequest);

	/**
	 * （重庆中台21-17 巡视记录查询）
	 * <AUTHOR>
	 * @date 2022/10/10 10:53
	 * @param msg
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztrecord/workRecordList")
	void workRecordList(@RequestParam(value = "msg",required = false)String msg);

	/**
	 * （重庆中台21-18 巡视记录查看）
	 * <AUTHOR>
	 * @date 2022/10/10 10:53
	 * @param msg
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztrecord/workRecordQuery")
	void workRecordQuery(@RequestParam(value = "msg",required = false)String msg);


	/**
	 * （重庆中台21-19 新增缺陷）
	 * <AUTHOR>
	 * @date 2022/10/10 10:55
	 * @param detailDTOList
	 * @return void
	 */
	@PostMapping("/api/externaldata/ztdefect/syncDefectData")
	void syncDefectData(@RequestBody List<DefectPictureDetailDTO> detailDTOList) ;

	/**
	 * （重庆中台21-20 缺陷数据查询）
	 * <AUTHOR>
	 * @date 2022/10/10 10:55
	 * @param unitGUID
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztdefect/getDefectDataByGuid")
	void getDefectDataFromZT(@RequestParam(value = "unitGUID", required = false) String unitGUID) ;


	/**
	 * （重庆中台21-21 缺陷数据查看）
	 * <AUTHOR>
	 * @date 2022/10/10 10:56
	 * @param detailId
	 * @return void
	 */
	@RequestMapping("/api/externaldata/ztdefect/getSelectDefectOneDataByDetail")
	void getSelectDefectOneDataByDetail(@RequestParam(value = "detailId", required = false) String detailId);


}
