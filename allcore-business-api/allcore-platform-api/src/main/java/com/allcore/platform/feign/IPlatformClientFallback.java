package com.allcore.platform.feign;

import com.allcore.core.tool.api.R;
import com.allcore.platform.dto.AlgorithmDTO;
import com.allcore.platform.dto.AlgorithmLyyDTO;
import org.springframework.stereotype.Component;

/**
 * Feign失败配置
 *
 * <AUTHOR>
 */
@Component
public class IPlatformClientFallback implements IPlatformClient {

	@Override
	public R<String> invokingAlgorithm(AlgorithmDTO algorithmDTO){
		return R.fail("获取数据失败");
	}

	@Override
	public R<String> invokingAlgorithmLyy(AlgorithmLyyDTO algorithmLyyDTO){
		return R.fail("获取数据失败");
	}

}
