package com.allcore.platform.feign;


import com.allcore.core.launch.constant.AppConstant;
import com.allcore.core.tool.api.R;
import com.allcore.platform.vo.DroneLedgerVo;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * 机巢 Feign接口类
 *
 * <AUTHOR>
 */
@FeignClient(
	value = AppConstant.APPLICATION_PLATFORM_NAME,
	fallback = IDroneLedgerClientFallBack.class
)
public interface IDroneLedgerClient {

	String CLIENT = "/droneLedger";

	String SELECT_BY_DEPT_ID = CLIENT + "/selectByDeptId";


	/**
	 * 根据DeptId查询机巢信息
	 * @param deptCode
	 * @return
	 */
	@GetMapping(value = SELECT_BY_DEPT_ID)
	R<List<DroneLedgerVo>> selectInDeptCode(@RequestParam String deptCode);
}
