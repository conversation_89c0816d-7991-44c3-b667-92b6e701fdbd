package com.allcore.platform.vo.InnerOrdervo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class vehicles {

    // 乘车事由
    private String useCarReason;
    // 乘车数量
    private String carNo;
    // 乘车人数
    private String takenPersonCounts;
    // 出发地点
    private String start;
    // 目的地点
    private String end;
    // 起始时间
    private String startTime;
    // 结束时间
    private String endTime;


}
