package com.allcore.platform.cqbean;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 中台工作任务单---巡检工单
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class taskListRegistrationVo extends BaseEntity {

    //唯一标识ID 0
    private String mRID;
    //专业 0
    private String profession;
    //工作单位 0
    private String establishmentUnit;
    //任务单编号 0
    private String workOrderNumber;
    //线路（设备）双重名称 0
    private String stationLineInformation;
    //工作内容 0
    private String jobContent;
    //工作地点 0
    private String workPlace;
    //计划开始时间 0
    private String planStartTime;
    //计划结束时间 0
    private String planEndTime;
    //工作负责人 0
    private String personInCharge;
    //小组负责人 0
    private String crewMember;
    //工作班组名称 0
    private String teamName;
    //完成情况 0
    private String completion;
    //设备变更情况 0
    private String equipmentChanges;
    //专业分类 0
    private String professionalClass;
    //所属工作票号 0
    private String jobTicketNumber;
    //电站 0
    private String substation;
    //停电范围 0
    private String powerOutageRange;
    //许可工作时间 0
    private String approvalOfPowerOutageStartTime;
    //终结报告时间 0
    private String endOfReportTimeprivate;

}
