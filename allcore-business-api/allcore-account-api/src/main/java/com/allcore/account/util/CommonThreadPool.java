package com.allcore.account.util;

import com.google.common.util.concurrent.ThreadFactoryBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class CommonThreadPool {
    private static ExecutorService executorService;

    private CommonThreadPool() {
        //EMPTY
    }

    static {
        int availableProcessor = Runtime.getRuntime().availableProcessors();
        int coreNum = availableProcessor / 2;
        int maxProcessor = (availableProcessor * 2 + 1) * 2;
        ThreadFactory namedThreadFactory = new ThreadFactoryBuilder()
                .setNameFormat("demo-pool-%d").build();
        executorService = new ThreadPoolExecutor(coreNum, maxProcessor,
                60L, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(Integer.MAX_VALUE), namedThreadFactory, new CustomRejectedExecutionHandler());

    }

    public static void executeTask(Runnable runnable) {
        executorService.execute(runnable);
    }
    /**
     * 队列已满 30s后尝试入列
     */
    static class CustomRejectedExecutionHandler implements RejectedExecutionHandler {
        @Override
        public void rejectedExecution(Runnable runnable, ThreadPoolExecutor executor) {
            try {
                log.info("===========拒绝线程入队列中...");
                Thread.sleep(30000);
                if (!executor.isShutdown()) {
                    //再尝试入队
                    executor.submit(runnable);
                    log.info("===========进入到队列中");
                }
            } catch (InterruptedException e) {
                log.error("====线程中断：" + e.getMessage());
            } catch (Exception e) {
                log.error("====线程丢弃：" + e.getMessage());
            }
        }
    }
}
