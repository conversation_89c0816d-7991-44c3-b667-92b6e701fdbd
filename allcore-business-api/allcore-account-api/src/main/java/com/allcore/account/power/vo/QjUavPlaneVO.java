package com.allcore.account.power.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @data 2022/11/11 14:20
 * @description: 全景无人机信息
 */
@Data
@ApiModel("全景无人机信息")
public class QjUavPlaneVO {

	@ApiModelProperty("无人机guid")
	private String planeGUID;

	@ApiModelProperty("无人机SN码")
	private String planeEquipmentNo;

	@ApiModelProperty("监控模块编号")
	private String planeEquipmentCode;

	@ApiModelProperty("无人机品牌")
	private String modelBrand;

	@ApiModelProperty("无人机型号")
	private String modelUAVModel;

	@ApiModelProperty("注册时间")
	private String planeRegisterTime;

	@ApiModelProperty("资产性质")
	private String planeAssetAttributesName;

	@ApiModelProperty("使用次数")
	private String sycs;

	@ApiModelProperty("无人机类型")
	private String sysCodeName;

	@ApiModelProperty("制造厂商")
	private String planeUAVMake;

	@ApiModelProperty("实名认证编号")
	private String planeReserve5;

	@ApiModelProperty("设备来源")
	private String planeEquipmentSource;

	@ApiModelProperty("保管人")
	private String planeCustodian;

	@ApiModelProperty("无人机名称")
	private String planeReserve8;

	@ApiModelProperty("所属地市公司guid")
	private String unitGUID;

	@ApiModelProperty("所属地市公司")
	private String receivingUnitName;

	@ApiModelProperty("所属工区id")
	private String workAreaGUID;

	@ApiModelProperty("所属工区名称")
	private String workAreaUnitName;

	@ApiModelProperty("所属班组id")
	private String teamGUID;

	@ApiModelProperty("所属班组名称")
	private String teamUnitName;

	@ApiModelProperty("备注")
	private String planeRemark;

	@ApiModelProperty("所属单位名称全拼")
	private String unitName;

	@ApiModelProperty("无人机状态(业务字典）")
	private String planeReserve1;

	@ApiModelProperty("无人机状态名称")
	private String wrjzt;

	@ApiModelProperty("无人机型号GUID")
	private String modelGUID;

	@ApiModelProperty("无人机类型code")
	private String modelType;

	@ApiModelProperty("资产性质code")
	private String planeAssetAttributes;

	@ApiModelProperty("统计工单数")
	private String countWorkOrder;

	@ApiModelProperty("无人机自增id")
	private String planeID;

	@ApiModelProperty("专业类型code")
	private String professionalType;

	@ApiModelProperty("专业类型名称")
	private String professionalTypeName;

	@ApiModelProperty("保养次数")
	private String planeNumber;

	@ApiModelProperty("飞行里程")
	private String planeFlyMileage;

	@ApiModelProperty("飞行架次")
	private String planeFlyNumber;

	@ApiModelProperty("飞行时长(分钟)")
	private String planeFlyTime;

	@ApiModelProperty("单位code")
	@JsonIgnore
	private String deptCode;

	@ApiModelProperty("单位id")
	@JsonIgnore
	private String uavDeptId;

}
