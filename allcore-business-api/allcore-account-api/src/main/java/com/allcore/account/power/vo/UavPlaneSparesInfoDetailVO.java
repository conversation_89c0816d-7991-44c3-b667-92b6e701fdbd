package com.allcore.account.power.vo;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 备品详情
 *
 * <AUTHOR>
 * @date 2022/04/19 14:40
 **/
@Data
public class UavPlaneSparesInfoDetailVO implements Serializable {

	private Long id;

	private String modelInfoGuid;
	/**
	 * 备品备件名称
	 */
	private String sparesName;

	private String sparesCode;
	/**
	 * 编码
	 */
	private String deptCode;

	private String uavType;
	/**
	 * 备件对应机型
	 */
	private String modelName;
	/**
	 * 备品备件类型
	 */
	private String sparesType;
	/**
	 * 备品备件型号
	 */
	private String sparesModel;
	/**
	 * 生产厂家
	 */
	private String manufacturer;
	/**
	 * 品牌
	 */
	private String sparesBrand;

	/**
	 * 库存数
	 */
	private Integer stockNum;
	/**
	 * 备注
	 */
	private String remarks;

	/**
	 * 入库列表
	 */
	private List<UavPlaneSparesStockLogVO>  putInStorageList;

	/**
	 * 出库列表
	 */
	private List<UavPlaneSparesStockLogVO>  exwarehouseList;
}
