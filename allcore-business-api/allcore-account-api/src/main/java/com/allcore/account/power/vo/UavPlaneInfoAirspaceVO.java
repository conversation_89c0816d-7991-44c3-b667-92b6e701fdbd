package com.allcore.account.power.vo;

import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 空域管理无人机列表
 *
 * <AUTHOR>
 * @date 2022/04/21 10:17
 **/
@Data
public class UavPlaneInfoAirspaceVO implements Serializable {
	private Long deptId;

	private Long id;

	private String planeGuid;

	/**
	 * 型号
	 */
	private String modelGuid;

	/**
	 * 无人机品牌
	 */
	private String uavBrand;
	/**
	 * 型号
	 */
	private String modelName;
	/**
	 * 类别(数据字典)
	 */
	private String uavType;

	private String snCode;

	/**
	 * 空机重量(千克)
	 */
	private BigDecimal emptyWeight;

	/**
	 * 续航时间（分钟）
	 */
	private BigDecimal enduranceTime;

	private String deptCode;

	/**
	 * 无人机名称
	 */
	private String realCertificationNumber;
}
