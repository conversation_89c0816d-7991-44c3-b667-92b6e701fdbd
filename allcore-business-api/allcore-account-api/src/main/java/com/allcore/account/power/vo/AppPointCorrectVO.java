package com.allcore.account.power.vo;

import lombok.Data;

import java.util.Date;

/**
 * 坐标修正
 * <AUTHOR>
 * @date 2022/09/28 14:12
 **/
@Data
public class AppPointCorrectVO {
	/**
	 * guid
	 */
	private String correctGuid;
	/**
	 * 线路guid
	 */
	private String lineGuid;
	/**
	 * 杆塔id
	 */
	private String towerGuid;
	/**
	 * 修改后经度
	 */
	private String longitude;
	/**
	 * 修改后纬度
	 */
	private String latitude;
	/**
	 * 修改后海拔高度
	 */
	private Double altitude;

	/**
	 * 审核状态 0：未审核 1：已审核
	 */
	private int examineType;
	/**
	 *
	 */
	private String examineTypeName;
	/**
	 * 审核人
	 */
	private String examineUser;
	/**
	 * 审核时间
	 */
	private Date examineTime;
	/**
	 * 线路名称
	 */
	private String lineName;
	/**
	 * 创建时间
	 */
	private Date createTime;

	private String towerNo;
}
