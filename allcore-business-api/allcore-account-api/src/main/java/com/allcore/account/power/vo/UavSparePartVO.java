package com.allcore.account.power.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
* @author: ldh
* @date: 2022/9/14 19:11
* @description: 无人机备件详情
*/
@Data
@ApiModel("无人机备件详情")
public class UavSparePartVO {


	@ApiModelProperty("id")
	private String id;

	@ApiModelProperty("GUID")
	private String sparePartGuid;

	@ApiModelProperty("运维单位")
	private String operationUnit;

	@ApiModelProperty("运维中心")
	private String operationCentre;

	@ApiModelProperty("运维班组")
	private String operationTeam;

	/**
	 * 编码
	 */
	@ApiModelProperty("编码")
	private String sparePartNo;

	/**
	 * 备件名称
	 */
	@ApiModelProperty("备品备件名称")
	private String sparePartName;

	/**
	 * 备件对应机型品牌
	 */
	@ApiModelProperty("部件对应机型品牌")
	private String uavBrand;

	/**
	 * 备件对应机型品牌
	 */
	@ApiModelProperty("部件对应机型品牌name")
	private String uavBrandName;

	/**
	 * 部件对应机型型号
	 */
	@ApiModelProperty("部件对应机型型号")
	private String modelGuid;

	/**
	 * 部件对应机型型号
	 */
	@ApiModelProperty("部件对应机型型号name")
	private String modelName;

	/**
	 * 备品备件类型
	 */
	@ApiModelProperty("备品备件类型")
	private String sparePartType;

	/**
	 * 备品备件型号
	 */
	@ApiModelProperty("备品备件型号")
	private String sparePartModel;


	@ApiModelProperty("生产厂家")
	private String manufacturer;

	/**
	 * 备件品牌
	 */
	@ApiModelProperty("品牌")
	private String sparePartBrand;

	@ApiModelProperty("备注")
	private String remarks;

	/**
	 * 库存数量
	 */
	@ApiModelProperty("库存数量")
	private Integer stockCount;

	@JsonIgnore
	private String deptCode;

	@JsonIgnore
	private String createDept;
}
