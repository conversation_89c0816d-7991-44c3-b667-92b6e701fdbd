package com.allcore.account.power.vo;


import com.allcore.user.entity.User;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;



/**
 * <p>
 * 片区管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Data
@ApiModel(value="AreaUserVO对象", description="片区用户单位VO")
public class AreaUserVO extends User {


	@ApiModelProperty(value = "岗位职责")
	private String postDuty;

	@ApiModelProperty(value = "单位名称")
	private String deptName;

	@ApiModelProperty(value = "账号周期类型")
	private String accountTypeName;

}
