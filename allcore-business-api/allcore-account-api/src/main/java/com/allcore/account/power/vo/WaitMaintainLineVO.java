package com.allcore.account.power.vo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @email <EMAIL>
 * @Date 2022/10/11 0011 14:42
 */
@Data
public class WaitMaintainLineVO {

	@Excel(name = "序号")
	@ApiModelProperty("序号")
	private Integer serialNumber;

	@Excel(name = "电压等级")
	@ApiModelProperty("电压等级")
	private String lineVoltageLevel;

	@Excel(name = "线路名称")
	@ApiModelProperty("线路名称")
	private String lineName;

	@ApiModelProperty("线路guid")
	private String lineGuid;

	@Excel(name = "运维中心")
	@ApiModelProperty("运维中心")
	private String workAreaName;

	@ApiModelProperty("运维班组")
	private String maintGroup;

	@Excel(name = "运维单位")
	@ApiModelProperty("运维单位")
	private String deptName;

	@ApiModelProperty("代维单位Guid")
	private String waitMaintainName;

	@Excel(name = "代维单位")
	@ApiModelProperty("代维单位（省级-中心-班组）")
	private String waitMaintainNameStr;

	@Excel(name = "总长")
	@ApiModelProperty("总长")
	private String lineLength;

	@Excel(name = "设备编码")
	@ApiModelProperty("设备编码")
	private String spaceCode;

	@Excel(name = "杆塔数量")
	@ApiModelProperty("杆塔数量")
	private Integer towerNumber;
}
