package com.allcore.account.power.vo;

import com.allcore.account.power.entity.AreaManageRelUnitEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <p>
 * 片区关联单位VO对象
 * </p>
 *
 * <AUTHOR>
 * @since 2022-09-01
 */
@Data
@ApiModel(value="AreaManageRelUnitVO对象", description="片区关联单位VO对象")
public class AreaManageRelUnitEntityVO extends AreaManageRelUnitEntity {


    @ApiModelProperty(value = "单位名称")
    private String unitName;

    @ApiModelProperty(value = "所属区域")
    private String belongArea;

    @ApiModelProperty(value = "单位级别")
    private String unitLevel;


}
