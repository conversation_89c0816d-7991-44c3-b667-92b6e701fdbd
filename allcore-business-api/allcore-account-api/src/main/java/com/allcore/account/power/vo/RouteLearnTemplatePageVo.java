package com.allcore.account.power.vo;

import com.allcore.common.base.ZxhcEntity;
import lombok.Data;

import java.io.Serializable;

/**
 * 航线模板(RouteLearnTemplate)实体类
 *
 * <AUTHOR>
 * @since 2022-08-29 17:43:40
 */
@Data
public class RouteLearnTemplatePageVo extends ZxhcEntity implements Serializable {
    private static final long serialVersionUID = -11147340518004113L;
    /**
     * 航线学习模板GUID
     */
    private String templateGuid;
    /**
     * 航线学习模板名称
     */
    private String templateName;
    /**
     * 电压等级
     */
    private String voltageLevel;
    /**
     * 杆塔性质（strain耐张 ；direct直流）
     */
    private String towersProperty;
    /**
     * 回路数（singleCircuit：单回，doubleCircuit：双回，fourLoops：四回）
     */
    private String loopsNumber;
    /**
     * 航线模板内容
     */
    private String templateContent;
	/**
	 * 专业类型: tms:输电, dms:配电
	 */
	private String professionalType;

	/**
	 * 第一个单位
	 */
	private String firstGuid;
	private String firstName;

	/**
	 * 第二个单位
	 */
	private String secondGuid;
	private String secondName;

	/**
	 * 第三个单位
	 */
	private String thirdGuid;
	private String thirdName;


}

