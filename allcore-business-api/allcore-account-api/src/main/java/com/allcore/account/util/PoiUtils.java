package com.allcore.account.util;


import cn.afterturn.easypoi.excel.ExcelImportUtil;
import cn.afterturn.easypoi.excel.entity.ImportParams;
import org.apache.commons.io.FileUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
* @author: ldh
* @date: 2022/9/29 10:07
* @description:
*/
public class PoiUtils {

	/**
	 * 文件下载
	 *
	 * @param netAddress
	 * @param response
	 * @param filename
	 */
	public static void downFile(String netAddress, HttpServletResponse response, String filename) {
		try {
			URL url = new URL(netAddress);
			URLConnection conn = url.openConnection();
			InputStream inputStream = conn.getInputStream();

			response.reset();
			response.setContentType(conn.getContentType());
			//纯下载方式 文件名应该编码成UTF-8
			response.setHeader("Content-Disposition", "attachment; filename=" + URLEncoder.encode(filename, "UTF-8"));
			byte[] buffer = new byte[1024];
			int len;
			OutputStream outputStream = response.getOutputStream();
			while ((len = inputStream.read(buffer)) > 0) {
				outputStream.write(buffer, 0, len);
			}
			inputStream.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * @param file       Excel文件
	 * @param sheetIndex 对应的表单，从0开始，0代表第一个表单
	 * @param clazz      对应封装的数据实例对象
	 * @return 返回数据集合
	 */
	public static <E> List<E> readExcel(MultipartFile file, int sheetIndex, Class<E> clazz) {
		String property = System.getProperty("user.dir");
		// 定义输入流
		FileInputStream fis = null;
		List<E> datas = null;
		File file1 = new File(property + File.separator + "allcore-business" + File.separator + "allcore-account" + File.separator + "src" + File.separator + "main" + File.separator + "resources" + File.separator + "temp" + File.separator + System.currentTimeMillis() + ".xlsx");
		try {
			FileUtils.copyInputStreamToFile(file.getInputStream(), file1);
			// 创建输入流对象
			fis = new FileInputStream(file1);
			// 创建一个easypoi使用的配置类
			ImportParams params = new ImportParams();
			// 设置表格坐标
			params.setStartSheetIndex(sheetIndex);
			// 校验Excel文件，去掉空行
			params.setNeedVerify(true);
			// 读取数据
			datas = ExcelImportUtil.importExcel(fis, clazz, params);
		} catch (Exception e) {
			e.printStackTrace();
		} finally {

			try {
				if (fis != null) {
					fis.close();
				}
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		file1.delete();
		return datas;
	}

	/**
	 * @author: ldh
	 * @date: 2022/9/18 8:34
	 * @description: 压缩文件流下载
	 */
	public static void downInputStreamZip(Map<String,InputStream> map, HttpServletResponse response) {
		ZipOutputStream zipOutputStream = null;
		try {
			// 设置响应流信息
			response.setContentType("application/x-zip-compressed");
			response.setHeader("Pragma", "no-cache");
			response.setHeader("Cache-Control", "no-cache");
			String fileName= "uav-info.zip";
			response.setHeader("Content-Disposition", "attachment;filename="+ fileName);
			response.setDateHeader("Expires", 0);
			zipOutputStream = new ZipOutputStream(response.getOutputStream());
			for (String key : map.keySet()) {
				compress(zipOutputStream, map.get(key),key);
			}

		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			try {
				zipOutputStream.flush();
				zipOutputStream.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * @author: ldh
	 * @date: 2022/9/18 8:36
	 * @description: 文件压缩
	 */
	public static void compress(ZipOutputStream out, InputStream inputStream, String name) throws IOException {
		out.putNextEntry(new ZipEntry(name));
		BufferedInputStream bis = new BufferedInputStream(inputStream);
		int len;
		//将源文件写入到zip文件中
		byte[] buf = new byte[1024];
		while ((len = bis.read(buf)) != -1) {
			out.write(buf, 0, len);
		}
		bis.close();
		inputStream.close();
	}

	/**
	* @author: ldh
	* @date: 2022/9/18 8:42
	* @description: 服务器静态文件下载
	*/
	public static void downLoadLocalFile(String filePath, String fileName, HttpServletResponse response){
		try {
			File file = new File(filePath);
			if (file.exists()) {
				response.setContentType("application/x-msdownload");
				response.setHeader("Content-Disposition", "attachment;filename=" + new String(fileName.getBytes(), "ISO-8859-1"));
				InputStream inputStream = new FileInputStream(file);
				ServletOutputStream ouputStream = response.getOutputStream();
				byte[] b = new byte[1024];
				int n;
				while ((n = inputStream.read(b)) != -1) {
					ouputStream.write(b, 0, n);
				}
				ouputStream.close();
				inputStream.close();
			}
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
}
