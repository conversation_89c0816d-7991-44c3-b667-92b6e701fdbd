package com.allcore.account.power.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel("无人机详情模型")
public class UavModelInfoVO {

	private String id;

	/**
	 * 无人机品牌
	 */
	@ApiModelProperty("无人机品牌")
	private String uavBrand;

	/**
	 * 无人机品牌name
	 */
	@ApiModelProperty("无人机品牌name")
	private String uavBrandName;

	/**
	 * 型号guid
	 */
	private String modelGuid;

	/**
	 * 型号
	 */
	private String modelName;
	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("大类别")
	private String bigUavType;

	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("大类别name")
	private String bigUavTypeName;

	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("中类别")
	private String middleUavType;

	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("中类别name")
	private String middleUavTypeName;

	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("小类别")
	private String smallUavType;

	/**
	 * 类别(数据字典)
	 */
	@ApiModelProperty("小类别name")
	private String smallUavTypeName;

	/**
	 * 是否有RTK功能 1:有 0:无
	 */
	@ApiModelProperty("是否有rtk功能")
	private String haveRtk;

	/**
	 * 是否有RTK功能
	 */
	@ApiModelProperty("是否有rtk功能name")
	private String haveRtkName;

	/**
	 * 最大起飞重量(千克)
	 */
	@ApiModelProperty("最大起飞重量(千克)")
	private BigDecimal maxTakeoffWeight;

	/**
	 * 最大抗风等级
	 */
	@ApiModelProperty("抗风等级")
	private Integer resistingWind;

	/**
	 * 最大速度(km/h)
	 */
	@ApiModelProperty("最大速度(km/h)")
	private BigDecimal maxSpeed;

	/**
	 * 最大飞行高度(米)
	 */
	@ApiModelProperty("最大飞行高度(米)")
	private BigDecimal maxFlightAltitude;

	/**
	 * 续航时间（分钟）
	 */
	@ApiModelProperty("续航时间（分钟）")
	private BigDecimal enduranceTime;

	/**
	 * 机身长度(毫米)
	 */
	@ApiModelProperty("机身长度(毫米)")
	private BigDecimal fuselageLength;

	/**
	 * 机身高度(毫米)
	 */
	@ApiModelProperty("机身高度(毫米)")
	private BigDecimal fuselageHeight;

	/**
	 * 机身宽度(毫米)
	 */
	@ApiModelProperty("机身宽度(毫米)")
	private BigDecimal fuselageWidth;

	/**
	 * 作业半径(米)
	 */
	@ApiModelProperty("作业半径(米)")
	private BigDecimal operatingRadius;

	/**
	 * 空机重量(千克)
	 */
	@ApiModelProperty("空机重量(千克)")
	private BigDecimal emptyWeight;

	/**
	 * 机型图片
	 */
	@ApiModelProperty("机型图片")
	private List<UavModelPicVO> fileUrls;

	/**
	 * 相机传感器类型(数据字典)
	 */
	@ApiModelProperty("相机传感器类型")
	private String cameraSensorType;

	/**
	 * 相机传感器类型(数据字典)
	 */
	@ApiModelProperty("相机传感器类型name")
	private String cameraSensorTypeName;

	/**
	 * 相机型号
	 */
	@ApiModelProperty("相机型号")
	private String cameraModel;

	/**
	 * 相机型号
	 */
	@ApiModelProperty("相机型号name")
	private String cameraModelName;

	/**
	 * 起降方式
	 */
	@ApiModelProperty("起降方式")
	private String takeoffAndLandingMode;

	/**
	 * 镜头
	 */
	@ApiModelProperty("镜头")
	private String scene;

	/**
	 * 像素
	 */
	@ApiModelProperty("像素")
	private String pixel;

	/**
	 * IOS范围
	 */
	@ApiModelProperty("IOS范围")
	private String iosRange;

	/**
	 * 云台
	 */
	@ApiModelProperty("云台")
	private String yunTai;



}
