package com.allcore.statistic.code.largescreen.feign;

import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.tool.api.R;
import com.allcore.statistic.code.largescreen.vo.StationPvInfoVO;
import com.allcore.statistic.code.largescreen.vo.StationPvInfoWithDefectOfflineVO;
import com.allcore.statistic.code.largescreen.vo.StationPvInfoWithDefectVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

/**
 * （服务feign）
 * <AUTHOR>
 * @date 2022/09/08 13:21
 */
@FeignClient(
	value = LauncherConstant.STATISTIC_SERVER_NAME,
	fallback = IScreenClientFallback.class
)
public interface IScreenClient {

	String API_PREFIX = "/client";

	String PV_INFO = API_PREFIX + "/pvInfo";
	String CHANGE_DEFECT = API_PREFIX + "/changeDefect";

	String CHANGE_DEFECTS = API_PREFIX + "/changeDefects";



//	@GetMapping(PV_INFO)
//	R<StationPvInfoWithDefectVO> pvInfo(@RequestParam String pvAreaId);

	@GetMapping(CHANGE_DEFECT)
	R<StationPvInfoWithDefectVO> changeDefect(@RequestParam String pvAreaId,@RequestParam String defectDescription,@RequestParam String inspectionTaskId);

	@GetMapping(CHANGE_DEFECTS)
	R<List<StationPvInfoWithDefectOfflineVO>> changeDefects(@RequestParam String pvAreaIds);
}
