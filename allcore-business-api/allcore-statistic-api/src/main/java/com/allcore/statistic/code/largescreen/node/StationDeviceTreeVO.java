package com.allcore.statistic.code.largescreen.node;

import com.allcore.core.tool.node.BaseNode;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 场站层-场站基础信息
 *
 * <AUTHOR>
 * @date 2023/11/16 09:12
 **/
@Data
public class StationDeviceTreeVO extends BaseNode<StationDeviceTreeVO> {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "名称")
    private String title;

    @ApiModelProperty(value = "类型")
    private String type;

    @ApiModelProperty(value = "key")
    private String key;

    @ApiModelProperty(value = "value")
    private String value;

    @ApiModelProperty(value = "经度")
    private Double longitude;

    @ApiModelProperty(value = "纬度")
    private Double latitude;

    @ApiModelProperty(value = "高程")
    private Double elevation;



}