package com.allcore.statistic.code.largescreen.vo;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

/**
 * 设备统计信息
 *
 * <AUTHOR>
 * @date 2023/11/14 15:25
 **/
@Data
@Builder
public class DeptFlyTimeVO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "场站id")
    private String stationId;

    @ApiModelProperty(value = "场站名称")
    private String stationName;

    @ApiModelProperty(value = "场站飞行时长")
    private String stationFlyTime;
}
