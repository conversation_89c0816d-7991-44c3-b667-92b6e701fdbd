package com.allcore.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value="机巢近7天的天气情况查询参数", description="机巢近7天的天气情况查询参数")
@Data
public class WeatherResultDTO {

  @ApiModelProperty("机巢编码")
  private String machineNestCode;

  @ApiModelProperty("机巢品牌商服务配置")
  private MachineNestBrandConfigDTO configDTO;


}
