package com.allcore.external.dto;

import lombok.Builder;
import lombok.Data;
import lombok.Singular;

import java.io.Serializable;
import java.util.List;

/**
 * 获取设备实时数据请求参数类
 */
@Data
public class PositionRealTimeRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    //设备唯一标识
    private List<String> skeys;
    //推送标识
    private String wsSerial;
    //推送分组
    private String wsGroup;
    /**
     * 推送类型
     * 1-只推送变化数据，2-有变化，推送group下全量数据；3-不管变不变化，定期推送；4-停止推送
     */
    private String wsType;
    //查询开始时间
    private String startTime;
    //查询结束时间
    private String endTime;
    //统计周期
    private String cycleType;
    //统计类别
    private String groupType;

}
