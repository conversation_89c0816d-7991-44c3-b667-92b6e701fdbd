package com.allcore.external.dto.SecurityCamera;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/15 14:14
 **/
@NoArgsConstructor
@Data
public class SubResourceQuDTO {

    @ApiModelProperty(value = "父区域编号")
    private String regionIndexCode;
    @ApiModelProperty(value = "当前页码")
    private Integer pageNo;
    @ApiModelProperty(value = "权限码集合")
    private List<String> authCodes;
    @ApiModelProperty(value = "分页大小")
    private Integer pageSize;
    @ApiModelProperty(value = "资源类型")
    private String resourceType;
}
