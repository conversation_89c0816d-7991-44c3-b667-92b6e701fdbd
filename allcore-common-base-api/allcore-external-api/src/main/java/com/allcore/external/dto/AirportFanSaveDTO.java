package com.allcore.external.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 11 7月 2025
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AirportFanSaveDTO implements Serializable {

    private static final long serialVersionUID = 967082552305045248L;

    /**
     * 任务名称
     */
    private String name;

    /**
     * 站点id
     */
    private String siteId;

    private String fanNum;

    private String longitude;

    private String latitude;

    private String elevation;

    private String towerHeight;

    private String bladeLength;

    private String height;
}
