package com.allcore.external.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p>无人机数据 - 实时数据</p>
 *
 * @author: sunkun
 * Date: 28 4月 2025
 */
@Data
public class UavDataDTO implements Serializable {

  private static final long serialVersionUID = 7717385679784249630L;

  /**
   * 实时纬度（WGS84 坐标系）
   */
  private String latitude;

  /**
   * 实时经度（WGS84 坐标系）
   */
  private String longitude;

  /**
   * 实时高度(m)
   */
  private String altitude;

  /**
   * 电池剩余百分比
   */
  private String batteryPercent;

  /**
   * 电池温度
   */
  private String batteryTemperature;

  /**
   * 无人机状态
   */
  private String uavState;

  /**
   * gps数量
   */
  private String gpsCount;

  /**
   * gps等级
   */
  private String gpsLevel;

  /**
   * 水平速度(m/s)
   */
  private String horizontalSpeed;

  /**
   * 水平速度 X(m/s)
   */
  private String horizontalXSpeed;

  /**
   * 水平速度 Y(m/s)
   */
  private String horizontalYSpeed;

  /**
   * 垂直速度(m/s)
   */
  private String verticalSpeed;

  /**
   * 是否正在充电
   * (0：未充电 1：正在充电)
   */
  private String isCharging;

  /**
   * 遥控器是否开机
   * (0：关机，1：开机)
   */
  private String isRcOn;

  /**
   * 无人机是否开机
   * (0：关机，1：开机)
   */
  private String isUavOn;

  /**
   * 无人机是否启用 RTK
   * (0：未开启，1：已开启)
   */
  private String isRtkOn;

  /**
   * 执行中的飞行任务的记录 ID
   * (飞行历史记录 ID)
   */
  private String flightMissionId;

  /**
   * 无人机动作
   */
  private String uavAction;

  /**
   * 飞行距离
   */
  private String flyDistance;

  /**
   * 相机模式 1-录像；2-拍照
   */
  private String lensMode;

}
