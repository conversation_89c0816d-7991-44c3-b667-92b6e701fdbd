package com.allcore.external.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 06 6月 2025
 */
@Data
public class AirportTaskDTO implements Serializable {

    private static final long serialVersionUID = 2355050966113514346L;

    private String taskName;

    /**
     * 巡检任务id - 巡检任务表
     */
    private String inspectionTaskId;

    /**
     * 文件guid - 航迹
     */
    private String fileGuid;

    /**
     * 设备id - 设备表
     */
    private String deviceId;

    private String filePath;

    /**
     * 设备类型
     */
    private String deviceType;

    /**
     * 设备排序
     */
    private Integer sort;

    /**
     * 站点id - 第三方飞控平台
     */
    private String siteId;
}
