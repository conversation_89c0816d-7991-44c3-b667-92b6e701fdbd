/*
 *      Copyright (c) 2018-2028, Chill <PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.allcore.external.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import com.allcore.common.base.ZxhcEntity;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import javax.validation.constraints.Size;

/**
 * 机巢表实体类
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
public class NestSaveOrUpdateDTO  {

	private static final long serialVersionUID = 1L;

	/**
	 * 机巢guid
	 */
	private Long id;
	private String machineGuid;
	/**
	 * 机巢名称
	 */
	@NotNull(message = "机巢名称不能为空")
	@Size(max = 100, message = "长度必须小于等于100")
	private String machineName;
	/**
	 * 机巢类型
	 */
	private String machineType;
	/**
	 * 机巢品牌
	 */
	private String machineBrand;
	/**
	 * 舱门开合类
	 */
	private String doorType;
	/**
	 * 机巢型号
	 */
	@NotNull(message = "机巢型号不能为空")
	@Size(max = 100, message = "长度必须小于等于100")
	private String machineModel;
	/**
	 * 机巢SN码
	 */
	@NotNull(message = "机巢SN码不能为空")
	@Size(max = 50, message = "长度必须小于等于100")
	private String machineSn;
	private String planeSn;
	/**
	 * 资产属性
	 */
	private String assetAttribute;
	/**
	 * 保管人
	 */
	@NotNull(message = "保管人不能为空")
	@Size(max = 20, message = "长度必须小于等于100")
	private String custodian;
	/**
	 * 巡检半径
	 */
	@Digits(integer = 10, fraction = 2,message = "保留2位小数")
	private Double inspectionRadius;
	/**
	 * 最高点高度
	 */
	@Digits(integer = 10, fraction = 2,message = "保留2位小数")
	private Double maxHeight;
	/**
	 * 巡航时间
	 */
	@Digits(integer = 10, fraction = 2,message = "保留2位小数")
	private Double cruiseTime;
	/**
	 * 高度
	 */
	@Digits(integer = 10, fraction = 2,message = "保留2位小数")
	private Double height;
	/**
	 * 经度
	 */
	private Double longitude;
	/**
	 * 纬度
	 */
	private Double latitude;


	@ApiModelProperty("访问密钥ID")
	private String accessKeyId;

	@ApiModelProperty("访问密钥")
	private String accessKeySecret;

	@ApiModelProperty("机巢服务地址")
	private String serviceAddress;


}
