package com.allcore.external.vo.sgc;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @program: bl
 * @description: 地图路径参数VO
 * @author: fanxiang
 * @create: 2025-05-08 09:45
 **/

@Data
public class PathsParamsVO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("ID")
    private String id;

    @ApiModelProperty("地图ID")
    private String agvMapId;

    @ApiModelProperty("开始标记点")
    private String startMarkerId;
    @ApiModelProperty("结束标记点")
    private String endMarkerId;
    @ApiModelProperty("开始点的控制点xy坐标")
    private String startControl;
    @ApiModelProperty("结束点的控制点xy坐标")
    private String endControl;
    @ApiModelProperty("长度")
    private Double length;
    @ApiModelProperty("线类型 1、直线 2、曲线（只要不为1,目前都默认为曲线）")
    private Integer lineType;
    @ApiModelProperty("方向 0、双向 1、正向 2、反向")
    private Integer direction;
    @ApiModelProperty("正向行驶时agv方向 1、正向 2、反向")
    private Integer forwardAgvDirection;
    @ApiModelProperty("反向行驶时agv方向 1、正向 2、反向")
    private Integer reverseAgvDirection;
    @ApiModelProperty("使用状态 ENABLE:启用 DISABLE:禁用")
    private String usageStatus;
    @ApiModelProperty("自动门id")
    private String autoDoorId;
    @ApiModelProperty("边路径参数")
    private List<SidePathVO> sidePathVOS;
    private String createTime;
    private String updateTime;

    @Data
    // SidePathVO class
    public static class SidePathVO {
        private String id;
        @ApiModelProperty("开始标记点")
        private String startMarkerId;
        @ApiModelProperty("结束标记点")
        private String endMarkerId;
        @ApiModelProperty("agv方向：0、双休 1、正向 2、反向")
        private int agvDirection;
        @ApiModelProperty("路径参数")
        private PathParam pathParam;

        @Data
        // PathParam class
        public static class PathParam {
            @ApiModelProperty(value = "扩展布尔")
            private String extend_bit;
            @ApiModelProperty(value = "扩展字符串")
            private String extend_string;
            @ApiModelProperty(value = "ID")
            private String id;
            @ApiModelProperty(value = "地图ID")
            private String agvMapId;
            @ApiModelProperty(value = "路径ID")
            private String sidePathId;
            @ApiModelProperty(value = "安全")
            private int safety;
            @ApiModelProperty(value = "前雷达避障减速区x>0 m")
            private int front_laser_dec_area_x;
            @ApiModelProperty(value = "前雷达避障减速区y>0 m")
            private int front_laser_dec_area_y;
            @ApiModelProperty(value = "前雷达避障停止区x>0 m")
            private int front_laser_stop_area_x;
            @ApiModelProperty(value = "前雷达避障停止区y>0 m")
            private int front_laser_stop_area_y;
            @ApiModelProperty(value = "后雷达避障减速区x>0 m")
            private int back_laser_dec_area_x;
            @ApiModelProperty(value = "后雷达避障减速区y>0 m")
            private int back_laser_dec_area_y;
            @ApiModelProperty(value = "后雷达避障停止区x>0 m")
            private int back_laser_stop_area_x;
            @ApiModelProperty(value = "后雷达避障停止区y>0 m")
            private int back_laser_stop_area_y;
            @ApiModelProperty(value = "最大平移速度>0 m/s")
            private int max_translation_speed;
            @ApiModelProperty(value = "最大旋转速度 rad/s >0")
            private int max_rotate_speed;
            @ApiModelProperty(value = "平移加速比例控制系数 >0")
            private int p;
            @ApiModelProperty(value = "平移加速微分控制系数 >0")
            private int D;
            @ApiModelProperty(value = "平移加（减）速度 m/s2 >0")
            private int translation_acc;
            @ApiModelProperty(value = "防跌落 1、开启 2、关闭")
            private int fall_prevent;
            @ApiModelProperty(value = "旋转加（减）速度 rad/s2 >0")
            private int rotate_acc;
            @ApiModelProperty(value = "创建时间")
            private String createTime;
            @ApiModelProperty(value = "修改时间")
            private String updateTime;
            @ApiModelProperty(value = "雷达避障区域 0、默认区域1、避障区域2...15、避障区域15")
            private int safety_scanner_region;
            @ApiModelProperty(value = "融合特征 1、开启 0、关闭")
            private int features_requested;

        }
    }
}