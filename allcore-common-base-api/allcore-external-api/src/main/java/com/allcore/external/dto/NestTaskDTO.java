/*
 * Copyright (c) 2018-2028, Chi<PERSON> All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without modification, are permitted provided that the
 * following conditions are met:
 *
 * Redistributions of source code must retain the above copyright notice, this list of conditions and the following
 * disclaimer. Redistributions in binary form must reproduce the above copyright notice, this list of conditions and the
 * following disclaimer in the documentation and/or other materials provided with the distribution. Neither the name of
 * the dreamlu.net developer nor the names of its contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission. Author: Chill 庄骞 (<EMAIL>)
 */
package com.allcore.external.dto;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 机巢配置指令表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
public class NestTaskDTO {
    private static final long serialVersionUID = 1L;

    @JsonSerialize(using = ToStringSerializer.class)
    @ApiModelProperty("主键id")
    private String id;

    private String inspectionTaskId;

    private String airportNestId;
    /**
     */
    private String taskStatus;
    /**
     */
    private String firstFlag;
    private String fileGuid;

    /**
     * 文件名称
     */
    private String fileName;

    private String taskName;

    /*机场额外参数*/
    private String siteId;
    private String filePath;
    private String snCode;
    @ApiModelProperty("访问密钥ID")
    private String accessKeyId;

    @ApiModelProperty("访问密钥")
    private String accessKeySecret;

    @ApiModelProperty("机巢服务地址")
    private String serviceAddress;

    public NestTaskDTO() {
        // 默认构造函数
    }

    public NestTaskDTO(String id, String inspectionTaskId, String airportNestId, String taskStatus, String firstFlag,
        String fileGuid, String fileName, String taskName, String filePath, String snCode, String accessKeyId,
        String accessKeySecret, String serviceAddress) {
        this.id = id;
        this.inspectionTaskId = inspectionTaskId;
        this.airportNestId = airportNestId;
        this.taskStatus = taskStatus;
        this.firstFlag = firstFlag;
        this.fileGuid = fileGuid;
        this.fileName = fileName;
        this.taskName = taskName;
        this.filePath = filePath;
        this.snCode = snCode;
        this.accessKeyId = accessKeyId;
        this.accessKeySecret = accessKeySecret;
        this.serviceAddress = serviceAddress;
    }
}
