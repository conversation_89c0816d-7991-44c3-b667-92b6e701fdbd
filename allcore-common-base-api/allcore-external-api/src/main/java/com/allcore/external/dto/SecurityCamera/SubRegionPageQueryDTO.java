package com.allcore.external.dto.SecurityCamera;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/7 13:48
 **/
@NoArgsConstructor
@Data
public class SubRegionPageQueryDTO {


    @ApiModelProperty(value = "资源类型，region：区域，camera：监控点",required = true)
    private String resourceType;

    @ApiModelProperty(value = "父级区域唯一编码")
    private String parentIndexCode;

    @ApiModelProperty(value = "当前页码；pageNo≥1",required = true)
    private Integer pageNo;

    @ApiModelProperty(value = "分页大小；0<pageSize≤1000",required = true)
    private Integer pageSize;

    @ApiModelProperty(value = "权限码集合，view:查看")
    private List<String> authCodes;

    @ApiModelProperty(value = "级联标识，\n" +
            "0-全部，\n" +
            "1-本级，\n" +
            "2-级联，\n" +
            "默认0")
    private Integer cascadeFlag;
}
