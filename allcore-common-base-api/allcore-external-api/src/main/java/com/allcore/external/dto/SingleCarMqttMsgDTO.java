package com.allcore.external.dto;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.allcore.common.base.ZxhcEntity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/16 16:48
 * @describe
 */
@Data
@TableName("single_car_mqtt_msg")
public class SingleCarMqttMsgDTO extends ZxhcEntity implements Serializable {
	private static final long serialVersionUID = 8184007418068993320L;

	@ApiModelProperty(value = "本条定位数据的定位时间")
	private String time_stamp;
	private String timeStamp;

	@ApiModelProperty(value = "设备序序列号")
	private String device_sn;
	private String deviceSn;

	@ApiModelProperty(value = "纬度 例：30.5663420")
	private Double lng;

	@ApiModelProperty(value = "纬度 例：30.5663420")
	private Double lat;

	@ApiModelProperty(value = "电池电量百分比")
	private Float battery;

	@ApiModelProperty(value = "高度， 单位是 m， 有正负号例：11.21")
	private Double high;

	@ApiModelProperty(value = "解状态")
	private Integer gps_type;
	private Integer gpsType;

	@ApiModelProperty(value = "厂商编号")
	private String dev_type;
	private String devType;

	@ApiModelProperty(value = "sos报警")
	private Integer sos_alarm;
	private Integer sosAlarm;

	public SingleCarMqttMsgDTO(String time_stamp) {
		this.time_stamp = time_stamp;
	}

	public SingleCarMqttMsgDTO() {
	}

	public void setTime_stamp(String time_stamp) {
		this.time_stamp = time_stamp;
		this.timeStamp = timeStamp;
	}

	public void setTimeStamp(String timeStamp) {
		this.timeStamp = timeStamp;
		this.time_stamp = time_stamp;
	}

	public void setDevice_sn(String device_sn) {
		this.device_sn = device_sn;
		this.deviceSn = deviceSn;
	}

	public void setDeviceSn(String deviceSn) {
		this.deviceSn = deviceSn;
		this.device_sn = device_sn;
	}

	public void setGps_type(Integer gps_type) {
		this.gps_type = gps_type;
		this.gpsType = gpsType;
	}

	public void setGpsType(Integer gpsType) {
		this.gpsType = gpsType;
		this.gps_type = gps_type;
	}

	public void setDev_type(String dev_type) {
		this.dev_type = dev_type;
		this.devType = devType;
	}

	public void setDevType(String devType) {
		this.devType = devType;
		this.dev_type = dev_type;
	}

	public void setSos_alarm(Integer sos_alarm) {
		this.sos_alarm = sos_alarm;
		this.sosAlarm = sosAlarm;
	}

	public void setSosAlarm(Integer sosAlarm) {
		this.sosAlarm = sosAlarm;
		this.sos_alarm = sos_alarm;
	}
}
