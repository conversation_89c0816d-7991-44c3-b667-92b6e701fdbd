package com.allcore.external.vo.SecurityCamera;

import com.fasterxml.jackson.annotation.JsonProperty;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2025/5/6 16:33
 **/
@NoArgsConstructor
@Data
public class RegionInfoVO {

    @JsonProperty("indexCode")
    @ApiModelProperty(value = "区域唯一标识码")
    private String indexCode;
    @JsonProperty("name")
    @ApiModelProperty(value = "区域名称")
    private String name;
    @JsonProperty("parentIndexCode")
    @ApiModelProperty(value = "父区域唯一标识码")
    private String parentIndexCode;
    @JsonProperty("treeCode")
    @ApiModelProperty(value = "树编号")
    private String treeCode;
    @JsonProperty("regionPath")
    @ApiModelProperty(value = "区域完整目录，含本节点，/进行分割，上级节点在前")
    private String regionPath;
    @JsonProperty("available")
    @ApiModelProperty(value = "用于标识区域节点是否有权限操作，true：有权限 false：无权限")
    private Boolean available;
    @JsonProperty("leaf")
    @ApiModelProperty(value = "true:是叶子节点，表示该区域下面未挂区域 false:不是叶子节点，表示该区域下面挂有区域")
    private Boolean leaf;
    @JsonProperty("cascadeCode")
    @ApiModelProperty(value = "级联平台标识，多个级联编号以@分隔，本级区域默认值“0”")
    private String cascadeCode;
    @JsonProperty("cascadeType")
    @ApiModelProperty(value = "区域标识 0：本级 1：级联 2：混合，下级推送给上级的本级点（杭州本级有滨江，然后下级滨江又把自己推送上来了，滨江是混合区域节点） 入参cascadeFlag与返回值对应： cascadeFlag=0：返回0、1、2 cascadeFlag=1：返回0、2 cascadeFlag=2：返回1、2")
    private Integer cascadeType;
    @JsonProperty("catalogType")
    @ApiModelProperty(value = "区域类型， 国标区域：0， 雪亮工程区域：1， 司法行政区域：2， 自定义区域：9， 历史兼容版本占用普通区域:10， 历史兼容版本占用级联区域:11， 楼栋单元:12")
    private Integer catalogType;
    @JsonProperty("externalIndexCode")
    @ApiModelProperty(value = "外码(如：国际码)")
    private String externalIndexCode;
    @JsonProperty("parentExternalIndexCode")
    @ApiModelProperty(value = "父外码(如：国际码)")
    private String parentExternalIndexCode;
    @JsonProperty("sort")
    @ApiModelProperty(value = "同级区域顺序")
    private Integer sort;
    @JsonProperty("localQuantity")
    @ApiModelProperty(value = "本区域资源数量(根据资源类型统计)，只统计本级挂的资源数量，不包含下级及下下级等")
    private Integer localQuantity;
    @JsonProperty("totalQuantity")
    @ApiModelProperty(value = "本区域及下级区域资源数量，包含本级及下级")
    private Integer totalQuantity;
    @JsonProperty("createTime")
    @ApiModelProperty(value = "创建时间，要求遵守ISO8601标准，如2018-07-26T21:30:08.322+08:00")
    private String createTime;
    @JsonProperty("updateTime")
    @ApiModelProperty(value = "创建时间，要求遵守ISO8601标准，如2018-07-26T21:30:08.322+08:00")
    private String updateTime;
}
