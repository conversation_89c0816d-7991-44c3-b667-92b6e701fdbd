package com.allcore.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.allcore.common.base.ZxhcEntity;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023/10/23 14:45
 * @describe
 */

@Data
public class MachineNestDTO extends ZxhcEntity implements Serializable {

	private static final long serialVersionUID = -7532976409140025046L;

	@ApiModelProperty(value = "机巢guid")
	private String machineGuid;

	@ApiModelProperty(value = "机巢名称")
	private String machineName;

	@ApiModelProperty(value = "机巢类型")
	private String machineType;

	@ApiModelProperty(value = "机巢品牌")
	private String machineBrand;

	@ApiModelProperty(value = "舱门开合类")
	private String doorType;

	@ApiModelProperty(value = "机巢型号")
	private String machineModel;

	@ApiModelProperty(value = "机巢SN码")
	private String machineSn;

	@ApiModelProperty(value = "资产属性")
	private String assetAttribute;

	@ApiModelProperty(value = "保管人")
	private String custodian;

	@ApiModelProperty(value = "巡检半径")
	private Double inspectionRadius;

	@ApiModelProperty(value = "最高点高度")
	private Double maxHeight;

	@ApiModelProperty(value = "巡航时间")
	private Double cruiseTime;

	@ApiModelProperty(value = "高度")
	private Double height;

	@ApiModelProperty(value = "经度")
	private Double longitude;

	@ApiModelProperty(value = "纬度")
	private Double latitude;

	@ApiModelProperty(value = "无人机SN")
	private String planeSn;

	@ApiModelProperty(value = "请求ID")
	private String accessKeyId;

	@ApiModelProperty(value = "请求密钥")
	private String accessKeySecret;

	@ApiModelProperty(value = "请求地址")
	private String serviceAddress;

}
