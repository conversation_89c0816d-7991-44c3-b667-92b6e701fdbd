package com.allcore.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@ApiModel(value="机巢操作指令参数", description="机巢操作指令参数")
@Data
public class MachineNestOperateDTO {

  @ApiModelProperty("机巢编码")
  private String machineNestCode;

  @ApiModelProperty("机巢操作指令：1开顶舱门 2舱门关闭 3一键起飞 4紧急备降 5自动返航 6机巢准备 7放电任务 8充电开启 9充电停止 10机场复位")
  private String instructKey;

  @ApiModelProperty("机巢品牌商服务配置")
  private MachineNestBrandConfigDTO configDTO;

}
