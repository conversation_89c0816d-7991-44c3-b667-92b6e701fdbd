/*
 *      Copyright (c) 2018-2028, <PERSON><PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.allcore.external.dto;

import com.allcore.external.entity.Nest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 机巢表数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-07-24
 */
@Data
public class NestDTO {
	private String deptCode;
	private String machineName;
	private String machineType;
	private String machineBrand;
	/**
	 * 0:空闲中 1:作业中
	 */
	private String nestStatus;
}
