package com.allcore.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Pattern;
import java.io.Serializable;

/**
 * <p>机场操作实体</p>
 *
 * @author: sunkun
 * Date: 07 5月 2025
 */
@Data
@ApiModel(value = "机场操作实体")
public class AirportOperationDTO implements Serializable {

    private static final long serialVersionUID = 5027898357459828733L;

    @ApiModelProperty(value = "站点id")
    private String siteId;

    @ApiModelProperty(value = "镜头（1.变焦 2.红外 3.广角）")
    private Integer actNum;

    @ApiModelProperty(value = "任务名称")
    private String missionName;

    @ApiModelProperty(value = "任务id")
    private String missionId;

    @ApiModelProperty(value = "航线文件路径")
    private String filePath;

    @ApiModelProperty(value = "指令 (1、开顶仓门 2、仓门关闭 3、自动返航)")
    private String instructKey;
}
