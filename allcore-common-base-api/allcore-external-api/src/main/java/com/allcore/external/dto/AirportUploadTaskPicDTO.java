package com.allcore.external.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 23 6月 2025
 */
@Data
@ApiModel(value = "飞控任务图片上传dto")
public class AirportUploadTaskPicDTO implements Serializable {

    private static final long serialVersionUID = 4354545059605428776L;

    @ApiModelProperty(value = "巡检任务id")
    @NotBlank(message = "巡检任务id 不能为空")
    private String inspectionTaskId;

    @ApiModelProperty(value = "设备id")
    @NotBlank(message = "设备id 不能为空")
    private String deviceId;

    @ApiModelProperty(value = "飞控平台任务id")
    @NotBlank(message = "飞控平台任务id不能为空")
    private String missionId;

    @ApiModelProperty("巡检机场任务id")
    @NotBlank(message = "巡检机场任务id 不能为空")
    private String airportTaskId;
}
