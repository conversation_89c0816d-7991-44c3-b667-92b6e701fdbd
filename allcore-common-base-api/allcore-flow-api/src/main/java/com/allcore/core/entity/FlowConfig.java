package com.allcore.core.entity;

import com.allcore.common.base.ZxhcEntity;
import com.allcore.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;

import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

/**
 * 业务流程设置实体类
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
@Data
@TableName("SYS_FLOW_CONFIG")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FlowConfig对象", description = "业务流程设置")
public class FlowConfig extends ZxhcEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 业务编码
     */
    @ApiModelProperty(value = "业务编码")
    private String busCode;
    /**
     * 业务名称
     */
    @ApiModelProperty(value = "业务名称")
    private String busName;
    /**
     * 流程定义主键
     */
    @ApiModelProperty(value = "流程定义主键")
    private String processDefinitionGuid;
//    /**
//     * 流程实例主键
//     */
//    @ApiModelProperty(value = "流程实例主键")
//    private String processInstanceGuid;

    @ApiModelProperty("业务状态")
    private Integer status;


}
