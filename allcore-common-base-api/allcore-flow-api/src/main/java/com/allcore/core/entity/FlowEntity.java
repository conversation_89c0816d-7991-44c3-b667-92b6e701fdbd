package com.allcore.core.entity;


import com.allcore.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * FlowEntity
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class FlowEntity extends BaseEntity {

	@TableField(exist = false)
	private AllcoreFlow flow;

	public AllcoreFlow getFlow() {
		if (flow == null) {
			flow = new AllcoreFlow();
		}
		return flow;
	}

}
