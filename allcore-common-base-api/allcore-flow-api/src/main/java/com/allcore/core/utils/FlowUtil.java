package com.allcore.core.utils;


import com.allcore.core.constant.ProcessConstant;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 工作流工具类
 *
 * <AUTHOR>
 */
public class FlowUtil {

	/**
	 * 定义流程key对应的表名
	 */
	private final static Map<String, String> BUSINESS_TABLE = new HashMap<>();

	static {
		BUSINESS_TABLE.put(ProcessConstant.LEAVE_KEY, "sys_process_leave");
	}

	/**
	 * 通过流程key获取业务表名
	 *
	 * @param key 流程key
	 */
	public static String getBusinessTable(String key) {
		String businessTable = BUSINESS_TABLE.get(key);
		if (Func.isEmpty(businessTable)) {
			throw new RuntimeException("流程启动失败,未找到相关业务表");
		}
		return businessTable;
	}

	/**
	 * 获取业务标识
	 *
	 * @param businessTable 业务表
	 * @param businessId    业务表主键
	 * @return businessKey
	 */
	public static String getBusinessKey(String businessTable, String businessId) {
		return StringUtil.format("{}:{}", businessTable, businessId);
	}

}
