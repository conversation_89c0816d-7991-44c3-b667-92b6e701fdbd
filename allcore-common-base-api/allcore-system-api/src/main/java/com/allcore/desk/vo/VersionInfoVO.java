package com.allcore.desk.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 版本升级信息视图实体类
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
public class VersionInfoVO {

	@ApiModelProperty("主键id")
	private String id;
	/**
	 * 文件Guid
	 */
	private String fileGuid;

	/**
	 * 文件Path
	 */
	private String filePath;

	/**
	 * 文件名称
	 */
	private String fileName;
	/**
	 * 版本更新日志
	 */
	private String description;
	/**
	 * 产品类型
	 */
	private String productType;

	/**
	 * 产品类型
	 */
	private String productTypeZh;

	/**
	 * 版本名
	 */
	private String versionName;
	/**
	 * 版本号
	 */
	private Integer versionCode;
	/**
	 * 否强制更新
	 */
	private String updateModel;

	/**
	 * 否强制更新
	 */
	private String updateModelZh;

	@DateTimeFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@JsonFormat(
		pattern = "yyyy-MM-dd HH:mm:ss"
	)
	@ApiModelProperty("创建时间")
	private Date createTime;

	@ApiModelProperty("创建人")
	private String createUser;

	@ApiModelProperty("创建人")
	private String createUserZh;


}
