package com.allcore.desk.entity;

import com.allcore.core.mp.base.BaseEntity;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 版本升级信息实体类
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Data
@TableName("product_version_info")
@EqualsAndHashCode(callSuper = true)
public class VersionInfo extends BaseEntity {

	private static final long serialVersionUID = 1L;

	/**
	 * 文件Guid
	 */
	private String fileGuid;
	/**
	 * 版本更新日志
	 */
	private String description;
	/**
	 * 产品类型
	 */
	private String productType;
	/**
	 * 版本名
	 */
	private String versionName;
	/**
	 * 版本号
	 */
	private Integer versionCode;
	/**
	 * 更新模式 强制更新 1  提示更新	0
	 */
	private String updateModel;
	/**
	 * 机构Code
	 */
	private String deptCode;


}
