package com.allcore.dict.vo;

import com.allcore.core.tool.node.INode;
import com.allcore.dict.entity.DictBiz;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.ArrayList;
import java.util.List;

/**
 * 视图实体类
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "DictBizVO对象", description = "DictBizVO对象")
public class DictBizVO extends DictBiz implements INode<DictBizVO> {
	private static final long serialVersionUID = 1L;
	/**
	 * 主键ID
	 */
	private String id;

	/**
	 * 父节点ID
	 */
	private String parentId;

	/**
	 * 子孙节点
	 */
	@JsonInclude(JsonInclude.Include.NON_EMPTY)
	private List<DictBizVO> children;

	@Override
	public List<DictBizVO> getChildren() {
		if (this.children == null) {
			this.children = new ArrayList<>();
		}
		return this.children;
	}

	/**
	 * 上级字典
	 */
	private String parentName;
}
