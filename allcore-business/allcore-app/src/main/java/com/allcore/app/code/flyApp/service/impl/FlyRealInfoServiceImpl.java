/*
 *      Copyright (c) 2018-2028, Chi<PERSON>ang All rights reserved.
 *
 *  Redistribution and use in source and binary forms, with or without
 *  modification, are permitted provided that the following conditions are met:
 *
 *  Redistributions of source code must retain the above copyright notice,
 *  this list of conditions and the following disclaimer.
 *  Redistributions in binary form must reproduce the above copyright
 *  notice, this list of conditions and the following disclaimer in the
 *  documentation and/or other materials provided with the distribution.
 *  Neither the name of the dreamlu.net developer nor the names of its
 *  contributors may be used to endorse or promote products derived from
 *  this software without specific prior written permission.
 *  Author: Chill 庄骞 (<EMAIL>)
 */
package com.allcore.app.code.flyApp.service.impl;


import com.allcore.app.code.flightsorties.entity.FlyRealInfo;
import com.allcore.app.code.flyApp.mapper.FlyRealInfoMapper;
import com.allcore.app.code.flyApp.service.IFlyRealInfoService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 移动端无人机实时位置信息表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-04-26
 */
@Service
public class FlyRealInfoServiceImpl extends ServiceImpl<FlyRealInfoMapper, FlyRealInfo> implements IFlyRealInfoService {



}
