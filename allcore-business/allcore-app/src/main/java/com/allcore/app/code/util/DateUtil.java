package com.allcore.app.code.util;

import com.alibaba.csp.sentinel.util.StringUtil;
import lombok.extern.slf4j.Slf4j;
import com.allcore.common.utils.CommonUtil;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.ParsePosition;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;

@SuppressWarnings("ALL")
@Slf4j
public class DateUtil {

    private static final Random jjj = new Random();

    /**
     * 将 int [1,2,3,5,8,9,10] 连续的分组成[1,2,3]、[5]、[8,9,10]
     * 再传入9 ，匹配上面的分组取出 [9,10]
     * @param NoNum 传入的整形数组
     * @return 返回含有匹配数字的数组
     */
    public static String convert(List<Integer> NoNum) {
        int state = 0;
        String result = "";
        for (int i = 0; i < NoNum.size(); i++)
        {
            if (i == NoNum.size() - 1){
                state = 2;
            }
            if (state == 0)
            {
                if (NoNum.get(i + 1) == NoNum.get(i) + 1)
                {
                    result += Integer.toString(NoNum.get(i));
                    result += "-";
                    state = 1;
                }
                else
                {
                    result += Integer.toString(NoNum.get(i));
                    result += ",";
                }
            }
            else if (state == 1)
            {
                if (NoNum.get(i + 1) != NoNum.get(i) + 1)
                {
                    result += Integer.toString(NoNum.get(i));
                    result += ",";
                    state = 0;
                } else {
                    result += NoNum.get(i)+"-";
                }
            }
            else
            {
                result += Integer.toString(NoNum.get(i));
            }
        }
        return result;

    }

    /**
     * 大-小    计算两个时间天数差
     * @param annualplanstarttine
     * @param annualplanendtine
     * @return
     */
    public static Integer getDifferenceFromTwoDate(String annualplanstarttine, String annualplanendtine) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format2 = new SimpleDateFormat("yyyy-MM-dd");
        int i = 0;
        try {
            Date date2 = format.parse(annualplanstarttine);
            Date date = format.parse(annualplanendtine);

            i = differentDaysByMillisecond(date, date2);
        } catch (ParseException e) {
            log.error("【计算两个日期的时间差出错】-{}", e);
        }
        return i;
    }

    /**
     * 通过时间秒毫秒数判断两个时间的间隔
     * @param date1
     * @param date2
     * @return
     */
    public static int differentDaysByMillisecond(Date date1,Date date2)
    {
        int days = (int) ((date2.getTime() - date1.getTime()) / (1000*3600*24));
        return days;
    }

    /**
     * 给定的时间是否在第二个时间之前，也即一个时间是否过期的比较方法
     * @param beforeTime
     * @param nowTime
     * @return
     */
    public static boolean beforeTheEnd(String beforeTime, String nowTime) {
        DateFormat df;
        beforeTime = beforeTime.trim();
        nowTime = nowTime.trim();
        if(beforeTime.length()!=nowTime.length()) {
            beforeTime = beforeTime.split(" ")[0];
            nowTime = nowTime.split(" ")[0];
        }
        // 带时分秒时间比较
        if (nowTime.length() >= 14) {
            df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
//            beforeTime = getStringDate();
        } else if(nowTime.length() < 14 && nowTime.length()>7){
            df = new SimpleDateFormat("yyyy-MM-dd");
        } else{
            df = new SimpleDateFormat("yyyy-MM");
        }


        try {
            Date dt1 = df.parse(beforeTime);
            Date dt2 = df.parse(nowTime);
            if (dt1.getTime() > dt2.getTime()) {
                return true;
            }
        } catch (ParseException e) {
            log.error("【比较两个时间异常】- {}", e);
        }
        return false;
    }

    /**
     * 给定的时间是否在第二个时间之前，也即一个时间是否过期的比较方法
     * @param beforeTime
     * @param nowTime
     * @return
     */
    public static boolean beforeTheEndDateFormat(Date beforeTime, Date nowTime) {
        if (beforeTime.getTime() >= nowTime.getTime()) {
            return true;
        } else {
            return false;
        }
    }


    /**
     * 获取某个月的天数
     * @param strDate 2012-01
     * @return 31
     */
    public static Integer getDaysFromMonth(String strDate) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date date = null;
        try {
            date = format.parse(strDate);
        } catch (ParseException e) {
            log.error("【日期解析错误】- {}", e);
        }
        Calendar calendar = new GregorianCalendar();
        calendar.setTime(date);
        int days1 = calendar.getActualMaximum(Calendar.DAY_OF_MONTH);
        return days1;
    }


    /**
     * 当年第一天
     *
     * @param date
     *            日期
     * @return 天
     * @throws Exception
     *             异常
     */
    public static String getThisYear(String date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format3 = new SimpleDateFormat("yyyy");
        Date time = null;
        String dates = null;
        try {
            time = format3.parse(date);
            String time1 = format3.format(time);
            Date startTime = format.parse(time1 + "-01-01");
            dates = format.format(startTime);
        } catch (ParseException e) {
            log.error("【日期解析异常】-{}", e);
        }
        return dates;
    }

    /**
     * 当年最后一天
     *
     * @param date
     *            日期
     * @return 天
     * @throws Exception
     *             异常
     */
    public static String getThisYearLastDay(String date) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat format3 = new SimpleDateFormat("yyyy");
        Date time = null;
        String dates = null;
        try {
            time = format3.parse(date);
            String time1 = format3.format(time);
            Date startTime = format.parse(time1 + "-12-31");
            dates = format.format(startTime);
        } catch (ParseException e) {
            log.error("【日期解析异常】-{}", e);
        }
        return dates;
    }



    /**
     * 当月第一天
     *
     * @param date
     *            天
     * @return 天
     * @throws ParseException
     *             异常
     */
    public static String getFirstDayToMonth(String date){
        // 获取截止当前天数
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM");
        Date nowDay = null;
        try {
            nowDay = format.parse(date);
        } catch (ParseException e) {
            log.error("【时间转化异常】- {}", e);
        }
        String time = format.format(nowDay);
        String firstDay = time + "-01";
        return firstDay;
    }

    /**
     * 当月最后一天
     *
     * @param date
     * @return yyyy-MM-dd
     * @throws ParseException
     *             异常
     */
    public static String getLastDayToMonth(String date){
        try {
            Calendar cal = Calendar.getInstance();
            //设置年份
            cal.set(Calendar.YEAR,Integer.valueOf(date.substring(0,4)));
            //设置月份
            cal.set(Calendar.MONTH, Integer.valueOf(date.substring(5,7))-1);
            //获取某月最大天数
            int lastDay = cal.getActualMaximum(Calendar.DAY_OF_MONTH);
            //设置日历中月份的最大天数
            cal.set(Calendar.DAY_OF_MONTH, lastDay);
            //格式化日期
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String lastDayOfMonth = sdf.format(cal.getTime());
            return lastDayOfMonth;
        } catch (Exception e) {
            log.error("【时间转化异常】- {}", e);
        }
        return null;
    }

//    public static void main(String[] args) {
//        Date currentTime = new Date();
//        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
//        String dateString = formatter.format(currentTime);
//        Date currentTime_2 = null;
//        try {
//            currentTime_2 = formatter.parse(dateString);
//        } catch (ParseException e) {
//            e.printStackTrace();
//        }
//        //System.out.println(currentTime_2);
//
//
//
//    }

    /*
     * /**
     *
     * @title: getNowDate
     *
     * @description:
     *
     * @return yyyy-MM-dd HH:mm:ss
     *
     * @throws:
     *
     * @date: 2013-10-23
     */
    public static Date getNowDate() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss",Locale.US);
        formatter.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        String dateString = formatter.format(currentTime);
        Date currentTime_2 = null;
        try {
            currentTime_2 = formatter.parse(dateString);
            return currentTime_2;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    public static String dateToString(Date date, String format) {

        SimpleDateFormat formatter = new SimpleDateFormat(format);
        return formatter.format(date);
    }

    /**
     * @return yyyy-MM-dd
     * @title: getNowDateShort
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static Date getNowDateShort() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(currentTime);
        Date currentTime_2 = null;
        try {
            currentTime_2 = formatter.parse(dateString);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return currentTime_2;
    }

    /**
     * @return yyyy-MM-dd HH:mm:ss
     * @title: getStringDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getStringDate() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss",  Locale.US);
        formatter.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @return yyyy-MM-dd HH:mm:ss
     * @title: getStringDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getStringDate(Date date) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(date);
        return dateString;
    }

    /**
     * @return 生成以年月日时分秒为编码的序列
     * @title: getStringDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static Long getStringDateCode() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(currentTime);
        return new Long(dateString);
    }

    /**
     * @return yyyy-MM-dd
     * @title: getStringDateShort
     * @description:
     * @throws:
     * @date: 2013-10-23
     */

    public static String getStringDateShort() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @return HH:mm:ss
     * @title: getTimeShort
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getTimeShort() {
        SimpleDateFormat formatter = new SimpleDateFormat("HH:mm:ss");
        Date currentTime = new Date();
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @param strDate
     * @return yyyy-MM-dd HH:mm:ss
     * @title: strToDateLong
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static Date strToDateLong(String strDate) {
        if (StringUtil.isEmpty(strDate)) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }

    /**
     * @param dateDate
     * @return yyyy-MM-dd HH:mm:ss
     * @title: dateToStrLong
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String dateToStrLong(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
        formatter.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 格式化时间
     *
     * @param dateStr
     * @return
     */
    public static String dateStrFormt(String dateStr) {
        String date = "";
        if (!StringUtil.isEmpty(dateStr)) {
            String[] data = dateStr.split(" ");
            date = data[0];
        }
        return date;
    }

    public static String dateToZHStr(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy年MM月dd日");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * @param dateDate
     * @return yyyy-MM-dd
     * @title: dateToStr
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String dateToStr(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    public static String dateTolongStr(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(dateDate);
        return dateString;
    }



    public static String dateToStr(Date dateDate, String format) {
        SimpleDateFormat formatter = new SimpleDateFormat(format);
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * @param dateDate
     * @return yyyy-MM-dd
     * @title: dateToStr
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String dateToStr2(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy/MM/dd");
        String dateString = formatter.format(dateDate);
        return dateString;
    }


    /**
     * @param strDate
     * @return
     * @title: strToDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static Date strToDate(String strDate) {
        if (StringUtil.isEmpty(strDate)) {
            return null;
        }
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(strDate, pos);
        return strtodate;
    }

    public static Date strToDate(String strDate, String format){
        if (StringUtil.isEmpty(strDate)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(format, Locale.US);
        sdf.setTimeZone(TimeZone.getTimeZone("GMT+8"));
//        SimpleDateFormat formatter = new SimpleDateFormat(format);
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = sdf.parse(strDate, pos);
        return strtodate;
    }

    /**
     * @return
     * @title: getNow
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static Date getNow() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss",Locale.US);
        formatter.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        String dateString = formatter.format(currentTime);
        try {
            Date currentTime_2 = formatter.parse(dateString);
            return currentTime_2;
        } catch (ParseException e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * @param day
     * @return
     * @title: getLastDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static Date getLastDate(long day) {
        Date date = new Date();
        long date_3_hm = date.getTime() - 3600000 * 24 * day;
        Date date_3_hm_date = new Date(date_3_hm);
        return date_3_hm_date;
    }

    /**
     * @return
     * @title: getStringToday
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getStringToday() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd HHmmss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @return
     * @title: getHour
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getHour() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        String hour;
        hour = dateString.substring(11, 13);
        return hour;
    }

    /**
     * @return
     * @title: getTime
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getTime() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        String min;
        min = dateString.substring(14, 16);
        return min;
    }

    /**
     * @return
     * @title: getCurrentDay
     * @description: 返回当前的DAY
     * @throws:
     * @date: 2013-11-21
     */
    public static String getCurrentDay() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        String min;
        min = dateString.substring(8, 10);
        return min;
    }

    public static String getDay(String strdate) {

        String min;
        min = strdate.substring(8, 10);
        return min;
    }

    public static String getMonth(String strdate) {

        String min;
        min = strdate.substring(5, 7);
        return min;
    }

    public static String getYear(String strdate) {
        String min;
        min = strdate.substring(0, 4);
        return min;
    }

    /**
     * @return
     * @title: getCurrentMonth
     * @description: 返回当前的MONTH
     * @throws:
     * @date: 2013-11-21
     */
    public static String getCurrentMonth() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        String min;
        min = dateString.substring(5, 7);
        return min;
    }


    /**
     * @return
     * @title: getCurrentYear
     * @description: 返回当前的year
     * @throws:
     * @date: 2013-11-21
     */
    public static String getCurrentYear() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        String min;
        min = dateString.substring(0, 4);
        return min;
    }

    /**
     * @param sformat
     * @return
     * @title: getUserDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getUserDate(String sformat) {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat(sformat);
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @param st1
     * @param st2
     * @return
     * @title: getTwoHour
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getTwoHour(String st1, String st2) {
        String[] kk = null;
        String[] jj = null;
        kk = st1.split(":");
        jj = st2.split(":");
        if (Integer.parseInt(kk[0]) < Integer.parseInt(jj[0])) {
            return "0";
        } else {
            double y = Double.parseDouble(kk[0]) + Double.parseDouble(kk[1]) / 60;
            double u = Double.parseDouble(jj[0]) + Double.parseDouble(jj[1]) / 60;
            if ((y - u) > 0) {
                return y - u + "";
            } else {
                return "0";
            }
        }
    }

    /**
     * @param sj1
     * @param sj2
     * @return
     * @title: getTwoDay
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getTwoDay(String sj1, String sj2) {
        SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
        long day = 0;
        try {
            Date date = myFormatter.parse(sj1);
            Date mydate = myFormatter.parse(sj2);
            day = (date.getTime() - mydate.getTime()) / (24 * 60 * 60 * 1000);
        } catch (Exception e) {
            return "";
        }
        return day + "";
    }

    /**
     * @param sj1
     * @param jj
     * @return
     * @title: getPreTime
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getPreTime(String sj1, String jj) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String mydate1 = "";
        try {
            Date date1 = format.parse(sj1);
            long Time = (date1.getTime() / 1000) + Integer.parseInt(jj) * 60;
            date1.setTime(Time * 1000);
            mydate1 = format.format(date1);
        } catch (Exception e) {
        }
        return mydate1;
    }

    /**
     * @param nowdate
     * @param delay
     * @return
     * @title: getNextDay
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getNextDay(String nowdate, String delay) {
        try {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            String mdate = "";
            Date d = strToDate(nowdate);
            if (d != null) {
                long myTime = (d.getTime() / 1000) + Integer.parseInt(delay) * 24 * 60 * 60;
                d.setTime(myTime * 1000);
                mdate = format.format(d);
            }
            return mdate;
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * @param ddate
     * @return
     * @title: isLeapYear
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static boolean isLeapYear(String ddate) {
        Date d = strToDate(ddate);
        GregorianCalendar gc = (GregorianCalendar) Calendar.getInstance();
        gc.setTime(d);
        int year = gc.get(Calendar.YEAR);
        if ((year % 400) == 0) {
            return true;
        } else if ((year % 4) == 0) {
            if ((year % 100) == 0) {
                return false;
            } else {
                return true;
            }
        } else {
            return false;
        }
    }

    /**
     * @param str
     * @return
     * @title: getEDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getEDate(String str) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        ParsePosition pos = new ParsePosition(0);
        Date strtodate = formatter.parse(str, pos);
        String j = strtodate.toString();
        String[] k = j.split(" ");
        return k[2] + k[1].toUpperCase() + k[5].substring(2, 4);
    }

    /**
     * @param dat
     * @return
     * @title: getEndDateOfMonth
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getEndDateOfMonth(String dat) {// yyyy-MM-dd
        String str = dat.substring(0, 8);
        String month = dat.substring(5, 7);
        int mon = Integer.parseInt(month);
        if (mon == 1 || mon == 3 || mon == 5 || mon == 7 || mon == 8 || mon == 10 || mon == 12) {
            str += "31";
        } else if (mon == 4 || mon == 6 || mon == 9 || mon == 11) {
            str += "30";
        } else {
            if (isLeapYear(dat)) {
                str += "29";
            } else {
                str += "28";
            }
        }
        return str;
    }

    /**
     * @param date1
     * @param date2
     * @return
     * @title: isSameWeekDates
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static boolean isSameWeekDates(Date date1, Date date2) {
        Calendar cal1 = Calendar.getInstance();
        Calendar cal2 = Calendar.getInstance();
        cal1.setTime(date1);
        cal2.setTime(date2);
        int subYear = cal1.get(Calendar.YEAR) - cal2.get(Calendar.YEAR);
        if (0 == subYear) {
            if (cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR)) {
                return true;
            }
        } else if (1 == subYear && 11 == cal2.get(Calendar.MONTH)) {

            if (cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR)) {
                return true;
            }
        } else if (-1 == subYear && 11 == cal1.get(Calendar.MONTH)) {
            if (cal1.get(Calendar.WEEK_OF_YEAR) == cal2.get(Calendar.WEEK_OF_YEAR)) {
                return true;
            }
        }
        return false;
    }

    /**
     * @return
     * @title: getSeqWeek
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getSeqWeek() {
        Calendar c = Calendar.getInstance(Locale.CHINA);
        String week = Integer.toString(c.get(Calendar.WEEK_OF_YEAR));
        if (week.length() == 1) {
            week = "0" + week;
        }
        String year = Integer.toString(c.get(Calendar.YEAR));
        return year + week;
    }

    /**
     * @param date1
     * @param date2
     * @return
     * @title: getDays
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static long getDays(String date1, String date2) {
        if (date1 == null || "".equals(date1)) {
            return 0;
        }
        if (date2 == null || "".equals(date2)) {
            return 0;
        }
        SimpleDateFormat myFormatter = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        Date mydate = null;
        try {
            date = myFormatter.parse(date1);
            mydate = myFormatter.parse(date2);
        } catch (Exception e) {
        }
        long day = 0;
        if (date != null && mydate != null) {
            day = (date.getTime() - mydate.getTime()) / (24 * 60 * 60 * 1000);
        }
        return day;
    }

    /**
     * @param k
     * @return
     * @title: getNo
     * @description: 使用日期加随机数产生NO
     * @throws:
     * @date: 2013-10-23
     */
    public static String getNo(int k) {
        return getUserDate("yyyyMMddhhmmss") + getRandom(k);
    }

    /**
     * @param i
     * @return
     * @title: getRandom
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static String getRandom(int i) {
        // int suiJiShu = jjj.nextInt(9);
        if (i == 0) {
            return "";
        }
        String jj = "";
        for (int k = 0; k < i; k++) {
            jj = jj + jjj.nextInt(9);
        }
        return jj;
    }

    /**
     * @param date
     * @return
     * @title: RightDate
     * @description:
     * @throws:
     * @date: 2013-10-23
     */
    public static boolean RightDate(String date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        ;
        if (date == null) {
            return false;
        }
        if (date.length() > 10) {
            sdf = new SimpleDateFormat("yyyy-MM-dd hh:mm:ss");
        } else {
            sdf = new SimpleDateFormat("yyyy-MM-dd");
        }
        try {
            sdf.parse(date);
        } catch (ParseException pe) {
            return false;
        }
        return true;
    }

    /*
     * @title: GetDay
     *
     * @description: 获取date1-date2两个日期直接的差值（按天计算）
     *
     * @param date1 日期
     *
     * @param date2 日期
     *
     * @return 天数
     *
     * @date: 2014-02-19
     */
    public static int getDayNum(Date date1, Date date2) {
        long day = 24L * 60L * 60L * 1000L;
        Long time1 = null;
        Long time2=null;
        if(!CommonUtil.isEmpty(date1)) {
            time1 = date1.getTime();
        }
        if(!CommonUtil.isEmpty(date2)) {
            time2 = date2.getTime();
        }


        return (int) ((time1 - time2) / day);
    }

    /**
     * 使用默认连接符连接年月日（默认为“-”连接）
     *
     * @param year
     * @param month
     * @param day
     * @return
     * @title: unionDate
     * @description:
     * @throws:
     * @date: 2014-6-6
     */
    public static String unionDate(String year, String month, String day) {
        // 使用默认连接
        return unionDate(year, month, day, null);
    }

    /**
     * 使用指定连接符进行连接
     *
     * @param year
     * @param month
     * @param day
     * @param union
     * @return
     * @title: unionDate
     * @description:
     * @throws:
     * @date: 2014-6-6
     */
    public static String unionDate(String year, String month, String day, String union) {
        StringBuilder sb = new StringBuilder();
        year = (year == null) ? "0000" : year;
        month = (month == null) ? "00" : month;
        day = (day == null) ? "00" : day;
        // 默认使用-连接
        union = (union == null) ? "-" : union;
        return sb.append(year).append(union).append(month).append(union).append(day).toString();
    }

    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime   当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return
     * <AUTHOR>
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }

        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取两个时间的相距分钟数
     * @param dt1
     * @param dt2
     * @return
     */
    public static Long getTimeDiffMinute(String dt1, String dt2) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss", Locale.US);
        format.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        Long minCount = 0L;
        try {
            Date date1 = format.parse(dt1);
            Date date2 = format.parse(dt2);
            long time = date2.getTime() - date1.getTime();
            minCount = time / 1000 / 60;
        } catch (Exception e) {
        }
        return minCount;
    }


    /**
     * 获取两个时间的分钟数
     * @param starttime
     * @param endtime
     * @return
     */
    public static Integer getDifferenceMinutesFromTwoDate(String starttime, String endtime) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //从对象中拿到时间
        long start = 0;
        long end = 0;
        try {
            start = df.parse(starttime).getTime();
            end = df.parse(endtime).getTime();
            long diff = (end - start) / 1000 / 60;
            return Math.toIntExact(diff);
        } catch (ParseException e) {
            log.error("【计算两个字符串类型的分钟差异常】- {}", e);
        }
        return null;
    }

    /**
     * 获取两个时间相差秒数
     * @param starttime
     * @param endtime
     * @return
     */
    public static Integer getDifferenceSecondsFromTwoDate(String starttime, String endtime) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //从对象中拿到时间
        long start = 0;
        long end = 0;
        try {
            start = df.parse(starttime).getTime();
            end = df.parse(endtime).getTime();
            long diff = (end - start) / 1000;
            return Math.toIntExact(diff);
        } catch (ParseException e) {
            log.error("【计算两个字符串类型的分钟差异常】- {}", e);
        }
        return null;
    }

    /**
     * 获取两个时间相差秒数
     * @param starttime
     * @param endtime
     * @return
     */
    public static Integer getDifferenceSecondsFromTwoDate(Date starttime, Date endtime) {
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        //从对象中拿到时间
        long start = 0;
        long end = 0;
        start = starttime.getTime();
        end = endtime.getTime();
        long diff = (end - start) / 1000;
        return Math.toIntExact(diff);
    }

    /**
     * 指定时间的amountToAdd天后的日期
     * @param year 年
     * @param month 月
     * @param day 日
     * @param hour 时
     * @param minute 分
     * @param second 秒
     * @param amountToAdd 天数
     * @param df 格式
     * @return
     */
    public static String getAmountDate(int year, int month, int day, int hour, int minute, int second, int amountToAdd, DateTimeFormatter df){
        LocalDateTime now = LocalDateTime.of(year,month,day,hour,minute,second);
        now = now.plus(amountToAdd, ChronoUnit.DAYS);
        return now.format(df).toString();
    }

    /**
     * 增加时间范围交叉查询（两段时间有交集）
     * @param criteria
     * @param startColName
     * @param endColName
     * @param startTime
     * @param endTime
     */
    public static void getAddBeginAndEndTimeCross(Object criteria,String startColName, String endColName,
                                                    String startTime, String endTime) {
        ClassLoader classLoader = Thread.currentThread().getContextClassLoader();
        String timeCrossCondition = "(" + "( "+startColName+" >= '" + startTime + "' and "+startColName+" <= '" + endTime + "' )" +
                " or " +
                "( "+endColName+" >= '" + startTime + "' and "+endColName+" <= '" + endTime + "' )" +
                " or " +
                "( "+endColName+" >= '" + startTime + "' and "+startColName+" <= '" + startTime + "' )" +
                ")";
        String clsName = criteria.getClass().getName();
        String methodName = "addCriterion";
        try {
            Class aClass = classLoader.loadClass(clsName);
            Method addCriterion = aClass.getDeclaredMethod(methodName);
            addCriterion.setAccessible(true);
            addCriterion.invoke(criteria, timeCrossCondition);
        } catch (ClassNotFoundException e) {
            log.error(clsName,e);
        } catch (NoSuchMethodException e) {
            log.error(clsName+":" + methodName,e);
        } catch (IllegalAccessException e) {
            log.error("查询异常",e);
        } catch (InvocationTargetException e) {
            log.error("查询异常",e);
        }
    }





    /**
     *
     * <p>Description: 本地时间转化为UTC时间</p>
     * @param localTime
     * @return
     * <AUTHOR>
     * @date  2018年10月19日 下午2:23:43
     *
     */
    public static Date localToUTC(String localTime) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date localDate= null;
        try {
            localDate = sdf.parse(localTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        long localTimeInMillis=localDate.getTime();
        /** long时间转换成Calendar */
        Calendar calendar= Calendar.getInstance();
        calendar.setTimeInMillis(localTimeInMillis);
        /** 取得时间偏移量 */
        int zoneOffset = calendar.get(Calendar.ZONE_OFFSET);
        /** 取得夏令时差 */
        int dstOffset = calendar.get(Calendar.DST_OFFSET);
        /** 从本地时间里扣除这些差量，即可以取得UTC时间*/
        calendar.add(Calendar.MILLISECOND, -(zoneOffset + dstOffset));
        /** 取得的时间就是UTC标准时间 */
        Date utcDate=new Date(calendar.getTimeInMillis());
        return utcDate;
    }

    /**
     *
     * <p>Description:UTC时间转化为本地时间 </p>
     * @param utcTime
     * @return:Date
     * <AUTHOR> @date  2018年10月19日 下午2:23:24
     *
     */
    public static Date utcToLocal(String utcTime){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = sdf.parse(utcTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        sdf.setTimeZone(TimeZone.getDefault());
        Date locatlDate = null;
        String localTime = sdf.format(utcDate.getTime());
        try {
            locatlDate = sdf.parse(localTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return locatlDate;
    }

    /**
     *
     * <p>Description:UTC时间转化为本地时间 </p>
     * @param utcTime
     * @return:String
     * <AUTHOR> @date
     *
     */
    public static String utcToLocalToString(String utcTime){
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMddHHmmss");
        sdf.setTimeZone(TimeZone.getTimeZone("UTC"));
        Date utcDate = null;
        try {
            utcDate = sdf.parse(utcTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        sdf.setTimeZone(TimeZone.getDefault());
        Date locatlDate = null;
        String localTime = sdf.format(utcDate.getTime());
        return localTime;
    }

    /**
     * @param dateDate
     * @return yyyy-MM
     * @title: dateToStr
     * @description:
     * @throws:
     * @date:
     */
    public static String getYearMonth(Date dateDate) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM");
        String dateString = formatter.format(dateDate);
        return dateString;
    }

    /**
     * 获取本周的第一天
     * @return String
     * **/
    public static String getWeekStart(){
        Calendar cal=Calendar.getInstance();
        cal.add(Calendar.WEEK_OF_MONTH, 0);
        cal.set(Calendar.DAY_OF_WEEK, 2);
        Date time=cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time)+" 00:00:00";
    }
    /**
     * 获取本周的最后一天
     * @return String
     * **/
    public static String getWeekEnd(){
        Calendar cal=Calendar.getInstance();
        cal.set(Calendar.DAY_OF_WEEK, cal.getActualMaximum(Calendar.DAY_OF_WEEK));
        cal.add(Calendar.DAY_OF_WEEK, 1);
        Date time=cal.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time)+" 23:59:59";
    }

    /**
     *获取本月的第一天
     * @return
     */
    public static String getMonthStart(){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMinimum(Calendar.DAY_OF_MONTH));
        Date time=calendar.getTime();
        return new SimpleDateFormat("yyyy-MM-dd").format(time)+" 00:00:00";
    }

    public static String getMonthEnd() {
        Calendar calendar2 = Calendar.getInstance();
        calendar2.setTime(new Date());
        calendar2.set(Calendar.DAY_OF_MONTH, calendar2.getActualMaximum(Calendar.DAY_OF_MONTH));
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(calendar2.getTime())+" 23:59:59";
    }

    /**
     * 计算指定日期多少天之前的日期
     * @param date
     * @param affter
     * @return
     */
    public static Date getBeforeDate(Date date, int before){
        LocalDateTime now = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        now = now.minus(before, ChronoUnit.DAYS);
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 计算指定日期多少天之后的日期
     * @param date
     * @param affter
     * @return
     */
    public static Date getAfterByDate(Date date, int after){
        LocalDateTime now = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        now = now.plus(after, ChronoUnit.DAYS);
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }

    //根据增加或减少的天数获取时间
    public static Date getDate(int day) {
        //获取当前时间
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //当前时间减去天
        calendar.add(Calendar.DAY_OF_MONTH, day);
        //获取时间
        return calendar.getTime();
    }

    //当天的0点
    public static Date initDateByDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        return calendar.getTime();
    }
    /**
     * 计算指定日期多少小时之后的日期
     * @param date
     * @param affter
     * @return
     */
    public static Date getAfterDateByHour(Date date, int after){
        LocalDateTime now = LocalDateTime.ofInstant(date.toInstant(), ZoneId.systemDefault());
        now = now.plus(after, ChronoUnit.HOURS);
        return Date.from(now.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 时间相减 计算天数
     * @param beforeDate
     * @param currentDate
     * @return
     */
    public static int differentDays(Date beforeDate, Date currentDate) {
        Calendar cal1 = Calendar.getInstance();
        cal1.setTime(beforeDate);

        Calendar cal2 = Calendar.getInstance();
        cal2.setTime(currentDate);
        int day1 = cal1.get(Calendar.DAY_OF_YEAR);
        int day2 = cal2.get(Calendar.DAY_OF_YEAR);

        int year1 = cal1.get(Calendar.YEAR);
        int year2 = cal2.get(Calendar.YEAR);
        //同一年
        if (year1 != year2)
        {
            int timeDistance = 0;
            for (int i = year1; i < year2; i++) {
                //闰年
                if (i % 4 == 0 && i % 100 != 0 || i % 400 == 0)
                {
                    timeDistance += 366;
                }
                //不是闰年
                else
                {
                    timeDistance += 365;
                }
            }
            return timeDistance + (day2 - day1);
        }
        //不同年
        else
        {
            return day2 - day1;
        }
    }
    /**
     * 获取当天第一刻的时间
     * @return
     */
    public static Date getTodayFirstMoment() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date zero = calendar.getTime();
        return zero;
    }

    /**
     * 获取当天最后一刻的时间
     * @return
     */
    public static Date getTodayLastMoment() {
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(new Date());
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date zero = calendar.getTime();
        return zero;
    }
    /**
     * @return
     * @title: getCurrentYear
     * @description: 返回当前的年月
     * @throws:
     * @date: 2013-11-21
     */
    public static String getCurrentYearMonth() {
        Date currentTime = new Date();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        String min;
        min = dateString.substring(0, 7);
        return min;
    }

    /**
     *获取当前时间一个月之前的时间
     * 格式：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getMonthDay(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        //过去一月
        c.setTime(new Date());
        c.add(Calendar.MONTH, -1);
        Date m = c.getTime();
        return format.format(m);
    }

    /**
     *获取当前时间一周之前的时间
     * 格式：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getWeekDay(){
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        //过去七天
        c.setTime(new Date());
        c.add(Calendar.DATE, - 7);
        Date d = c.getTime();
        return format.format(d);
    }
    /**
     *获取当天最后一刻的时间一个月之前的时间
     * 格式：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getMonthDayByLastMoment(){
        Date date = getTodayLastMoment();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        //过去一月
        c.setTime(date);
        c.add(Calendar.MONTH, -1);
        Date m = c.getTime();
        return format.format(m);
    }

    /**
     *获取当前时间一周之前的时间
     * 格式：yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static String getWeekDayByLastMoment(){
        Date date = getTodayLastMoment();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Calendar c = Calendar.getInstance();
        //过去七天
        c.setTime(date);
        c.add(Calendar.DATE, - 7);
        Date d = c.getTime();
        return format.format(d);
    }

    /**
     * 计算2个日期之间相差的  以年、月、日为单位，各自计算结果是多少
     * 比如：2011-02-02 到  2017-03-02
     *                                以年为单位相差为：6年
     *                                以月为单位相差为：73个月
     *                                以日为单位相差为：2220天
     * @param fromDate
     * @param toDate
     * @return
     */
//    public static DayCompare dayCompare(Date fromDate, Date toDate){
//        Calendar  from  =  Calendar.getInstance();
//        from.setTime(fromDate);
//        Calendar  to  =  Calendar.getInstance();
//        to.setTime(toDate);
//        //只要年月
//        int fromYear = from.get(Calendar.YEAR);
//        int fromMonth = from.get(Calendar.MONTH);
//
//        int toYear = to.get(Calendar.YEAR);
//        int toMonth = to.get(Calendar.MONTH);
//
//        int year = toYear  -  fromYear;
//        int month = toYear *  12  + toMonth  -  (fromYear  *  12  +  fromMonth);
//        int day = (int) ((to.getTimeInMillis()  -  from.getTimeInMillis())  /  (24  *  3600  *  1000));
//        return DayCompare.builder().day(day).month(month).year(year).build();
//    }

}
