package com.allcore.app.code.solve.service;

import com.allcore.app.code.flightsorties.vo.AreaInfo;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.inspection.vo.InspectionDefectVO;
import com.allcore.main.code.solve.vo.RemoveTaskDefectAppVO;
import com.allcore.main.code.source.vo.PvAreaVO;
import com.allcore.main.code.system.vo.SelectBoxVo;
import com.allcore.statistic.code.largescreen.vo.StationDefectCollectVO;
import com.allcore.statistic.code.largescreen.vo.StationPvInfoWithDefectOfflineVO;

import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-11-01
 */
public interface IFirstPageService {


    /**
     * （当前场站的第一个区域）
     * <AUTHOR>
     * @date 2024/01/10 17:01
     * @return com.allcore.app.code.flightsorties.vo.AreaInfo
     */
    AreaInfo areaInfo();

    /**
     * （缺陷统计）
     * <AUTHOR>
     * @date 2024/01/10 17:01
     * @return com.allcore.statistic.code.largescreen.vo.StationDefectCollectVO
     */
    StationDefectCollectVO defectCollect();

    /**
     * （区域的缺陷类型）
     * <AUTHOR>
     * @date 2024/01/10 17:01
     * @param pvAreaId
     * @param inspectionTaskId
     * @return java.util.List<com.allcore.main.code.system.vo.SelectBoxVo>
     */
    List<SelectBoxVo> defectDescriptionSelect(String pvAreaId,String inspectionTaskId);

    /**
     * @description: 离线下载详情
     * @author: liangyi
     * @date: 2024/8/9 9:21
     **/
    R<List<StationPvInfoWithDefectOfflineVO>> offlineDefect(String pvAreaIds);


    /**
     * （光伏区域下拉）
     * <AUTHOR>
     * @date 2024/01/10 17:02
     * @return java.util.List<com.allcore.main.code.source.vo.PvAreaVO>
     */
    List<PvAreaVO> select();


    /**
     * （任务列表）
     * <AUTHOR>
     * @date 2024/01/10 17:02
     * @param removeTaskStatus
     * @param timeType
     * @return java.util.List<com.allcore.main.code.solve.vo.RemoveTaskAppVO>
     */
    List<RemoveTaskDefectAppVO> removeTaskList(String removeTaskStatus, String timeType);


    /**
     * 多查缺陷详情
     *
     * @param inspectionPictureTaggingIds
     * @return
     */
    R<List<InspectionDefectVO>> defectDetailList(String inspectionPictureTaggingIds);
}
