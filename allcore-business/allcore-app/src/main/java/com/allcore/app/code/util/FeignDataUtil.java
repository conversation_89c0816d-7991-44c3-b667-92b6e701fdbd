package com.allcore.app.code.util;


import com.allcore.core.tool.api.R;
import com.allcore.core.tool.api.ResultCode;

/**
 * <AUTHOR>
public class FeignDataUtil {

    private static final String EMPTY_MSG = "暂无承载数据";

    /**
     * 开启序列化后，msg为"暂无承载数据"时，将data置null
     * @param result
     * @param <T>
     * @return
     */
    public static <T> R<T> format(R<T> result){
        if (result.getCode() == ResultCode.SUCCESS.getCode() && result.isSuccess() && EMPTY_MSG.equals(result.getMsg())){
            result.setData(null);
        }
        return result;
    }
}
