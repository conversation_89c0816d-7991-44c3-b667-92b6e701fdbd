package com.allcore.isc.config;

import com.xxl.job.core.executor.impl.XxlJobSpringExecutor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.cloud.commons.util.InetUtils;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.Resource;

/**
 * xxl-job config
 *
 * <AUTHOR> 2017-04-28
 */
@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = "job", value = "enable", matchIfMissing = true)
public class XxlJobConfig {
	private final Logger logger = LoggerFactory.getLogger(XxlJobConfig.class);

	@Value("${xxl.job.admin.addresses}")
	private String adminAddresses;

	@Value("${xxl.job.executor.appname}")
	private String appName;


	@Resource
	private InetUtils inetUtils;

	@Value("${xxl.job.executor.port}")
	private int port;

	@Value("${xxl.job.accessToken}")
	private String accessToken;

	@Value("${xxl.job.executor.logpath}")
	private String logPath;

	@Value("${xxl.job.executor.logretentiondays}")
	private int logRetentionDays;


	@Bean
	public XxlJobSpringExecutor xxlJobExecutor() {
		logger.info(">>>>>>>>>>> xxl-job config init.");
		String ip_ = inetUtils.findFirstNonLoopbackHostInfo().getIpAddress();
		XxlJobSpringExecutor xxlJobSpringExecutor = new XxlJobSpringExecutor();
		xxlJobSpringExecutor.setAdminAddresses(adminAddresses);
		xxlJobSpringExecutor.setAppName(appName);
		xxlJobSpringExecutor.setIp(ip_);
		xxlJobSpringExecutor.setPort(port);
		xxlJobSpringExecutor.setAccessToken(accessToken);
		xxlJobSpringExecutor.setLogPath(logPath);
		xxlJobSpringExecutor.setLogRetentionDays(logRetentionDays);

		return xxlJobSpringExecutor;
	}

	/**
	 * 针对多网卡、容器内部署等情况，可借助 "spring-cloud-commons" 提供的 "InetUtils" 组件灵活定制注册IP；
	 *
	 *      1、引入依赖：
	 *          <dependency>
	 *             <groupId>org.springframework.cloud</groupId>
	 *             <artifactId>spring-cloud-commons</artifactId>
	 *             <version>${version}</version>
	 *         </dependency>
	 *
	 *      2、配置文件，或者容器启动变量
	 *          spring.cloud.inetutils.preferred-networks: 'xxx.xxx.xxx.'
	 *
	 *      3、获取IP
	 *          String ip_ = inetUtils.findFirstNonLoopbackHostInfo().getIpAddress();
	 */


}
