package com.allcore.isc.service;

import com.allcore.core.tool.api.AppR;
import com.allcore.isc.vo.IscUserVO;
import com.allcore.user.entity.User;
import com.sgcc.isc.core.orm.organization.BusinessOrganization;
import com.sgcc.isc.core.orm.organization.OrganizationNature;
import com.sgcc.isc.ualogin.client.vo.IscSSOUserBean;

import java.util.List;

/**
 * （isc调用）
 * <AUTHOR>
 * @date 2022/08/08 15:26
 * @return
 */
public interface IIscService {

	/**
	 * （ticket isc登录跳转）
	 * <AUTHOR>
	 * @date 2022/08/08 15:25
	 * @param ticket
	 * @return com.allcore.platform.vo.IscUserVO
	 */
	IscUserVO iscLogin(String ticket);
	String userLoginAuthIsc(String loginName, String passWord);


	/**
	 * （机构初始化）
	 * <AUTHOR>
	 * @date 2022/08/08 15:26
	 * @return com.sgcc.isc.core.orm.complex.BusiOrgNode
	 */
	boolean queryDeptInfoList();

	/**
	 * （获取单元性质名称）
	 * <AUTHOR>
	 * @date 2022/09/08 13:14
	 * @param id
	 * @return java.lang.String
	 */
	String getNatureIdZh(String id);


	/**
	 * （查询所有不同的单位性质名称）
	 * <AUTHOR>
	 * @date 2022/09/08 13:14
	 * @return java.util.List<com.sgcc.isc.core.orm.organization.OrganizationNature>
	 */
	List<OrganizationNature> queryNatureInfosForDictBiz();

	/**
	 * （根据id查询机构-isc）
	 * <AUTHOR>
	 * @date 2022/09/20 17:04
	 * @param id
	 * @return java.util.List<com.sgcc.isc.core.orm.organization.BusinessOrganization>
	 */
	List<BusinessOrganization> queryDeptInfoById(String id);

	/**
	 * （根据 iscUserId获取用户 没有就创建）
	 * <AUTHOR>
	 * @date 2022/09/21 09:00
	 * @param iscSsoUserBean
	 * @return com.allcore.system.user.entity.User
	 */
	User getUser(IscSSOUserBean iscSsoUserBean);

	/**
	 * （isc ticket校验并获取userId等信息）
	 * <AUTHOR>
	 * @date 2022/09/21 10:44
	 * @param ticket
	 * @return com.sgcc.isc.ualogin.client.vo.IscSSOUserBean
	 */
	IscSSOUserBean iscValidate(String ticket);

    Boolean queryUserList();

	Boolean queryUser(String param);

    AppR pcLoginIsc(String loginName, String passWord, String deviceGuid, String fromType);
}
