package com.allcore.isc.controller;

import com.alibaba.fastjson.JSONObject;
import com.allcore.common.cache.CacheNames;
import com.allcore.common.config.IndexConfig;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.utils.AESCrypt;
import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.AppR;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.ObjectUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.desk.feign.IDeskClient;
import com.allcore.desk.vo.WorkerLoginResponse;
import com.allcore.isc.service.IIgwService;
import com.allcore.isc.service.IIscService;
import com.allcore.isc.vo.IscUserVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.openfeign.SpringQueryMap;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * isc登录认证
 *
 * <AUTHOR>
 * @date 2022/08/01 11:03
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/isc")
@Api(value = "ISC", tags = "ISC接口")
@Slf4j
public class IscController extends AllcoreController {

	private final IIscService iIscService;
	private final IIgwService igwService;

	private final IndexConfig indexConfig;

	private AllcoreRedis allcoreRedis;

	private IDeskClient deskClient;


	/**
	 * 增强创建账号逻辑
	 * @param ticket
	 * @param response
	 * @return
	 * @throws IOException
	 *
	 * isc登录后传入ticket
	 */
	@GetMapping("/verifyIscLogin")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ticket跳转isc模式首页", notes = "首页跳转")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "ticket", value = "ticket", paramType = "query", dataType = "string")
	})
	public void iscLogin(@RequestParam String ticket, HttpServletResponse response) {
		IscUserVO rst = iIscService.iscLogin(ticket);
		try {
			if(ObjectUtil.isEmpty(rst)){
				response.sendRedirect(indexConfig.getServer()+"/login?service="+indexConfig.getService());
			}else{
				response.sendRedirect(indexConfig.getIscIndex()+"?ticket="+ticket);
			}
		} catch (IOException e) {
			throw new ServiceException("重定向异常");
		}

	}

	@GetMapping("/getUserByTicket")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据ticket获取登录参数", notes = "根据ticket获取isc模式登录参数")
	@ApiImplicitParams({
			@ApiImplicitParam(name = "ticket", value = "ticket", paramType = "query", dataType = "string")
	})
	public R<IscUserVO> getUserByTicket(@RequestParam String ticket) {
		String rst = allcoreRedis.get(CacheNames.ISC_TICKET + ticket);
		return R.data(JSONObject.parseObject(rst,IscUserVO.class));
	}


	/**
	 * app用户登录  app系统专用
	 * isc模式
	 *  loginName
	 *  passWord
	 *  appCode
	 *  verifyCode
	 * @param request
	 * @param  param json字符串 AES加密(包含loginName、passWord、appCode、verifyCode)
	 * @return
	 */
	@RequestMapping(value = "/loginIsc", produces = "application/json;charset=UTF-8")
	@ResponseBody
	public AppR loginIsc(@RequestParam(value = "param") String param,
								 @SpringQueryMap HttpServletRequest request){
		AppR<WorkerLoginResponse> data = new AppR<WorkerLoginResponse>();
		try {
			log.info("app用户登录接口获取加密后数据串：{}",param);
			param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY,param);
			JSONObject paramMap = JSONObject.parseObject(param);
			String ticket = paramMap.getString("ticket");
			log.info("新i国网根据ticket获取用户信息,ticket为:{}",ticket);
			//新i国网根据ticket获取用户信息
			IscUserVO user = igwService.igwLogin(ticket);
			if (ObjectUtil.isEmpty(user)){
				log.error("i国网登录失败===============");
				return AppR.fail("登录失败");
			}else {
				data = deskClient.androidLoginApp(user.getTenantId(),user.getUsername(),ticket);
			}
			return data;
		} catch (Exception e) {
			log.info("【app用户登录方法异常】-> {}", e);
			return AppR.fail("app用户登录方法异常");
		}
	}



	/**
	 * app用户登录  app系统专用
	 * 默认登录模式
	 *  loginName
	 *  passWord
	 *  appCode
	 *  verifyCode
	 * @param request
	 * @param  param json字符串 AES加密(包含loginName、passWord、appCode、verifyCode)
	 * @return
	 */
	@RequestMapping(value = "/loginPcIsc", produces = "application/json;charset=UTF-8")
	@ResponseBody
	public AppR loginPcIsc(@RequestParam(value = "param") String param,
							 @SpringQueryMap HttpServletRequest request){
		AppR<WorkerLoginResponse> data = new AppR<WorkerLoginResponse>();
		try {
			log.info("app用户登录接口获取加密后数据串：{}",param);
			param = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY,param);
			JSONObject paramMap = JSONObject.parseObject(param);
			//获取来源
			//获取来源
			String fromType = ObjectUtils.isEmpty(paramMap.get("fromType"))?"1":paramMap.get("fromType").toString();
			String deviceGuid = null;
			if ("1".equals(fromType)){
				/**
				 * 处理request 查找sign是否存在
				 */
				Cookie[] cookies = request.getCookies();
				String sign = request.getHeader("sign");
				if (sign == null) {
					if (null != cookies) {
						for (int i = 0; i < cookies.length; i++) {
							if ("sign".equals(cookies[i].getName())) {
								sign = cookies[i].getValue();
							}
						}
					}
				}
				if (StringUtil.isBlank(sign)){
					return AppR.fail("授权失败,sign不存在");
				}
				log.info("sign数据：{}",sign);
				/**sign格式 sign;sn  如：ewrwssdfsd:0V2SH9DRA30155**/
				String[] strings = sign.split(";");
				if (StringUtil.isBlank(allcoreRedis.get(strings[1])) || !strings[0].equals(allcoreRedis.get(strings[1]))){
					return AppR.fail("授权失败,无效的sign");
				}
				if (StringUtil.isBlank(strings[1])){
					return AppR.fail("非法设备");
				}
				deviceGuid = strings[1];
			}else {
				if (ObjectUtils.isEmpty(paramMap.get("deviceGuid"))){
					return AppR.fail("deviceGuid参数不能为空");
				}
				deviceGuid = paramMap.getString("deviceGuid");
			}
			if (null == paramMap.get("loginName")) {
				return AppR.fail("登录名不能为空");
			}
			if (null == paramMap.get("passWord")) {
				return AppR.fail("密码不能为空");
			}
			String loginName = paramMap.getString("loginName");
			String passWord = paramMap.getString("passWord");
			data = iIscService.pcLoginIsc(loginName, passWord,deviceGuid,fromType);
			return data;
		} catch (Exception e) {
			log.info("【app用户登录方法异常】-> {}", e);
			return AppR.fail("app用户登录方法异常");
		}
	}

}
