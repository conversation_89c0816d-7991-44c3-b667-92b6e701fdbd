package com.allcore.isc.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

@Data
@RefreshScope
@Component
public class PcConfig {

	/**
	 * （缺陷标注）重命名软件
	 */
	@Value("${pc.defectHandle.loginFlag:default}")
	private String defectHandleLoginFlag;

}
