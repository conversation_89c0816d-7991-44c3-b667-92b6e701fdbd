package com.allcore.isc.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allcore.common.config.IndexConfig;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.isc.service.IIgwService;
import com.allcore.isc.service.IIscService;
import com.allcore.isc.vo.IscUserVO;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.aostarit.sgid.agent.EncryptHelper;
import com.sgcc.isc.ualogin.client.vo.IscSSOUserBean;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.allcore.isc.util.HttpClientUtil;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * ISC实现类
 *
 * <AUTHOR>
 * @date 2022/08/01 11:08
 **/
@Service
@Slf4j
@AllArgsConstructor
public class IIgwServiceImpl implements IIgwService {

	private final IndexConfig indexConfig;

	private final IIscService iscService;

	private final String DEV_TICKET = "dev-ticket";
	private final String DEV_USER_ID = "330000000128620";

	/**
	 * （返回结果给前端发密码模式登录）
	 * <AUTHOR>
	 * @date 2022/07/30 12:36
	 * @param ticket
	 * @return com.allcore.system.user.vo.IscUserVO
	 */
	@Override
	public IscUserVO igwLogin(String ticket) {
		if(StringUtil.isBlank(ticket)){
			return null;
		}
		String userId = DEV_USER_ID;
		if(!ticket.equals(DEV_TICKET)){
			JSONObject userInfo = getUserByTicket(ticket);
			userId = userInfo.get("iscUserId").toString();
		}
		log.info("获取i国网==============userId: {}", userId);
		User user;
		user = UserCache.getUser(userId);
		/*如果有 表示 该账号已经在PC端isc方式登录过了 直接拿系统数据*/
		if(Func.isEmpty(user.getId())){
			//获取账号 方式二 根据userId去isc获取,没有事先发过isc的ticket校验接口 此方案待验证,此方案如果通 就不需要PC端登录该账号的前置操作
			IscSSOUserBean iscSsoUserBean = new IscSSOUserBean();
			iscSsoUserBean.setIscUserId(userId);
			user = iscService.getUser(iscSsoUserBean);
		}
		IscUserVO rst = new IscUserVO();
		rst.setTenantId(user.getTenantId());
		rst.setAuthorization(CommonUtil.DEAFAULT_AUTHORIZATION);
		rst.setGrantType(CommonUtil.PASSWORD_KEY);
		rst.setScope(CommonUtil.ALL);
		rst.setUsername(user.getAccount());
		//Jsepc01! 密文
		rst.setPassword(CommonUtil.DEFAULT_PASSWORD_BASE64);
		return rst;
	}

	/**
	 * 根据ticket换取用户id
	 * @param ticket
	 * @return
	 */
	@Override
	public JSONObject getUserByTicket(String ticket) {
		String iscappid = indexConfig.getAppId();
		String isclogonserviceurl = indexConfig.getSsoPath();
		String clientSecret = indexConfig.getSecretKey();
		String signKey = indexConfig.getSign();
		String tokenUrl = indexConfig.getApiTokenPath();
		String ticketUrl = indexConfig.getApiTicketPath();


		if (!StringUtil.isBlank(ticket)) {
			try {
				Map<String, String> param = new LinkedHashMap<>(2);
				param.put("appId",iscappid);
				param.put("clientSecret",clientSecret);
				String tokenBackUrl = tokenUrl+"?appId="+iscappid+"&clientSecret="+clientSecret;
				log.info("========================signKey:{}",signKey);
				log.info("========================JSON.toJSONString(param):{}",JSON.toJSONString(param));
				String dataParm = EncryptHelper.sign(signKey, JSON.toJSONString(param));
				log.info("调用i国网token tokenBackUrl:{},param:{},iscappid:{},dataParm:{}",tokenBackUrl, param, iscappid, dataParm);
				JSONObject token = HttpClientUtil.doPostByJson(tokenBackUrl, param, iscappid, dataParm);
				log.info("调用i国网token 返回:{}",token);
				String accessToken = token.get("data").toString();
				Map<String, String> param1 = new LinkedHashMap<>();
				param1.put("appId",iscappid);
				param1.put("service",isclogonserviceurl);
				param1.put("ticket",ticket);
				log.info("========================signKey:{}",signKey);
				log.info("========================JSON.toJSONString(param1):{}",JSON.toJSONString(param1));
				String signData = EncryptHelper.sign(signKey, JSON.toJSONString(param1));
				String ticketBackUrl = ticketUrl+"?ticket="+ticket+"&appId="+iscappid+"&service="+isclogonserviceurl;
				log.info("调用i国网ticket ticketBackUrl:{},param1:{},iscappid:{},signData:{},accessToken:{},",ticketBackUrl, param1, iscappid,signData, accessToken);
				JSONObject userInfo = HttpClientUtil.doPostByTicket(ticketBackUrl, param1, iscappid,signData, accessToken);
				log.info("调用i国网token 返回:{}",userInfo);
				String data = userInfo.get("data").toString();
				JSONObject user = JSONObject.parseObject(data);
				String iscUserId = user.get("userId").toString();
				log.info("外层userId(iscUserId)===================="+iscUserId);
				JSONObject sgccUserInfo = JSONObject.parseObject(user.get("sgccUserInfo").toString());
				log.info("userinfo是===================="+sgccUserInfo.toJSONString());
				sgccUserInfo.put("iscUserId",iscUserId);
				return sgccUserInfo;
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return null;
	}


}
