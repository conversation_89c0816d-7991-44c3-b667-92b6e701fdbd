package com.allcore.main.code.inspection.service.impl;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.inspection.dto.InspectionRouteDTO;
import com.allcore.main.code.inspection.dto.InspectionRouteSaveDTO;
import com.allcore.main.code.inspection.dto.InspectionRouteDeviceDTO;
import com.allcore.main.code.inspection.entity.InspectionRoute;
import com.allcore.main.code.inspection.entity.InspectionRouteDevice;
import com.allcore.main.code.inspection.mapper.InspectionRouteMapper;
import com.allcore.main.code.inspection.service.IInspectionRouteDeviceService;
import com.allcore.main.code.inspection.service.IInspectionRouteService;
import com.allcore.main.code.inspection.vo.InspectionRouteVO;
import com.allcore.main.code.source.service.IDeviceQrCodeService;
import com.allcore.main.code.source.vo.QrCodeDeviceVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @program: bl
 * @description: 巡检路线服务实现类
 * @author: fanxiang
 * @create: 2025-06-06 09:34
 **/

@Service
@Slf4j
@AllArgsConstructor
public class InspectionRouteServiceImpl extends ZxhcServiceImpl<InspectionRouteMapper, InspectionRoute>
implements IInspectionRouteService {

    private  final IInspectionRouteDeviceService inspectionRouteDeviceService;

    private final IDeviceQrCodeService deviceQrCodeService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public R saveInspectionRoute(InspectionRouteSaveDTO dto) {
        try {
            log.info("开始保存巡检路线，路线名称：{}", dto.getRouteName());

            // 参数验证
            validateSaveParams(dto);

            // 创建路线实体
            InspectionRoute entity = buildRouteEntity(dto);

            // 保存路线主表
            if (!this.save(entity)) {
                throw new RuntimeException("保存巡检路线主表失败");
            }
            log.info("巡检路线主表保存成功，路线ID：{}", entity.getId());

            // 保存设备关联
            List<InspectionRouteDevice> deviceList = buildRouteDevices(entity.getId(), dto);
            inspectionRouteDeviceService.saveRouteDevices(entity.getId(), deviceList);
            log.info("巡检路线设备关联保存成功，设备数量：{}", deviceList.size());

            log.info("保存巡检路线完成，路线ID：{}", entity.getId());
            return R.success("保存巡检路线成功");

        } catch (Exception e) {
            log.error("保存巡检路线失败，路线名称：{}", dto.getRouteName(), e);
            // 抛出运行时异常，触发事务回滚
            throw new RuntimeException("保存巡检路线失败：" + e.getMessage(), e);
        }
    }

    @Override
    public R<IPage<InspectionRouteVO>> selectInspectionRoutePage(IPage<InspectionRouteVO> page, InspectionRouteDTO dto) {

        List<InspectionRouteVO>list=baseMapper.selectInspectionRoutePage(page,dto);
        list.forEach(e->{
            List<InspectionRouteDevice> deviceList = inspectionRouteDeviceService.list(Wrappers.<InspectionRouteDevice>lambdaQuery()
                    .eq(InspectionRouteDevice::getRouteId, e.getId()));
            for (InspectionRouteDevice device : deviceList) {
                if(Func.isAnyBlank(device.getLongitude(),device.getLatitude())){
                    // 去设备台账中查询
                    R<QrCodeDeviceVO> deviceInfoR = deviceQrCodeService.getDeviceInfo(device.getDeviceId(), device.getDeviceType());
                    if(deviceInfoR.isSuccess() && deviceInfoR.getData() != null){
                        QrCodeDeviceVO data = deviceInfoR.getData();
                        QrCodeDeviceVO.DeviceBasicInfoVO deviceBasicInfo = data.getDeviceBasicInfo();
                        device.setLongitude(deviceBasicInfo.getLongitude());
                        device.setLatitude(deviceBasicInfo.getLatitude());
                        device.setDeviceName(deviceBasicInfo.getDeviceName());
                    }
                }
            }
            e.setDeviceList(deviceList);
        });
        return R.data(page.setRecords(list));
    }

    @Override
    public R updateInspectionRoute(InspectionRouteSaveDTO dto) {
        try
        {
            InspectionRoute existingRoute = this.getById(dto.getId());
            if (existingRoute == null) {
                return R.fail("路线不存在");
            }

            // 参数验证
            validateSaveParams(dto);

            InspectionRoute inspectionRoute = buildRouteEntity(dto);
            if(!this.updateById(inspectionRoute)) {
                throw new ServiceException("更新巡检路线主表失败");
            }

            // 更新路线主表
            List<InspectionRouteDevice> deviceList = buildRouteDevices(inspectionRoute.getId(),dto);
            inspectionRouteDeviceService.saveRouteDevices(inspectionRoute.getId(), deviceList);
            return R.success("更新巡检路线成功");
        }catch (Exception e){
            log.error("更新巡检路线失败，路线名称：{}", dto.getRouteName(), e);
            // 抛出运行时异常，触发事务回滚
            throw new RuntimeException("更新巡检路线失败：" + e.getMessage(), e);
        }

    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public R deleteInspectionRoute(List<String> strList) {
        try{
            List<InspectionRoute> routes=this.listByIds(strList);

            //删除路线和关联设备
            if(!inspectionRouteDeviceService.remove(Wrappers.<InspectionRouteDevice>lambdaQuery()
                    .in(InspectionRouteDevice::getRouteId, strList))){
                throw new ServiceException("删除巡检路线设备关联失败");
            }
            if(!this.removeByIds(strList)){
                throw new ServiceException("删除巡检路线失败");
            }
            return R.success("删除巡检路线成功");
        }catch (Exception e){
            log.error("删除巡检路线失败");
            return R.fail("删除巡检路线失败: " + e.getMessage());
        }
    }

    /**
     * 验证保存参数
     * @param dto 保存DTO
     */
    private void validateSaveParams(InspectionRouteSaveDTO dto) {
        String id=Func.notNull(dto.getId())?dto.getId():null;
        if (checkRouteNameExists(dto.getRouteName(), dto.getStationId(), dto.getDeviceType(), id)) {
            throw new IllegalArgumentException("路线名称已存在");
        }

        if (dto.getDeviceIds() == null || dto.getDeviceIds().size() < 2) {
            throw new IllegalArgumentException("至少选择两个设备");
        }
    }

    /**
     * 构建路线实体
     * @param dto 保存DTO
     * @return 路线实体
     */
    private InspectionRoute buildRouteEntity(InspectionRouteSaveDTO dto) {
        InspectionRoute entity = BeanUtil.copy(dto, InspectionRoute.class);
        if (entity == null) {
            throw new RuntimeException("创建路线实体失败");
        }

        // 如果是新增
        if(Func.isEmpty(entity.getId())){
            entity.setRouteCode(generateRouteCode(dto.getStationId(), dto.getDeviceType()));
            entity.setStatus(1);
        }
        entity.setDeviceCount(dto.getDeviceIds().size());
      // 默认启用

        return entity;
    }





    /**
     *  检查是否重名
     * @param routeName
     * @param stationId
     * @param deviceType
     * @param excludeId
     * @return
     */
    private boolean checkRouteNameExists(String routeName,String stationId,String deviceType,String excludeId) {
        int count=baseMapper.checkRouteNameExists(routeName,stationId,deviceType,excludeId);
        return count>0;
    }


    /**
     *  路线编号生成
     * @param stationId
     * @param deviceType
     * @return
     */
    private String generateRouteCode(String stationId,String deviceType){
        String prefix="IR"+deviceType+stationId.substring(stationId.length()-4);

        //查询当前前缀下的最大编号
        String maxCode=baseMapper.getMaxRouteCode(prefix+"%");

        // 如果没有现有编号，从001开始
        int nextNumber =1;
        if(maxCode!=null && maxCode.length()>prefix.length()){
            try{
                String numberPart=maxCode.substring(prefix.length());
                nextNumber=Integer.parseInt(numberPart)+1;
            }catch (NumberFormatException e){
                log.error("解析路线编号失败！",e);
            }
        }
        return prefix+String.format("%03d",nextNumber);
    }

    /**
     * 构建路线设备关联列表
     * @param routeId 路线ID
     * @param dto 保存DTO
     * @return 设备关联列表
     */
    private List<InspectionRouteDevice> buildRouteDevices(String routeId, InspectionRouteSaveDTO dto) {
        List<InspectionRouteDevice> deviceList = new ArrayList<>();

        // 创建设备详情映射，提高查找效率
        Map<String, InspectionRouteDeviceDTO> deviceMap = new HashMap<>();
        if (Func.isNotEmpty(dto.getDeviceList())) {
            deviceMap = dto.getDeviceList().stream()
                    .collect(Collectors.toMap(
                            InspectionRouteDeviceDTO::getDeviceId,
                            Function.identity(),
                            (existing, replacement) -> existing // 处理重复key
                    ));
        }

        // 构建设备关联列表
        for (int i = 0; i < dto.getDeviceIds().size(); i++) {
            String deviceId = dto.getDeviceIds().get(i);
            InspectionRouteDevice device = new InspectionRouteDevice();
            device.setRouteId(routeId);
            device.setDeviceId(deviceId);
            device.setDeviceOrder(i + 1);
            device.setDeviceType(dto.getDeviceType());

            // 从映射中获取设备详情
            InspectionRouteDeviceDTO deviceDetail = deviceMap.get(deviceId);
            if (deviceDetail != null) {
                device.setDeviceName(deviceDetail.getDeviceName());
                device.setLongitude(deviceDetail.getLongitude());
                device.setLatitude(deviceDetail.getLatitude());
                device.setElevation(deviceDetail.getElevation());
            }

            deviceList.add(device);
        }

        return deviceList;
    }

}
