package com.allcore.main.code.inspection.mapper;

import com.allcore.main.code.inspection.entity.InspectionUser;
import com.allcore.main.code.inspection.vo.InspectionUserVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
public interface InspectionUserMapper extends BaseMapper<InspectionUser> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param inspectionUser
	 * @return
	 */
	List<InspectionUserVO> selectInspectionUserPage(IPage page, InspectionUserVO inspectionUser);

}
