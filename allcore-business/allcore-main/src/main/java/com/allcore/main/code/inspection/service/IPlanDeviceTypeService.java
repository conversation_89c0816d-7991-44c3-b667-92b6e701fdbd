package com.allcore.main.code.inspection.service;

import com.allcore.common.base.ZxhcService;
import com.allcore.main.code.inspection.entity.PlanDeviceType;
import com.allcore.main.code.inspection.vo.PlanDeviceTypeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-10-26
 */
public interface IPlanDeviceTypeService extends IService<PlanDeviceType> {

    /**
     * 自定义分页
     *
     * @param page
     * @param planDeviceType
     * @return
     */
    IPage<PlanDeviceTypeVO> selectPlanDeviceTypePage(IPage<PlanDeviceTypeVO> page, PlanDeviceTypeVO planDeviceType);

}
