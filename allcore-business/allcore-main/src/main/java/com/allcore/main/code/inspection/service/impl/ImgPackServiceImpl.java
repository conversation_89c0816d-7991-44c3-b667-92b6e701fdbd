package com.allcore.main.code.inspection.service.impl;

import static com.allcore.common.constant.BasicConstant.PACK_LOADING;
import static com.allcore.common.constant.BasicConstant.PACK_OK;
import static java.util.stream.Collectors.toList;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.allcore.core.secure.utils.AuthUtil;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizEnum;
import com.allcore.common.utils.FileUtils;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.*;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.common.service.CommonService;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.inspection.dto.ImgPackDTO;
import com.allcore.main.code.inspection.dto.ImgPackQueryDTO;
import com.allcore.main.code.inspection.dto.ImgPackSaveDTO;
import com.allcore.main.code.inspection.entity.ImgPack;
import com.allcore.main.code.inspection.entity.InspectionPicture;
import com.allcore.main.code.inspection.mapper.ImgPackMapper;
import com.allcore.main.code.inspection.mapper.InspectionPictureMapper;
import com.allcore.main.code.inspection.service.IImgPackService;
import com.allcore.main.code.inspection.vo.ImgPackVO;
import com.allcore.main.code.inspection.vo.InspectionPictureVO;
import com.allcore.main.code.inspection.vo.PatrolDataImageVO;
import com.allcore.main.code.inspection.wrapper.InspectionPictureWrapper;
import com.allcore.main.utils.ZipUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-06
 */
@Slf4j
@Service
@AllArgsConstructor
public class ImgPackServiceImpl extends ZxhcServiceImpl<ImgPackMapper, ImgPack> implements IImgPackService {
    private final IOssClient ossClient;
    private final CommonService commonService;
    private final InspectionPictureMapper inspectionPictureMapper;

    @Override
    public IPage<ImgPackVO> selectImgPackPage(IPage<ImgPackVO> page, ImgPackQueryDTO imgPack) {
        if (Func.isBlank(imgPack.getDeptCode())) {
            imgPack.setDeptCode(AuthUtil.getDeptCode());
        }
        List<ImgPack> entity = baseMapper.selectImgPackPage(page, imgPack);
        List<ImgPackVO> vo = BeanUtil.copy(entity, ImgPackVO.class);
        if (ObjectUtil.isNotEmpty(vo)) {
            this.setDownPath(vo);
        }
        return page.setRecords(vo);
    }

    private void setDownPath(List<ImgPackVO> vo) {
        // 文件guid
        List<String> fileGuids = vo.stream().map(m -> m.getFileGuid()).collect(Collectors.toList());
        // 缺陷图 -- guidmap
        Map<String, AllcoreFileVO> fileMap = getFileMap(fileGuids);
        vo.stream().forEach(e -> {
            e.setStatusZh(StringUtil.equals(StringPool.ONE, e.getStatus()) ? PACK_OK : PACK_LOADING);
            if (ObjectUtil.isNotEmpty(fileMap)) {
                e.setDownPath(fileMap.get(e.getFileGuid()).getStaticPath());
                e.setFileName(fileMap.get(e.getFileGuid()).getOriginalName());
            }
        });
    }

    private Map<String, AllcoreFileVO> getFileMap(List<String> fileGuids) {
        // 文件信息处理成map
        R<List<AllcoreFileVO>> fileR = ossClient.getFilesDetail(fileGuids);
        Map<String, AllcoreFileVO> fileMap = Maps.newHashMap();
        if (fileR.isSuccess()) {
            // 放进map方便获取
            fileMap =
                fileR.getData().stream().collect(Collectors.toMap(AllcoreFileVO::getFileGuid, Function.identity()));
        }
        return fileMap;
    }

    @Override
    public ImgPack modify(ImgPackDTO imgPackDTO) {
        if (imgPackDTO.getId() == null) {
            ImgPack imgPack = BeanUtil.copy(imgPackDTO, ImgPack.class);
            if (!super.save(imgPack)) {
                throw new ServiceException("新增失败!");
            }
            return imgPack;
        }
        ImgPack imgPack =
            Optional.ofNullable(super.getById(imgPackDTO.getId())).orElseThrow(() -> new ServiceException("数据不存在"));
        BeanUtils.copyProperties(imgPackDTO, imgPack);
        if (!super.updateById(imgPack)) {
            throw new ServiceException("修改失败!");
        }
        return imgPack;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean savePack(ImgPackSaveDTO dto) throws IOException {
        ImgPack entity = new ImgPack();
        entity.setDeviceId(dto.getDeviceId());
        entity.setRemark(dto.getRemark());
        entity.setDeviceType(dto.getDeviceType());
        entity.setStatus(StringPool.ZERO);
        this.save(entity);
        return zipPackage(entity.getId(), dto);
    }

    @Override
    public R<InspectionPictureVO> getPictureTaggingByGuid(String pictureGuid, String deviceType) {
        // 获取当前图片信息
        InspectionPicture pictureDetail = inspectionPictureMapper
            .selectOne(new LambdaQueryWrapper<InspectionPicture>().eq(InspectionPicture::getFileGuid, pictureGuid));
        if (ObjectUtil.isEmpty(pictureDetail)) {
            return R.fail("图片不存在!");
        }
        InspectionPictureVO pictureDetailVo = BeanUtil.copy(pictureDetail, InspectionPictureVO.class);
        R<AllcoreFileVO> fileDetail = ossClient.getFileDetail(pictureDetailVo.getFileGuid());
        if (fileDetail.isSuccess()) {
            // 附件相对路径
            pictureDetailVo.setFilePath(fileDetail.getData().getStaticPath());
            // 缩略图相对路径
            pictureDetailVo.setThumbnailFilePath(fileDetail.getData().getStaticThumbPath());
            pictureDetailVo.setOriginalName(fileDetail.getData().getOriginalName());
        }
        Map<String, BasicCommonVO> deviceMap =
            commonService.getDeviceByDeviceIds(Stream.of(pictureDetailVo.getDeviceId()).collect(toList()), deviceType);
        pictureDetailVo.setDeviceName(deviceMap.get(pictureDetailVo.getDeviceId()).getDeviceName());

        return R.data(InspectionPictureWrapper.build().entityVO(pictureDetailVo));
    }

    @Async
    public boolean zipPackage(String id, ImgPackSaveDTO dto) throws IOException {
        // 数据信息不能空
        List<PatrolDataImageVO> dataSource = dto.getDataSource();
        if (CollectionUtils.isEmpty(dataSource)) {
            throw new ServiceException("数据为空!");
        }
        // 创建路径
        File file = new File(CommonConstant.TEMPPATH);
        if (!file.exists()) {
            file.mkdirs();
        }
        String format = DateUtil.time();
        FileUtils.createDir(CommonConstant.TEMPPATH + format);
        // 查图像列表
        List<String> fileGuidList = Func.toStrList(dto.getFileGuids());
        // 从全量树数据中将选中的文件打包
        createDeviceTreeFileSource(CommonConstant.TEMPPATH + format, dataSource, fileGuidList);
        // 上传zip
        String zip = "";
        String date = DateUtil.time();
        try {
            zip = ZipUtil.zip(CommonConstant.TEMPPATH + format, CommonConstant.TEMPPATH, date + ".zip", true);
            File zipFile = new File(zip);
            FileInputStream fileInputStream = new FileInputStream(zipFile);
            MultipartFile multipartFile = FileUtils.getMultipartFile(fileInputStream, zipFile.getName());
            R<AllcoreFileVO> r =
                ossClient.putFileAttach(BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(), multipartFile, StringPool.NO, "");
            if (r.isSuccess()) {
                ImgPack entity = this.getById(id);
                entity.setStatus(StringPool.ONE);
                entity.setFileGuid(r.getData().getFileGuid());
                return this.updateById(entity);
            }
            // 将zip包上传到文件服务，保存文件服务返回的地址和fileGuid
        } catch (ZipException e) {
            e.printStackTrace();
        } finally {
            ZipUtil.deleteDir(CommonConstant.TEMPPATH + format);
            File fileRoot = new File(zip);
            fileRoot.delete();
        }
        return false;
    }

    private void createDeviceTreeFileSource(String tempPath, List<PatrolDataImageVO> dataSource, List<String> picGuid) {
        dataSource.stream().forEach(d -> {
            String picPath = tempPath + ZipUtil.FILE_SEPARATOR + d.getDeviceName();
            List<PatrolDataImageVO> vo = d.getChildren();
            if (CollectionUtil.isEmpty(vo)) {
                // 风机、光伏
                packagePicFileSource(picPath, d.getFileMap(), picGuid);
            } else {
                vo.forEach(v -> {
                    // 杆塔
                    String childPath = picPath + ZipUtil.FILE_SEPARATOR + v.getDeviceName();
                    packagePicFileSource(childPath, v.getFileMap(), picGuid);
                });
            }
        });

    }

    private void packagePicFileSource(String picPath, Map<String, List<AllcoreFileVO>> fileMap, List<String> picGuid) {
        fileMap.forEach((key, value) -> {
            String temppath = picPath + ZipUtil.FILE_SEPARATOR + key + ZipUtil.FILE_SEPARATOR;;
            for (AllcoreFileVO fileVo : value) {
                if (picGuid.contains(fileVo.getFileGuid())) {
                    File file = new File(temppath);
                    if (!file.exists()) {
                        file.mkdirs();
                    }
                    String filePath = fileVo.getStaticPath();
                    String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                    FileUtils.loadImg(filePath, temppath + fileName);
                }
            }
        });

    }

}
