package com.allcore.main.code.fences.controller;

import java.util.Map;

import com.allcore.main.code.fences.vo.FenceAlarmVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.fences.dto.FencesDTO;
import com.allcore.main.code.fences.dto.FencesRequest;
import com.allcore.main.code.fences.service.IFencesService;
import com.allcore.main.code.fences.vo.FencesVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;

/**
 * 电子围栏
 *
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/fences")
@Api(value = "电子围栏", tags = "电子围栏")
public class FencesController {

    @Autowired
    private IFencesService xFusionService;

    /**
     * 围栏创建
     * @param request
     * @return
     */
    @PostMapping("/create")
    public R fenceCreate(@RequestBody FencesRequest request) {
        return xFusionService.fenceCreate(request);
    }

    /**
     * 围栏更新
     * @param request
     * @return
     */
    @PostMapping("/update")
    public R fenceUpdate(@RequestBody FencesRequest request) {
        return xFusionService.fenceUpdate(request);
    }

    /**
     * 围栏查询
     * @param request
     * @return
     */
    @PostMapping("/query")
    public R fenceQuery(@RequestBody Map<String, String> request) {
        return xFusionService.fenceQuery(request);
    }

    /**
     * 围栏列表查询
     * @param query
     * @param queryDTO
     * @return
     */
    @PostMapping("/page")
    public R<IPage<FencesVO>> fenceList(Query query, FencesDTO queryDTO) {
        return R.data(xFusionService.fenceList(Condition.getPage(query), queryDTO));
    }

    /**
     * 围栏删除
     * @param request
     * @return
     */
    @PostMapping("/delete")
    public R fenceDelete(@RequestBody Map<String, String> request) {
        return xFusionService.fenceDelete(request);
    }

    @GetMapping("/check")
    public R<FenceAlarmVO> fenceCheck(@RequestParam String userId, @RequestParam String longitude, @RequestParam String latitude) {
        return xFusionService.fenceAlarmVerification(userId,longitude,latitude);
    }
}