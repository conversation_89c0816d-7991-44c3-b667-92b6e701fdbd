package com.allcore.main.code.flywatch.mapper;

import com.allcore.main.code.flywatch.entity.AirspaceFile;
import com.allcore.main.code.flywatch.vo.AirspaceFileVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface AirspaceFileMapper extends BaseMapper<AirspaceFile> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param airspaceFile
	 * @return
	 */
	List<AirspaceFileVO> selectAirspaceFilePage(IPage page, AirspaceFileVO airspaceFile);

}
