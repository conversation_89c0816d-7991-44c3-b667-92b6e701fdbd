package com.allcore.main.code.flywatch.service;

import com.allcore.main.code.flywatch.entity.AirspaceUavPlane;
import com.allcore.main.code.flywatch.vo.AirspaceUavPlaneVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
public interface IAirspaceUavPlaneService extends IService<AirspaceUavPlane> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param airspaceUavPlane
	 * @return
	 */
	IPage<AirspaceUavPlaneVO> selectAirspaceUavPlanePage(IPage<AirspaceUavPlaneVO> page, AirspaceUavPlaneVO airspaceUavPlane);

}
