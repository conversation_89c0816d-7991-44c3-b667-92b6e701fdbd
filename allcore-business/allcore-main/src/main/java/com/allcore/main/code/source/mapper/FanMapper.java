package com.allcore.main.code.source.mapper;

import java.util.List;

import com.allcore.main.code.source.dto.FanDTO;
import com.allcore.main.code.source.entity.Fan;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-10-10
 */
public interface FanMapper extends BaseMapper<Fan> {

    /**
     * 自定义分页
     *
     * @param page
     * @param deviceQuery
     * @return
     */
    List<Fan> selectFanPage(IPage page, FanDTO deviceQuery);

    /**
     * （list）
     * <AUTHOR>
     * @date 2024/01/17 15:45
     * @param deviceQuery
     * @return java.util.List<com.allcore.main.code.source.entity.Fan>
     */
    List<Fan> selectFan(@Param("deviceQuery") FanDTO deviceQuery);

    /**
     * （风机模型）
     * <AUTHOR>
     * @date 2024/01/17 15:48
     * @return java.lang.String
     */
    String getLastModelPath();

}
