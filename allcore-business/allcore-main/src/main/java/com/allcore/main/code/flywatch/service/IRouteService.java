package com.allcore.main.code.flywatch.service;

import java.util.List;
import java.util.Map;

import com.allcore.main.code.inspection.vo.AppRouteResponseVO;
import org.springframework.web.multipart.MultipartFile;

import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.allcore.main.code.common.dto.DeviceCommonDTO;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.flywatch.dto.BatchSubmitRouteFileDto;
import com.allcore.main.code.flywatch.dto.RouteDTO;
import com.allcore.main.code.flywatch.dto.RouteSaveDTO;
import com.allcore.main.code.flywatch.dto.UploadDevicePvAirLineDTO;
import com.allcore.main.code.flywatch.entity.Route;
import com.allcore.main.code.flywatch.vo.PvTrackVO;
import com.allcore.main.code.flywatch.vo.RouteVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 服务类
 *
 * <AUTHOR>
 * @since 2023-10-20
 */
public interface IRouteService extends ZxhcService<Route> {

    /**
     * 自定义分页
     *
     * @param page
     * @param route
     * @return
     */
    IPage<RouteVO> selectRoutePage(IPage<RouteVO> page, RouteDTO route);

    /**
     * 增
     *
     * @param route
     * @return
     */
    R saveRoute(RouteSaveDTO route);

    /**
     * 改
     *
     * @param route
     * @return
     */
    R updateRouteById(RouteSaveDTO route);

    /**
     * 删
     *
     * @param strList
     * @return
     */
    R deleteRoute(List<String> strList);

    /**
     * 批量
     *
     * @param batchSubmitRouteFileDto
     * @return
     */
    R batchSubmit(BatchSubmitRouteFileDto batchSubmitRouteFileDto);

    /**
     * 航线列表
     *
     * @param dto
     * @return
     */
    List<BasicCommonVO> listCommon(DeviceCommonDTO dto);

    /**
     * 批量保存
     *
     * @param dto
     * @return
     */
    R saveBatchPvRouteInfo(List<RouteSaveDTO> dto);

    /**
     * 业务层
     * @param taskId
     * @return
     */
    List<PvTrackVO> airportNestTrack(String taskId);

    /**
     * 根据设备信息解析航线
     * @param taskId
     * @param deviceId
     * @return
     */
    List<PvTrackVO> deviceNestTrack(String taskId,String deviceId);

    /**
     * 业务层
     * @param file
     * @param dto
     * @return
     */
    R uploadDevicePvAirLineFile(MultipartFile file, UploadDevicePvAirLineDTO dto);

    /**
     * 业务层
     * @param devicePvId
     * @param airLineType
     * @return
     */
    R devicePvAirLineList(String devicePvId, String airLineType);

    /**
     * 业务层
     * @param fileLink
     * @return
     */
    List<PvTrackVO> getRouteTrack(String fileLink);

    /**
     * 获取航线文件
     *
     * @param map
     * @return
     */
    R<AppRouteResponseVO> getRouteInfo(Map<String, String> map);
}
