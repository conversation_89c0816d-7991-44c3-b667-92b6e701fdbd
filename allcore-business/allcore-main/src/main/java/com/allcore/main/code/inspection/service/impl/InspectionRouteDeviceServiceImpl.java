package com.allcore.main.code.inspection.service.impl;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.utils.Func;
import com.allcore.main.code.inspection.entity.InspectionRouteDevice;
import com.allcore.main.code.inspection.mapper.InspectionRouteDeviceMapper;
import com.allcore.main.code.inspection.service.IInspectionRouteDeviceService;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;


/**
 * @program: bl
 * @description: 巡检路线设备服务实现类
 * @author: fanxiang
 * @create: 2025-06-06 16:55
 **/

@Service
@Slf4j
public class InspectionRouteDeviceServiceImpl extends ZxhcServiceImpl<InspectionRouteDeviceMapper, InspectionRouteDevice>
        implements IInspectionRouteDeviceService {



    @Override
    public void saveRouteDevices(String routeId, List<InspectionRouteDevice> deviceList) {
        try {
            log.info("开始保存路线设备关联，路线ID：{}，设备数量：{}", routeId, deviceList != null ? deviceList.size() : 0);

            // 先删除原有关联
            if (!deleteRouteId(routeId)) {
                throw new ServiceException("删除原有路线设备关联失败");
            }

            // 批量插入新关联
            if (Func.isNotEmpty(deviceList)) {
                if (!this.saveBatch(deviceList)) {
                    throw new ServiceException("批量保存路线设备关联失败");
                }
            }

            log.info("保存路线设备关联成功，路线ID：{}", routeId);

        } catch (Exception e) {
            log.error("保存路线设备关联失败，路线ID：{}", routeId, e);
            throw new RuntimeException("保存路线设备关联失败：" + e.getMessage(), e);
        }
    }

    /**
     * 删除指定路线的所有设备关联（逻辑删除）
     * @param routeId 路线ID
     * @return 是否删除成功
     */
    private boolean deleteRouteId(String routeId) {
        try {
            // 对于逻辑删除，返回false不一定是错误
            // 可能是数据已经被删除或不存在
            boolean result = this.remove(Wrappers.<InspectionRouteDevice>lambdaQuery()
                    .eq(InspectionRouteDevice::getRouteId, routeId));

            log.info("删除路线设备关联完成，路线ID：{}，更新记录数：{}",
                    routeId, result ? "有记录被删除" : "无记录需要删除");
            return true;

        } catch (Exception e) {
            log.error("删除路线设备关联失败，路线ID：{}", routeId, e);
            return false;
        }
    }
}
