package com.allcore.main.code.solve.service.impl;

import cn.hutool.core.util.StrUtil;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.enums.BizEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.common.utils.FileUtils;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.node.ForestNodeMerger;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.core.tool.utils.*;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.feign.IAlarmClient;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.inspection.entity.InspectionPictureTagging;
import com.allcore.main.code.inspection.mapper.InspectionPictureTaggingMapper;
import com.allcore.main.code.inspection.mapper.InspectionTaskMapper;
import com.allcore.main.code.inspection.service.IInspectionPictureTaggingService;
import com.allcore.main.code.inspection.vo.InspectionPictureTaggingVO;
import com.allcore.main.code.solve.dto.*;
import com.allcore.main.code.solve.entity.*;
import com.allcore.main.code.solve.mapper.RemovePictureTaggingMapper;
import com.allcore.main.code.solve.mapper.RemoveTaskMapper;
import com.allcore.main.code.solve.node.SolveTreeNode;
import com.allcore.main.code.solve.service.*;
import com.allcore.main.code.solve.vo.*;
import com.allcore.main.code.source.service.IPvAreaService;
import com.allcore.main.code.system.props.SystemProperties;
import com.allcore.main.utils.ZipUtil;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.exception.ZipException;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.entity.ContentType;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.allcore.common.constant.BasicConstant.*;
import static com.allcore.main.constant.MainConstant.XQ;

/**
 * 服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-30
 */
@Slf4j
@Service
@AllArgsConstructor
public class RemoveTaskServiceImpl extends ZxhcServiceImpl<RemoveTaskMapper, RemoveTask> implements IRemoveTaskService {

    private final IRemovePictureTaggingService removePictureTaggingService;
    private final RemovePictureTaggingMapper removePictureTaggingMapper;
    private final IRemovePictureTaggingDetailService removePictureTaggingDetailService;
    private final IRemovePictureTaggingFileService removePictureTaggingFileService;

    private final InspectionPictureTaggingMapper inspectionPictureTaggingMapper;
    private final IInspectionPictureTaggingService inspectionPictureTaggingService;
    private final IRemoveLibraryDownLoadService removeLibraryDownLoadService;

    private final IOssClient ossClient;

    private final SystemProperties systemProperties;

    private final IPvAreaService pvAreaService;

    private final IAlarmClient alarmClient;

    @Resource
    private InspectionTaskMapper inspectionTaskMapper;


    @Override
    public IPage<RemoveTaskVO> selectRemoveTaskPage(IPage<RemoveTaskVO> page, RemoveTaskVO removeTask) {
        return page.setRecords(baseMapper.selectRemoveTaskPage(page, removeTask));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRemoveTask(RemoveTaskDTO dto) {
        RemoveTask removeTask = BeanUtil.copy(dto, RemoveTask.class);
        save(removeTask);
        List<RemovePictureTagging> pictureTaggings = new ArrayList<>();
        //生成消缺编号XQ-20231219093026807-002、20231219093026807取对应巡检任务编号中间的 002 按list的i生成
        String time = dto.getInspectionTaskNo().split(StringPool.DASH)[1];
        for (int i = 0; i < dto.getInspectionPictureTaggingIds().size(); i++) {
            String e = dto.getInspectionPictureTaggingIds().get(i);
            RemovePictureTagging pictureTagging = new RemovePictureTagging();
            pictureTagging.setDeptCode(dto.getDeptCode());
            pictureTagging.setCreateUser(dto.getCreateUser());
            pictureTagging.setCreateDept(dto.getCreateDept());
            pictureTagging.setRemoveTaskId(removeTask.getId());
            pictureTagging.setInspectionPictureTaggingId(e);
            pictureTagging.setRemoveTaskNo(XQ + StringPool.DASH + time + StringPool.DASH + String.format("%03d", i + 1));
            // 根据设备类型设置消缺任务初始状态：光伏设备为待下发，其他设备为执行中
            if (BizDictEnum.DEVICE_TYPE_PV.getCode().equals(removeTask.getDeviceType())) {
                pictureTagging.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_TO_SEND.getCode());
            } else {
                pictureTagging.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
            }
            pictureTaggings.add(pictureTagging);
        }
        return removePictureTaggingService.saveBatch(pictureTaggings);
    }

    @Override
    public boolean send(SolveSendDTO dto) {
        //改为执行中
        boolean flag = removePictureTaggingService.update(new LambdaUpdateWrapper<RemovePictureTagging>()
                .set(RemovePictureTagging::getRemoveTaskStatus, BizDictEnum.REMOVE_STATUS_DOING.getCode())
                .set(RemovePictureTagging::getRemoveUser, dto.getRemoveUser())
                .set(RemovePictureTagging::getSendTime, new Date())
                .in(RemovePictureTagging::getInspectionPictureTaggingId, Func.toStrList(dto.getInspectionPictureTaggingIds())));

        try {
            //更新任务状态
            List<String> taskIds = removePictureTaggingService.list(new LambdaUpdateWrapper<RemovePictureTagging>()
                            .in(RemovePictureTagging::getInspectionPictureTaggingId, Func.toStrList(dto.getInspectionPictureTaggingIds())))
                    .stream().map(RemovePictureTagging::getRemoveTaskId).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(taskIds)) {
                update(new LambdaUpdateWrapper<RemoveTask>()
                        .set(RemoveTask::getRemoveTaskStatus, BizDictEnum.REMOVE_STATUS_DOING.getCode())
                        .in(RemoveTask::getId, taskIds));
            }
            //更新tag状态
            inspectionPictureTaggingService.update(new LambdaUpdateWrapper<InspectionPictureTagging>()
                    .set(InspectionPictureTagging::getEliminateStatus, BizDictEnum.TAGGING_TYPE_HAVE_TO_PUSH.getCode())
                    .in(InspectionPictureTagging::getId, Func.toStrList(dto.getInspectionPictureTaggingIds())));
        } catch (Exception e) {
            throw new ServiceException("更新消缺任务状态异常");
        }

        return flag;
    }

    @Override
    public boolean solve(SolveDTO dto) {
        List<RemovePictureTaggingDetail> details = new ArrayList<>();
        List<RemovePictureTaggingFile> files = new ArrayList<>();
        List<String> inspectionPictureTaggingIds = Func.toStrList(dto.getInspectionPictureTaggingIds());

        //先清空详情和文件信息
        removePictureTaggingDetailService.remove(new LambdaQueryWrapper<RemovePictureTaggingDetail>()
                .in(RemovePictureTaggingDetail::getInspectionPictureTaggingId, inspectionPictureTaggingIds));
        removePictureTaggingFileService.remove(new LambdaQueryWrapper<RemovePictureTaggingFile>()
                .in(RemovePictureTaggingFile::getInspectionPictureTaggingId, inspectionPictureTaggingIds));

        inspectionPictureTaggingIds.forEach(e -> {
            RemovePictureTaggingDetail detail = new RemovePictureTaggingDetail();
            detail.setInspectionPictureTaggingId(e);
            detail.setRealTagReason(dto.getRealTagReason());
            detail.setRemark(dto.getRemark());
            details.add(detail);
            if (CollectionUtil.isNotEmpty(dto.getZFiles())) {
                dto.getZFiles().forEach(file -> {
                    RemovePictureTaggingFile taggingFile = new RemovePictureTaggingFile();
                    taggingFile.setInspectionPictureTaggingId(e);
                    taggingFile.setRemoveTaggingFileType(file.getRemoveTaggingFileType() + "_z");
                    taggingFile.setFileGuid(file.getFileGuid());
                    files.add(taggingFile);
                });
            }

            if (CollectionUtil.isNotEmpty(dto.getTFiles())) {
                dto.getTFiles().forEach(file -> {
                    RemovePictureTaggingFile taggingFile = new RemovePictureTaggingFile();
                    taggingFile.setInspectionPictureTaggingId(e);
                    taggingFile.setRemoveTaggingFileType(file.getRemoveTaggingFileType() + "_t");
                    taggingFile.setFileGuid(file.getFileGuid());
                    files.add(taggingFile);
                });
            }

        });
        removePictureTaggingDetailService.saveBatch(details);
        removePictureTaggingFileService.saveBatch(files);

        //更新每个缺陷的任务状态
        // 获取当前登录用户ID
        String currentUserId = AuthUtil.getUserId();

        // 查询出相关的RemovePictureTagging记录，获取对应的RemoveTask信息来判断设备类型
        List<RemovePictureTagging> pictureTaggingList = removePictureTaggingService.list(
                new LambdaQueryWrapper<RemovePictureTagging>()
                        .in(RemovePictureTagging::getInspectionPictureTaggingId, inspectionPictureTaggingIds));

        // 获取所有相关的RemoveTask ID
        List<String> removeTaskIds = pictureTaggingList.stream()
                .map(RemovePictureTagging::getRemoveTaskId)
                .distinct()
                .collect(Collectors.toList());

        // 查询RemoveTask信息获取设备类型
        List<RemoveTask> removeTaskList = listByIds(removeTaskIds);
        Map<String, String> taskDeviceTypeMap = removeTaskList.stream()
                .collect(Collectors.toMap(RemoveTask::getId, RemoveTask::getDeviceType));

        // 分别处理光伏和非光伏设备类型的更新
        List<String> pvTaggingIds = new ArrayList<>();
        List<String> nonPvTaggingIds = new ArrayList<>();

        for (RemovePictureTagging tagging : pictureTaggingList) {
            String deviceType = taskDeviceTypeMap.get(tagging.getRemoveTaskId());
            if (BizDictEnum.DEVICE_TYPE_PV.getCode().equals(deviceType)) {
                pvTaggingIds.add(tagging.getInspectionPictureTaggingId());
            } else {
                nonPvTaggingIds.add(tagging.getInspectionPictureTaggingId());
            }
        }

        boolean flag = true;

        // 更新光伏设备类型的记录（设置消缺人员，支持多个消缺人员以逗号分割）
        if (CollectionUtil.isNotEmpty(pvTaggingIds)) {
            // 查询光伏设备的RemovePictureTagging记录
            List<RemovePictureTagging> pvTaggingList = removePictureTaggingService.list(
                    new LambdaQueryWrapper<RemovePictureTagging>()
                            .in(RemovePictureTagging::getInspectionPictureTaggingId, pvTaggingIds));

            // 处理每个记录的消缺人员
            for (RemovePictureTagging tagging : pvTaggingList) {
                String existingRemoveUser = tagging.getRemoveUser();
                String newRemoveUser;

                if (StrUtil.isBlank(existingRemoveUser)) {
                    // 如果为空，直接设置为当前用户
                    newRemoveUser = currentUserId;
                } else {
                    // 如果不为空，检查是否已包含当前用户
                    List<String> userList = Arrays.asList(existingRemoveUser.split(","));
                    if (!userList.contains(currentUserId)) {
                        newRemoveUser = existingRemoveUser + "," + currentUserId;
                    } else {
                        newRemoveUser = existingRemoveUser; // 已存在，不需要修改
                    }
                }

                tagging.setRemoveUser(newRemoveUser);
                tagging.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_COMPLETED.getCode());
                tagging.setRemoveTime(new Date());
            }

            // 批量更新
            flag = removePictureTaggingService.updateBatchById(pvTaggingList);
        }

        // 更新非光伏设备类型的记录（设置消缺人员为当前登录用户）
        if (CollectionUtil.isNotEmpty(nonPvTaggingIds)) {
            boolean nonPvFlag = removePictureTaggingService.update(new LambdaUpdateWrapper<RemovePictureTagging>()
                    .set(RemovePictureTagging::getRemoveTaskStatus, BizDictEnum.REMOVE_STATUS_COMPLETED.getCode())
                    .set(RemovePictureTagging::getRemoveTime, new Date())
                    .set(RemovePictureTagging::getRemoveUser, currentUserId)
                    .in(RemovePictureTagging::getInspectionPictureTaggingId, nonPvTaggingIds));
            flag = flag && nonPvFlag;
        }


        //更新缺陷告警状态
        List<AlarmInfo> alarmInfoList = new ArrayList<>();
        inspectionPictureTaggingIds.forEach(e -> {
            R<AlarmInfo> infoR = alarmClient.getInfoByTaggingId(e);
            if (infoR.isSuccess() && infoR.getData() != null) {
                AlarmInfo info = infoR.getData();
                // 告警状态设置为已处理
                info.setAlarmStatus(3);
                alarmInfoList.add(info);
            }

        });
        alarmClient.updateBatch(alarmInfoList);


        log.info("消缺任务状态更新完成，光伏设备{}个，非光伏设备{}个", pvTaggingIds.size(), nonPvTaggingIds.size());
        boolean own = inspectionPictureTaggingService.update(new LambdaUpdateWrapper<InspectionPictureTagging>()
                .set(InspectionPictureTagging::getEliminateStatus, BizDictEnum.TAGGING_TYPE_ALREADY_VANISH_DEFECT.getCode())
                .in(InspectionPictureTagging::getId, inspectionPictureTaggingIds));

        //根据tagIds 查询 任务ids
        //判断消缺任务下的缺陷是否已经全部消缺  如果全部 把任务整个 完成
        List<String> taskIds = baseMapper.selectCompletedTask(inspectionPictureTaggingIds);
        if (CollectionUtil.isNotEmpty(taskIds)) {
            // 更新所有任务状态为完成
            update(
                    new LambdaUpdateWrapper<RemoveTask>()
                            .in(RemoveTask::getId, taskIds)
                            .set(RemoveTask::getRemoveTaskStatus, BizDictEnum.REMOVE_STATUS_COMPLETED.getCode()));

        }
        return flag && own;
    }

    @Override
    public RemoveTaskVO detail(RemoveTaskDetailDTO detailDTO) {
        RemoveTask task = getById(detailDTO.getId());

        RemoveTaskVO vo = new RemoveTaskVO();
        vo.setRemoveTaskName(task != null ? task.getRemoveTaskName() : "");

        InspectionPictureTaggingVO pictureTaggingVO = null;
        switch (detailDTO.getDeviceType()) {
            case TMS_LINE:
                pictureTaggingVO = inspectionPictureTaggingMapper.getLineRemoveTaskDetail(detailDTO.getInspectionPictureTaggingId());
                break;
            case FAN:
                pictureTaggingVO = inspectionPictureTaggingMapper.getFanRemoveTaskDetail(detailDTO.getInspectionPictureTaggingId());
                break;
            case PV:
                pictureTaggingVO = inspectionPictureTaggingMapper.getPvRemoveTaskDetail(detailDTO.getInspectionPictureTaggingId());
                break;
            default:
                log.warn("不支持的设备类型: {}", detailDTO.getDeviceType());
                break;
        }

        // 添加空值检查，防止NullPointerException
        if (pictureTaggingVO == null) {
            log.error("未找到巡检图片标注详情，设备类型: {}, 标注ID: {}",
                detailDTO.getDeviceType(), detailDTO.getInspectionPictureTaggingId());
            throw new ServiceException("未找到相关的巡检图片标注信息");
        }

        //设备类型、设备名称、缺陷描述、缺陷等级、缺陷图片
        vo.setDeviceTypeZh(BizDictEnum.getNameByCode(detailDTO.getDeviceType()));
        vo.setDeviceName(pictureTaggingVO.getDeviceName());
        vo.setDefectDescriptionZh(CommonUtil.transDefectCode(pictureTaggingVO.getDefectDescription(), detailDTO.getDeviceType()));
        vo.setDefectLevel(pictureTaggingVO.getDefectLevel());
        vo.setDefectLevelZh(DictBizCache.getValue(BizDictEnum.DEFECT_LEVEL.getCode(), pictureTaggingVO.getDefectLevel()));

        // 添加文件GUID的空值检查
        if (StringUtil.isNotBlank(pictureTaggingVO.getFileGuid())) {
            R<AllcoreFileVO> rstR = ossClient.getFileDetail(pictureTaggingVO.getFileGuid());
            if (rstR.isSuccess()) {
                vo.setDefectFilePath(rstR.getData().getDynamicPath());
            }
        }

        if (detailDTO.getDeviceType().equals(PV)) {
            if (StringUtil.isNotBlank(pictureTaggingVO.getLightFileGuid())) {
                R<AllcoreFileVO> rstR = ossClient.getFileDetail(pictureTaggingVO.getLightFileGuid());
                if (rstR.isSuccess()) {
                    vo.setDefectLightFilePath(rstR.getData().getDynamicPath());
                }
            }
        }

        //
        RemovePictureTaggingDetail detail = removePictureTaggingDetailService.getOne(new LambdaQueryWrapper<RemovePictureTaggingDetail>()
                .eq(RemovePictureTaggingDetail::getInspectionPictureTaggingId, detailDTO.getInspectionPictureTaggingId()));
        if (Func.isNotEmpty(detail)) {
            vo.setRealTagReason(detail.getRealTagReason());
            vo.setRemark(detail.getRemark());
        }
        Optional.ofNullable(removePictureTaggingMapper.selectOne(new LambdaQueryWrapper<RemovePictureTagging>()
                .eq(RemovePictureTagging::getInspectionPictureTaggingId, detailDTO.getInspectionPictureTaggingId())
                .last("limit 1"))).ifPresent(e -> {
            vo.setRemoveTime(e.getRemoveTime());
            if (StringUtils.isNotBlank(e.getRemoveUser())) {

                // 处理多个消缺人员情况，以逗号分割
                String[] userIds=e.getRemoveUser().split(",");
                StringBuilder userNames=new StringBuilder();
                for (int i = 0; i < userIds.length; i++) {
                    String userId = userIds[i].trim();
                    if (StringUtil.isNotBlank(userId)) {
                        User user = UserCache.getUser(userId);
                        String userName = Func.isNull(user) ? "" : user.getRealName();
                        if (StringUtil.isNotBlank(userName)) {
                            if (i > 0) {
                                userNames.append(",");
                            }
                            userNames.append(userName);
                        }
                    }
                }
                vo.setRemoveUserZh(userNames.toString());
            }
        });


        List<RemovePictureTaggingFile> files = removePictureTaggingFileService.list(new LambdaQueryWrapper<RemovePictureTaggingFile>()
                .eq(RemovePictureTaggingFile::getInspectionPictureTaggingId, detailDTO.getInspectionPictureTaggingId()));
        List<RemovePictureTaggingFileVO> fileVOList = new ArrayList<>();
        R<List<AllcoreFileVO>> rstListR = ossClient.getFilesDetail(files.stream().map(RemovePictureTaggingFile::getFileGuid).collect(Collectors.toList()));
        if (rstListR.isSuccess()) {
            files.forEach(e -> {
                rstListR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        RemovePictureTaggingFileVO fileVO = new RemovePictureTaggingFileVO();
                        fileVO.setRemoveTaggingFileType(e.getRemoveTaggingFileType());
                        fileVO.setFileGuid(e.getFileGuid());
                        fileVO.setFilePath(r.getDynamicPath());
                        fileVOList.add(fileVO);
                    }
                });
            });
        }
        vo.setZFileVOS(fileVOList.stream().filter(e -> e.getRemoveTaggingFileType().contains("_z")).collect(Collectors.toList()));
        vo.setTFileVOS(fileVOList.stream().filter(e -> e.getRemoveTaggingFileType().contains("_t")).collect(Collectors.toList()));
        return vo;
    }

    @Override
    public List<TreeNode> defectLineTree() {

        String deptCode = AuthUtil.getDeptCode();
        //场站-线路-杆塔
        //查询有缺陷的场站
        List<String> deptCodes = inspectionPictureTaggingMapper.getDeptDefectList(TMS_LINE, deptCode);
        //查询有缺陷的线路
        List<LineDefectVO> lineDefectList = inspectionPictureTaggingMapper.getLineDefectList(deptCode);
        //查询有缺陷的杆塔
        List<TowerDefectVO> towerDefectList = inspectionPictureTaggingMapper.getTowerDefectList(deptCode);

        //最终集合
        List<TreeNode> resultList = new ArrayList<>();

        List<Dept> allDept = new ArrayList<>();
        deptCodes.forEach(e -> {
            Dept dept = SysCache.getDeptByDeptCode(e);
            if (Func.isNotEmpty(dept) && StringUtil.isNotBlank(dept.getId())) {
                allDept.add(dept);
            }
        });

        //单位
        List<TreeNode> deptList = new ArrayList<>();
        buildDeptList(resultList, deptList, allDept);

        //线路
        List<TreeNode> lineList = new ArrayList<>();
        buildLineList(deptList, lineList, lineDefectList);

        //杆塔
        List<TreeNode> towerList = new ArrayList<>();
        buildTowerList(lineList, towerList, towerDefectList);

        return ForestNodeMerger.merge(resultList);
    }


    private void buildDeptList(List<TreeNode> resultList, List<TreeNode> deptList, List<Dept> allDept) {
        List<TreeNode> tree = new ArrayList<>();
        for (Dept dept : allDept) {
            TreeNode vo = new TreeNode();
            vo.setTitle(dept.getDeptName());
            vo.setId(dept.getDeptCode());
            vo.setParentId(StringPool.ZERO);
            vo.setHasChildren(false);
            vo.setType(StringPool.ZERO);
            tree.add(vo);
        }
        deptList.addAll(tree);
        resultList.addAll(deptList);
    }

    private void buildLineList(List<TreeNode> deptList, List<TreeNode> lineList, List<LineDefectVO> lineDefectList) {
        for (TreeNode treeNode : deptList) {
            List<TreeNode> tree = new ArrayList<>();
            //过滤出该单位的数据
            List<LineDefectVO> list = lineDefectList.stream().filter(e -> e.getDeptCode().equals(treeNode.getId())).collect(Collectors.toList());
            for (LineDefectVO lineDefectVO : list) {
                TreeNode vo = new TreeNode();
                vo.setTitle(lineDefectVO.getLineName());
                vo.setId(lineDefectVO.getId());
                vo.setParentId(treeNode.getId());
                vo.setHasChildren(false);
                vo.setType(TMS_LINE);
                tree.add(vo);
            }

            if (CollectionUtil.isNotEmpty(tree)) {
                treeNode.setChildren(tree);
                lineList.addAll(tree);
            } else {
                treeNode.setHasChildren(false);
            }
        }
    }

    private void buildTowerList(List<TreeNode> lineList, List<TreeNode> towerList, List<TowerDefectVO> towerDefectList) {
        for (TreeNode treeNode : lineList) {
            List<TreeNode> tree = new ArrayList<>();
            //过滤出该单位的数据
            List<TowerDefectVO> list = towerDefectList.stream().filter(e -> e.getLineId().equals(treeNode.getId())).collect(Collectors.toList());
            for (TowerDefectVO towerDefectVO : list) {
                TreeNode vo = new TreeNode();
                vo.setTitle(towerDefectVO.getTowerName());
                vo.setId(towerDefectVO.getId());
                vo.setParentId(treeNode.getId());
                vo.setHasChildren(false);
                vo.setType(TMS_TOWER);
                tree.add(vo);
            }
            if (CollectionUtil.isNotEmpty(tree)) {
                treeNode.setChildren(tree);
                towerList.addAll(tree);
            } else {
                treeNode.setHasChildren(false);
            }
        }
    }


    @Override
    public List<TreeNode> defectFanTree() {
        String deptCode = AuthUtil.getDeptCode();
        //场站-风机
        //查询有缺陷的场站
        List<String> deptCodes = inspectionPictureTaggingMapper.getDeptDefectList(FAN, deptCode);
        //查询有缺陷的线路
        List<FanDefectVO> fanDefectList = inspectionPictureTaggingMapper.getFanDefectList(deptCode);

        //最终集合
        List<TreeNode> resultList = new ArrayList<>();

        List<Dept> allDept = new ArrayList<>();
        deptCodes.forEach(e -> {
            Dept dept = SysCache.getDeptByDeptCode(e);
            if (Func.isNotEmpty(dept) && StringUtil.isNotBlank(dept.getId())) {
                allDept.add(dept);
            }
        });

        //单位
        List<TreeNode> deptList = new ArrayList<>();
        buildDeptList(resultList, deptList, allDept);

        //风机
        List<TreeNode> fanList = new ArrayList<>();
        buildFanList(deptList, fanList, fanDefectList);

        return ForestNodeMerger.merge(resultList);
    }

    private void buildFanList(List<TreeNode> deptList, List<TreeNode> fanList, List<FanDefectVO> fanDefectList) {
        for (TreeNode treeNode : deptList) {
            List<TreeNode> tree = new ArrayList<>();
            //过滤出该单位的数据
            List<FanDefectVO> list = fanDefectList.stream().filter(e -> e.getDeptCode().equals(treeNode.getId())).collect(Collectors.toList());
            for (FanDefectVO lineDefectVO : list) {
                TreeNode vo = new TreeNode();
                vo.setTitle(lineDefectVO.getDeviceName());
                vo.setId(lineDefectVO.getId());
                vo.setParentId(treeNode.getId());
                vo.setHasChildren(false);
                vo.setType(FAN);
                tree.add(vo);
            }

            if (CollectionUtil.isNotEmpty(tree)) {
                treeNode.setChildren(tree);
                fanList.addAll(tree);
            } else {
                treeNode.setHasChildren(false);
            }
        }
    }

    @Override
    public List<TreeNode> defectPvTree() {
        String deptCode = AuthUtil.getDeptCode();
        //场站-风机
        //查询有缺陷的场站
        List<String> deptCodes = inspectionPictureTaggingMapper.getDeptDefectList(PV, deptCode);
        //查询有缺陷的线路
        List<PvDefectVO> pvDefectList = inspectionPictureTaggingMapper.getPvDefectList(deptCode);

        //最终集合
        List<TreeNode> resultList = new ArrayList<>();

        List<Dept> allDept = new ArrayList<>();
        deptCodes.forEach(e -> {
            Dept dept = SysCache.getDeptByDeptCode(e);
            if (Func.isNotEmpty(dept) && StringUtil.isNotBlank(dept.getId())) {
                allDept.add(dept);
            }
        });

        //单位
        List<TreeNode> deptList = new ArrayList<>();
        buildDeptList(resultList, deptList, allDept);

        //光伏
        List<TreeNode> pvList = new ArrayList<>();
        buildPvList(deptList, pvList, pvDefectList);

        return ForestNodeMerger.merge(resultList);
    }

    private void buildPvList(List<TreeNode> deptList, List<TreeNode> pvList, List<PvDefectVO> pvDefectList) {
        for (TreeNode treeNode : deptList) {
            List<TreeNode> tree = new ArrayList<>();
            //过滤出该单位的数据
            List<PvDefectVO> list = pvDefectList.stream().filter(e -> e.getDeptCode().equals(treeNode.getId())).collect(Collectors.toList());
            for (PvDefectVO lineDefectVO : list) {
                TreeNode vo = new TreeNode();
                vo.setTitle(lineDefectVO.getDeviceName());
                vo.setId(lineDefectVO.getId());
                vo.setParentId(treeNode.getId());
                vo.setHasChildren(false);
                vo.setType(PV);
                tree.add(vo);
            }

            if (CollectionUtil.isNotEmpty(tree)) {
                treeNode.setChildren(tree);
                pvList.addAll(tree);
            } else {
                treeNode.setHasChildren(false);
            }
        }
    }

    @Override
    public IPage<RemoveTaskPageVO> selectTaskPage(IPage<RemoveTaskPageVO> page, RemoveTaskQueryDTO dto) {
        //插入单位code,无值查自己所有，如果有值则查询值下的
        if (StringUtil.isBlank(dto.getDeptCode())) {
            dto.setDeptCode(AuthUtil.getDeptCode());
        }
        if (Func.isNotEmpty(dto.getTreeType()) && Func.isNotEmpty(dto.getTreeId()) && !"0".equals(dto.getTreeType())) {

            //提取单位code和设备类型
            boolean isContained = dto.getTreeId().contains(dto.getTreeType());
            if (isContained) {
                Pattern pattern = Pattern.compile("\\d+");
                Matcher matcher = pattern.matcher(dto.getTreeId());
                if (matcher.find()) {
                    dto.setDeptCode(matcher.group());
                    dto.setDeviceType(dto.getTreeType());
                    dto.setTreeId(null);
                    dto.setTreeType(null);
                }
            }

        }
        List<RemoveTaskPageVO> list = baseMapper.selectUnifiedTaskPage(page, dto);
        List<String> fileGuids = list.stream().map(RemoveTaskPageVO::getFileGuid).collect(Collectors.toList());
        R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
        if (rstR.isSuccess()) {
            list.forEach(e -> {
                rstR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setFilePath(r.getDynamicPath());
                    }
                });
            });
        }

        // 处理PV设备类型的光照文件路径（从列表中筛选PV类型的记录）
        List<String> lightFileGuids = list.stream()
                .filter(e -> PV.equals(e.getDeviceType()) && StringUtil.isNotBlank(e.getLightFileGuid()))
                .map(RemoveTaskPageVO::getLightFileGuid)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(lightFileGuids)) {
            R<List<AllcoreFileVO>> rstR1 = ossClient.getFilesDetail(lightFileGuids);
            if (rstR1.isSuccess()) {
                list.forEach(e -> {
                    if (PV.equals(e.getDeviceType()) && StringUtil.isNotBlank(e.getLightFileGuid())) {
                        rstR1.getData().forEach(r -> {
                            if (e.getLightFileGuid().equals(r.getFileGuid())) {
                                e.setLightFilePath(r.getDynamicPath());
                            }
                        });
                    }
                });
            }
        }
        return page.setRecords(list);
    }

    @Override
    public List<SolveTreeNode> getImageLibrary(ImageLibraryDTO dto) {
        dto.setDeptCode(StringUtil.isBlank(dto.getDeptCode()) ? AuthUtil.getDeptCode() : dto.getDeptCode());

        //最终集合
        List<SolveTreeNode> resultList = new ArrayList<>();
        List<ImageLibraryVO> voList;
        Function<ImageLibraryVO, List<String>> parentDeviceKey;
        Function<ImageLibraryVO, List<String>> deviceKey;
        Function<ImageLibraryVO, List<String>> dateKey;
        Map<List<String>, List<ImageLibraryVO>> parentDeviceMap;
        Map<List<String>, List<ImageLibraryVO>> deviceMap;
        Map<List<String>, List<ImageLibraryVO>> dateMap;
        List<SolveTreeNode> firstList;
        List<SolveTreeNode> towerList;
        List<SolveTreeNode> dateList;


        switch (dto.getDeviceType()) {
            case TMS_LINE:
                voList = baseMapper.getLineImageLibrary(dto);
                //赋值文件路径
                handleLibraryFilePath(voList);
                parentDeviceKey = vo ->
                        Arrays.asList(vo.getParentDeviceId(), vo.getParentDeviceName());

                deviceKey = vo ->
                        Arrays.asList(vo.getParentDeviceId(), vo.getParentDeviceName(), vo.getDeviceId(), vo.getDeviceName());

                dateKey = vo ->
                        Arrays.asList(vo.getParentDeviceId(), vo.getParentDeviceName(), vo.getDeviceId(), vo.getDeviceName(), vo.getDate());

                // 分组
                parentDeviceMap = voList.stream().collect(Collectors.groupingBy(parentDeviceKey, Collectors.toList()));
                deviceMap = voList.stream().collect(Collectors.groupingBy(deviceKey, Collectors.toList()));
                dateMap = voList.stream().collect(Collectors.groupingBy(dateKey, Collectors.toList()));

                firstList = new ArrayList<>();
                buildSolveFirstList(resultList, firstList, parentDeviceMap);

                towerList = new ArrayList<>();
                buildSolveTowerList(firstList, towerList, deviceMap);

                dateList = new ArrayList<>();
                buildSolveDateList(towerList, dateList, dateMap, 4);
                break;
            case FAN:
                voList = baseMapper.getFanImageLibrary(dto);
                //赋值文件路径
                handleLibraryFilePath(voList);
                buildSolveTreeRst(voList, resultList);
                break;
            case PV:
                voList = baseMapper.getPvImageLibrary(dto);
                //赋值文件路径
                handleLibraryFilePath(voList);
                buildSolveTreeRst(voList, resultList);
                break;
            default:
                break;
        }

        return ForestNodeMerger.merge(resultList);
    }

    private void handleLibraryFilePath(List<ImageLibraryVO> voList) {
        R<List<AllcoreFileVO>> rstListR = ossClient.getFilesDetail(voList.stream().map(ImageLibraryVO::getFileGuid).collect(Collectors.toList()));
        if (rstListR.isSuccess()) {
            voList.forEach(e -> {
                rstListR.getData().forEach(r -> {
                    if (e.getFileGuid().equals(r.getFileGuid())) {
                        e.setFilePath(r.getDynamicPath());
                    }
                });
            });
        }
    }

    private void buildSolveTreeRst(List<ImageLibraryVO> voList, List<SolveTreeNode> resultList) {
        Function<ImageLibraryVO, List<String>> deviceKey = vo ->
                Arrays.asList(vo.getDeviceId(), vo.getDeviceName());

        Function<ImageLibraryVO, List<String>> dateKey = vo ->
                Arrays.asList(vo.getDeviceId(), vo.getDeviceName(), vo.getDate());

        // 分组
        Map<List<String>, List<ImageLibraryVO>> deviceMap = voList.stream().collect(Collectors.groupingBy(deviceKey, Collectors.toList()));
        Map<List<String>, List<ImageLibraryVO>> dateMap = voList.stream().collect(Collectors.groupingBy(dateKey, Collectors.toList()));

        List<SolveTreeNode> firstList = new ArrayList<>();
        buildSolveFirstList(resultList, firstList, deviceMap);

        List<SolveTreeNode> dateList = new ArrayList<>();
        buildSolveDateList(firstList, dateList, dateMap, 2);
    }


    private void buildSolveFirstList(List<SolveTreeNode> resultList, List<SolveTreeNode> list, Map<List<String>, List<ImageLibraryVO>> map) {
        List<SolveTreeNode> tree = new ArrayList<>();
        map.forEach((k, v) -> {
            SolveTreeNode vo = new SolveTreeNode();
            vo.setTitle(k.get(1));
            vo.setId(k.get(0));
            vo.setParentId(StringPool.ZERO);
            vo.setHasChildren(false);
            vo.setSize(v.size());
            tree.add(vo);
        });
        list.addAll(tree);
        resultList.addAll(list);
    }

    private void buildSolveTowerList(List<SolveTreeNode> towerList, List<SolveTreeNode> dateList, Map<List<String>, List<ImageLibraryVO>> map) {
        for (SolveTreeNode treeNode : towerList) {
            List<String> smallList = new ArrayList<>();
            smallList.add(treeNode.getId());
            smallList.add(treeNode.getTitle());
            List<SolveTreeNode> tree = new ArrayList<>();
            map.forEach((k, v) -> {
                if (k.containsAll(smallList)) {
                    SolveTreeNode vo = new SolveTreeNode();
                    vo.setTitle(k.get(3));
                    vo.setId(k.get(2));
                    vo.setParentId(treeNode.getId());
                    vo.setHasChildren(false);
                    vo.setSize(v.size());
                    tree.add(vo);
                }
            });

            if (CollectionUtil.isNotEmpty(tree)) {
                treeNode.setChildren(tree);
                dateList.addAll(tree);
            } else {
                treeNode.setHasChildren(false);
            }
        }
    }

    private void buildSolveDateList(List<SolveTreeNode> dateParentList, List<SolveTreeNode> dateList, Map<List<String>, List<ImageLibraryVO>> map, Integer index) {
        for (SolveTreeNode treeNode : dateParentList) {
            List<String> smallList = new ArrayList<>();
            smallList.add(treeNode.getId());
            smallList.add(treeNode.getTitle());
            List<SolveTreeNode> tree = new ArrayList<>();
            map.forEach((k, v) -> {
                if (k.containsAll(smallList)) {
                    SolveTreeNode vo = new SolveTreeNode();
                    vo.setTitle(k.get(index));
                    vo.setId(k.get(index));
                    vo.setParentId(treeNode.getId());
                    vo.setHasChildren(false);
                    vo.setSize(v.size());
                    vo.setPicVOList(v.stream().filter(e -> "picture_z".equals(e.getFileType()) || "picture_t".equals(e.getFileType())).collect(Collectors.toList()));
                    vo.setVideoVOList(v.stream().filter(e -> "video_z".equals(e.getFileType()) || "video_t".equals(e.getFileType())).collect(Collectors.toList()));
                    tree.add(vo);
                }
            });

            if (CollectionUtil.isNotEmpty(tree)) {
                treeNode.setChildren(tree);
                dateList.addAll(tree);
            } else {
                treeNode.setHasChildren(false);
            }
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean saveRecord(RemoveLibraryDownLoadDTO dto) throws IOException {
        RemoveLibraryDownLoad libraryDownLoad = new RemoveLibraryDownLoad();
        libraryDownLoad.setDeviceId(dto.getDeviceId());
        libraryDownLoad.setRemark(dto.getRemark());
        removeLibraryDownLoadService.save(libraryDownLoad);
        return zipPackage(libraryDownLoad, dto);
    }

    @Async
    public boolean zipPackage(RemoveLibraryDownLoad libraryDownLoad, RemoveLibraryDownLoadDTO dto) throws IOException {
        List<SolveTreeNode> data = dto.getData();
        List<String> guidList = dto.getFileGuids();

        if (CollectionUtils.isNotEmpty(data)) {
            String temppath = CommonConstant.TEMPPATH;
            File file = new File(temppath);
            if (!file.exists()) {
                file.mkdirs();
            }
            String format = CommonUtil.generateUuid();
            FileUtils.createDir(temppath + format);
            String date = DateUtil.format(new Date(), DateUtil.PATTERN_DATETIME_MINI);
            R<List<AllcoreFileVO>> listR = ossClient.getFilesDetail(guidList);
            if (!listR.isSuccess()) {
                return false;
            }
            //从全量树数据中将选中的文件打包
            createDeviceTreeFile(temppath + format, data, guidList);
            String zip = "";
            try {
                zip = ZipUtil.zip(temppath + format, temppath, date + ".zip", true);
                File zipFile = new File(zip);
                FileInputStream fileInputStream = new FileInputStream(zipFile);
                MultipartFile multipartFile = new MockMultipartFile(zipFile.getName(), zipFile.getName(),
                        ContentType.APPLICATION_OCTET_STREAM.toString(), fileInputStream);
                R<AllcoreFileVO> r = ossClient.putFileAttach(
                        BizEnum.BIZ_CODE_CALLOUT_PICTURE.getCode(),
                        multipartFile,
                        StringPool.NO,
                        "");
                if (r.isSuccess()) {
                    libraryDownLoad.setVideoStatus(StringPool.ONE);
                    libraryDownLoad.setFileGuid(r.getData().getFileGuid());
                    return removeLibraryDownLoadService.saveOrUpdate(libraryDownLoad);
                }
                //将zip包上传到文件服务，保存文件服务返回的地址和fileGuid
            } catch (ZipException e) {
                e.printStackTrace();
            } finally {
                ZipUtil.deleteDir(temppath + format);
                File fileRoot = new File(zip);
                fileRoot.delete();
            }
        }
        return false;
    }

    private void createDeviceTreeFile(String tempPath, List<SolveTreeNode> data, List<String> guidList) {

        data.forEach(e -> {
            String picPath = tempPath + ZipUtil.FILE_SEPARATOR + e.getTitle();
            //可能是日期层 或者 杆塔层
            List<SolveTreeNode> second = e.getChildren();
            second.forEach(a -> {
                String subPath = picPath + ZipUtil.FILE_SEPARATOR + a.getTitle();
                //杆塔层
                if (a.getHasChildren()) {
                    //日期层
                    List<SolveTreeNode> third = a.getChildren();
                    third.forEach(b -> {
                        String subChildPath = subPath + ZipUtil.FILE_SEPARATOR + b.getTitle();
                        packagePicFile(subChildPath, b, guidList);
                    });
                } else {
                    //日期层
                    packagePicFile(subPath, a, guidList);
                }
            });
        });
    }


    private void packagePicFile(String picPath, SolveTreeNode data, List<String> guidList) {
        List<ImageLibraryVO> list = new ArrayList<>();
        List<ImageLibraryVO> picVOList = data.getPicVOList();
        List<ImageLibraryVO> videoVOList = data.getVideoVOList();
        list.addAll(picVOList);
        list.addAll(videoVOList);

        for (ImageLibraryVO pic : list) {
            for (String checkFileGuid : guidList) {
                if (checkFileGuid.equals(pic.getFileGuid())) {
                    String temppath = picPath + ZipUtil.FILE_SEPARATOR;
                    String fileType = pic.getFileType();
                    if ("picture_z".equals(fileType) || "picture_t".equals(fileType)) {
                        temppath = temppath + PIC + ZipUtil.FILE_SEPARATOR;
                    } else {
                        temppath = temppath + VIDEO + ZipUtil.FILE_SEPARATOR;
                    }
                    File file = new File(temppath);
                    if (!file.exists()) {
                        file.mkdirs();
                    }
                    String filePath = systemProperties.getWebUrl() + StringPool.SLASH + pic.getFilePath();
                    String fileName = filePath.substring(filePath.lastIndexOf("/") + 1);
                    FileUtils.loadImg(filePath, temppath + fileName);
                }

            }
        }
    }


    @Override
    public RemoveTaskCollectAppVO taskInfo() {
        String userId = AuthUtil.getUserId();

        //TODO 当所有下发的缺陷都已处理后 需要把消缺任务的状态也改成已完成
        Integer allNum = baseMapper.allTaskInfo(userId);
        Integer completedTaskNum = baseMapper.completedTaskInfo(userId);
        RemoveTaskCollectAppVO rst = new RemoveTaskCollectAppVO();
        rst.setAllTaskNum(allNum);
        rst.setCompletedTaskNum(completedTaskNum);

        return rst;
    }


    @Override
    public List<DefectHistoryVO> history(String inspectionPictureTaggingId, String pvComponentId) {
        List<DefectHistoryVO> rst = baseMapper.history(pvComponentId, AuthUtil.getUserId());
        if (CollectionUtil.isNotEmpty(rst)) {
            rst.forEach(e -> {
                e.setStartToEndTime(e.getStartDate());
            });
        }
        return rst;
    }

    @Override
    public List<String> getWorkOrderNoList(String deptCode, String deviceType) {
        List<String> workOrderNos = new ArrayList<>();
        List<RemoveTask> list = this.list(new QueryWrapper<RemoveTask>().lambda()
                .eq(StrUtil.isNotBlank(deviceType), RemoveTask::getDeviceType, deviceType)
                .likeRight(RemoveTask::getDeptCode, StrUtil.isBlank(deptCode) ? AuthUtil.getDeptCode() : deptCode));
        if (CollectionUtil.isNotEmpty(list)) {
            workOrderNos = list.stream().map(x -> x.getInspectionTaskNo()).distinct().collect(Collectors.toList());
        }
        return workOrderNos;
    }

    @Override
    public List<String> getRemoveNoList(String deptCode, String deviceType) {

        if (StringUtil.isBlank(deptCode)) {
            deptCode = AuthUtil.getDeptCode();
        }
        return baseMapper.getRemoveNoList(deptCode, deviceType);
    }

    @Override
    public void addMoreRemoveTagByTask(RemoveTaskDTO dto) {
        List<RemoveTask> removeTaskDbList = list(new QueryWrapper<RemoveTask>().lambda()
                .eq(RemoveTask::getInspectionTaskId, dto.getInspectionTaskId())
                .eq(RemoveTask::getDeviceType, dto.getDeviceType()));

        //如果算法识别无缺陷 此时还没有任务
        //如果已经有任务了
        if (CollectionUtil.isEmpty(removeTaskDbList)) {
            RemoveTask removeTask = BeanUtil.copy(dto, RemoveTask.class);
            save(removeTask);
            List<RemovePictureTagging> pictureTaggings = new ArrayList<>();
            //生成消缺编号XQ-20231219093026807-002、20231219093026807取对应巡检任务编号中间的 002 按list的i生成
            String time = dto.getInspectionTaskNo().split(StringPool.DASH)[1];
            for (int i = 0; i < dto.getInspectionPictureTaggingIds().size(); i++) {
                String e = dto.getInspectionPictureTaggingIds().get(i);
                RemovePictureTagging pictureTagging = new RemovePictureTagging();
                pictureTagging.setRemoveTaskId(removeTask.getId());
                pictureTagging.setInspectionPictureTaggingId(e);
                pictureTagging.setRemoveTaskNo(XQ + StringPool.DASH + time + StringPool.DASH + String.format("%03d", i + 1));
                pictureTaggings.add(pictureTagging);
                if (BizDictEnum.DEVICE_TYPE_PV.getCode().equals(removeTask.getDeviceType())) {
                    pictureTagging.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_TO_SEND.getCode());
                } else {
                    pictureTagging.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
                }
            }
            removePictureTaggingService.saveBatch(pictureTaggings);
        } else {
            log.info("追加消缺任务，找到{}个相关的消缺任务", removeTaskDbList.size());

            // 处理每个相关的消缺任务
            for (RemoveTask removeTaskDb : removeTaskDbList) {
                //追加 消缺任务
                RemovePictureTagging tagging = removePictureTaggingMapper.getMaxNoTag(removeTaskDb.getId());
                String maxNo = tagging == null ? "000" : tagging.getRemoveTaskNo().split(StringPool.DASH)[2];
                List<RemovePictureTagging> pictureTaggings = new ArrayList<>();
                //生成消缺编号XQ-20231219093026807-002、20231219093026807取对应巡检任务编号中间的 002 按list的i生成
                String time = dto.getInspectionTaskNo().split(StringPool.DASH)[1];

                List<String> currentInspectionPictureTaggingIds = new ArrayList<>(dto.getInspectionPictureTaggingIds());

                if (StringUtil.equals(PV, dto.getDeviceType())) {
                    //过滤掉 remove中 已有的组件
                    currentInspectionPictureTaggingIds = removePictureTaggingMapper.filterPvRemoveTag(
                            currentInspectionPictureTaggingIds,
                            dto.getInspectionTaskId(),
                            removeTaskDb.getId());
                }

                // 如果过滤后还有需要处理的标注ID，则进行处理
                if (CollectionUtil.isNotEmpty(currentInspectionPictureTaggingIds)) {
                    for (int i = 0; i < currentInspectionPictureTaggingIds.size(); i++) {
                        String e = currentInspectionPictureTaggingIds.get(i);
                        RemovePictureTagging pictureTagging = new RemovePictureTagging();
                        pictureTagging.setRemoveTaskId(removeTaskDb.getId());
                        pictureTagging.setInspectionPictureTaggingId(e);
                        pictureTagging.setRemoveTaskNo(XQ + StringPool.DASH + time + StringPool.DASH + String.format("%03d", Integer.valueOf(maxNo) + i + 1));
                        pictureTaggings.add(pictureTagging);
                        if (BizDictEnum.DEVICE_TYPE_PV.getCode().equals(removeTaskDb.getDeviceType())) {
                            pictureTagging.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_TO_SEND.getCode());
                        } else {
                            pictureTagging.setRemoveTaskStatus(BizDictEnum.REMOVE_STATUS_DOING.getCode());
                        }
                    }
                    removePictureTaggingService.saveBatch(pictureTaggings);
                    log.info("为消缺任务{}追加了{}个标注", removeTaskDb.getId(), pictureTaggings.size());
                }
            }
        }

    }

    @Override
    public R<List<NearByWorkOrderVO>> getNearByWorkOrder(NearByWorkOrderDTO dto) {

        if (Func.isAnyBlank(dto.getRemoveUser(), dto.getDeviceIds())) {
            return R.fail("缺少参数");
        }

        if (!BizDictEnum.INSPECTION_TYPE_PV.getCode().equals(dto.getDeviceType())) {
            return R.fail("不支持当前设备类型：" + dto.getDeviceType() + " 下发");
        }
        //对可能的重复设备id去重
        List<String> deviceIds = Func.toStrList(dto.getDeviceIds()).stream().distinct().collect(Collectors.toList());

        List<InspectionDeviceInfoVO> deviceInfoVOs = baseMapper.getDeviceInfo(deviceIds)
                .stream().peek(device -> {
                    device.setRemoveUser(dto.getRemoveUser());
                }).collect(Collectors.toList());
        if (Func.isEmpty(deviceInfoVOs)) {
            return R.fail("未找到当前消缺设备信息");
        }
        log.info("设备信息：{}", deviceInfoVOs);
        List<NearByWorkOrderVO> inspectTaskList = new ArrayList<>();

       inspectionTaskMapper.getNearByWorkOrder(deviceInfoVOs).stream().collect(Collectors.groupingBy(NearByWorkOrderVO::getInspectionTaskId))
               .forEach((x,y)->{

                   NearByWorkOrderVO vo=new NearByWorkOrderVO();
                   if(LocalDate.parse(y.get(0).getStartDate(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")).isAfter(LocalDate.now())){
                       vo.setInspectionTaskId(x);
                       vo.setInspectionTaskNo(y.get(0).getInspectionTaskNo());
                       vo.setInspectionTaskName(y.get(0).getInspectionTaskName());
                       vo.setInspectionTaskStatus(y.get(0).getInspectionTaskStatus());
                       vo.setStartDate(y.get(0).getStartDate());
                       vo.setDeviceType(y.get(0).getDeviceType());
                       StringBuilder sb=new StringBuilder();
                       y.forEach(e->{
                           sb.append(e.getDeviceName()).append(",");
                       });
                       // 检查最后一个字符是否是逗号，如果是则删除
                       if (sb.length() > 0 && sb.charAt(sb.length() - 1) == ',') {
                           sb.deleteCharAt(sb.length() - 1);
                       }
                       vo.setDeviceName(sb.toString());
                       vo.setDeptCode(y.get(0).getDeptCode());
                       vo.setInspectionDeviceId(y.get(0).getInspectionDeviceId());
                       vo.setRemoveTaskNo(dto.getRemoveTaskNo());
                       inspectTaskList.add(vo);
                   }
               });


        if (Func.isEmpty(inspectTaskList)) {
            log.error("未找到附近500米左右的巡检工单");
            return R.data(inspectTaskList, "未找到附近500米左右的巡检工单");
        }
        return R.data(inspectTaskList);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public boolean reverseSend(SolveSendDTO dto) {

        // 撤销RemovePictureTagging的更新，恢复为初始状态
        boolean flag = removePictureTaggingService.update(new LambdaUpdateWrapper<RemovePictureTagging>()
                .set(RemovePictureTagging::getRemoveTaskStatus, BizDictEnum.REMOVE_STATUS_TO_SEND.getCode())
                .set(RemovePictureTagging::getRemoveUser, null)
                .set(RemovePictureTagging::getSendTime, null)
                .in(RemovePictureTagging::getInspectionPictureTaggingId, Func.toStrList(dto.getInspectionPictureTaggingIds())));

        try {
            // 撤销RemoveTask状态更新
            List<String> taskIds = removePictureTaggingService.list(new LambdaUpdateWrapper<RemovePictureTagging>()
                            .in(RemovePictureTagging::getInspectionPictureTaggingId, Func.toStrList(dto.getInspectionPictureTaggingIds())))
                    .stream().map(RemovePictureTagging::getRemoveTaskId).distinct().collect(Collectors.toList());
            if (CollectionUtil.isNotEmpty(taskIds)) {
                update(new LambdaUpdateWrapper<RemoveTask>()
                        .set(RemoveTask::getRemoveTaskStatus, BizDictEnum.REMOVE_STATUS_TO_SEND.getCode())
                        .in(RemoveTask::getId, taskIds));
            }

            // 撤销InspectionPictureTagging状态更新
            inspectionPictureTaggingService.update(new LambdaUpdateWrapper<InspectionPictureTagging>()
                    .set(InspectionPictureTagging::getEliminateStatus, BizDictEnum.TAGGING_TYPE_NOT_PUSH.getCode())
                    .in(InspectionPictureTagging::getId, Func.toStrList(dto.getInspectionPictureTaggingIds())));
        } catch (Exception e) {
            throw new ServiceException("撤销消缺任务状态异常");
        }

        return flag;
    }

    @Override
    public List<TreeNode> defectUnifiedTree() {


        String deptCode = AuthUtil.getDeptCode();

        // 查询所有设备类型的缺陷数据
        List<String> lineDeptCodes = inspectionPictureTaggingMapper.getDeptDefectList(TMS_LINE, deptCode);
        List<String> fanDeptCodes = inspectionPictureTaggingMapper.getDeptDefectList(FAN, deptCode);
        List<String> pvDeptCodes = inspectionPictureTaggingMapper.getDeptDefectList(PV, deptCode);

        // 合并所有有缺陷的部门
        Set<String> allDeptCodes = new HashSet<>();
        allDeptCodes.addAll(lineDeptCodes);
        allDeptCodes.addAll(fanDeptCodes);
        allDeptCodes.addAll(pvDeptCodes);

        // 获取部门信息
        List<Dept> allDept = new ArrayList<>();
        allDeptCodes.forEach(e -> {
            Dept dept = SysCache.getDeptByDeptCode(e);
            if (Func.isNotEmpty(dept) && StringUtil.isNotBlank(dept.getId())) {
                allDept.add(dept);
            }
        });

        // 查询各设备类型的缺陷数据
        List<LineDefectVO> lineDefectList = inspectionPictureTaggingMapper.getLineDefectList(deptCode);
        List<TowerDefectVO> towerDefectList = inspectionPictureTaggingMapper.getTowerDefectList(deptCode);
        List<FanDefectVO> fanDefectList = inspectionPictureTaggingMapper.getFanDefectList(deptCode);
        List<PvDefectVO> pvDefectList = inspectionPictureTaggingMapper.getPvDefectList(deptCode);

        // 构建统一树结构
        List<TreeNode> resultList = new ArrayList<>();

        // 1. 构建部门层级
        List<TreeNode> deptList = buildUnifiedDeptList(allDept);
        resultList.addAll(deptList);

        // 2. 为每个部门构建设备类型层级
        for (TreeNode deptNode : deptList) {
            List<TreeNode> deviceTypeNodes = new ArrayList<>();

            // 2.1 构建TMS_LINE设备类型节点
            if (lineDeptCodes.contains(deptNode.getId())) {
                TreeNode lineTypeNode = buildDeviceTypeNode("输电线路", TMS_LINE, deptNode.getId());
                deviceTypeNodes.add(lineTypeNode);

                // 构建线路->杆塔层级
                buildLineDeviceTree(lineTypeNode, deptNode.getId(), lineDefectList, towerDefectList);
            }

            // 2.2 构建FAN设备类型节点
            if (fanDeptCodes.contains(deptNode.getId())) {
                TreeNode fanTypeNode = buildDeviceTypeNode("风机", FAN, deptNode.getId());
                deviceTypeNodes.add(fanTypeNode);

                // 构建风机设备
                buildFanDeviceTree(fanTypeNode, deptNode.getId(), fanDefectList);
            }

            // 2.3 构建PV设备类型节点
            if (pvDeptCodes.contains(deptNode.getId())) {
                TreeNode pvTypeNode = buildDeviceTypeNode("光伏", PV, deptNode.getId());
                deviceTypeNodes.add(pvTypeNode);

                // 构建光伏设备
                buildPvDeviceTree(pvTypeNode, deptNode.getId(), pvDefectList);
            }

            if (CollectionUtil.isNotEmpty(deviceTypeNodes)) {
                deptNode.setChildren(deviceTypeNodes);
                deptNode.setHasChildren(true);
            }
        }

        return ForestNodeMerger.merge(resultList);
    }

    private TreeNode buildDeviceTypeNode(String typeName, String deviceType, String parentDeptCode) {
        TreeNode typeNode = new TreeNode();
        typeNode.setTitle(typeName);
        typeNode.setId(parentDeptCode + "_" + deviceType); // 组合ID避免冲突
        typeNode.setParentId(parentDeptCode);
        typeNode.setHasChildren(false);
        typeNode.setType(deviceType);
        return typeNode;
    }


    private void buildLineDeviceTree(TreeNode lineTypeNode, String deptCode,
                                     List<LineDefectVO> lineDefectList, List<TowerDefectVO> towerDefectList) {
        // 过滤出该部门的线路数据
        List<LineDefectVO> deptLineList = lineDefectList.stream()
                .filter(e -> e.getDeptCode().equals(deptCode))
                .collect(Collectors.toList());

        List<TreeNode> lineNodes = new ArrayList<>();
        for (LineDefectVO lineDefectVO : deptLineList) {
            TreeNode lineNode = new TreeNode();
            lineNode.setTitle(lineDefectVO.getLineName());
            lineNode.setId(lineDefectVO.getId());
            lineNode.setParentId(lineTypeNode.getId());
            lineNode.setHasChildren(false);
            lineNode.setType(TMS_LINE);

            // 构建该线路下的杆塔
            List<TowerDefectVO> lineTowerList = towerDefectList.stream()
                    .filter(e -> e.getLineId().equals(lineDefectVO.getId()))
                    .collect(Collectors.toList());

            if (CollectionUtil.isNotEmpty(lineTowerList)) {
                List<TreeNode> towerNodes = new ArrayList<>();
                for (TowerDefectVO towerDefectVO : lineTowerList) {
                    TreeNode towerNode = new TreeNode();
                    towerNode.setTitle(towerDefectVO.getTowerName());
                    towerNode.setId(towerDefectVO.getId());
                    towerNode.setParentId(lineNode.getId());
                    towerNode.setHasChildren(false);
                    towerNode.setType(TMS_TOWER);
                    towerNodes.add(towerNode);
                }
                // 按杆塔名称排序
                towerNodes.sort(Comparator.comparing(TreeNode::getTitle));
                lineNode.setChildren(towerNodes);
                lineNode.setHasChildren(true);
            }

            lineNodes.add(lineNode);
        }

        if (CollectionUtil.isNotEmpty(lineNodes)) {
            // 按线路名称排序
            lineNodes.sort(Comparator.comparing(TreeNode::getTitle));
            lineTypeNode.setChildren(lineNodes);
            lineTypeNode.setHasChildren(true);
        }
    }

    private void buildFanDeviceTree(TreeNode fanTypeNode, String deptCode, List<FanDefectVO> fanDefectList) {
        // 过滤出该部门的风机数据
        List<FanDefectVO> deptFanList = fanDefectList.stream()
                .filter(e -> e.getDeptCode().equals(deptCode))
                .collect(Collectors.toList());

        List<TreeNode> fanNodes = new ArrayList<>();
        for (FanDefectVO fanDefectVO : deptFanList) {
            TreeNode fanNode = new TreeNode();
            fanNode.setTitle(fanDefectVO.getDeviceName());
            fanNode.setId(fanDefectVO.getId());
            fanNode.setParentId(fanTypeNode.getId());
            fanNode.setHasChildren(false);
            fanNode.setType(FAN);
            fanNodes.add(fanNode);
        }

        if (CollectionUtil.isNotEmpty(fanNodes)) {
            // 按风机设备名称排序
            fanNodes.sort(Comparator.comparing(TreeNode::getTitle));
            fanTypeNode.setChildren(fanNodes);
            fanTypeNode.setHasChildren(true);
        }
    }

    private void buildPvDeviceTree(TreeNode pvTypeNode, String deptCode, List<PvDefectVO> pvDefectList) {
        // 过滤出该部门的光伏数据
        List<PvDefectVO> deptPvList = pvDefectList.stream()
                .filter(e -> e.getDeptCode().equals(deptCode))
                .collect(Collectors.toList());

        List<TreeNode> pvNodes = new ArrayList<>();
        for (PvDefectVO pvDefectVO : deptPvList) {
            TreeNode pvNode = new TreeNode();
            pvNode.setTitle(pvDefectVO.getDeviceName());
            pvNode.setId(pvDefectVO.getId());
            pvNode.setParentId(pvTypeNode.getId());
            pvNode.setHasChildren(false);
            pvNode.setType(PV);
            pvNodes.add(pvNode);
        }

        if (CollectionUtil.isNotEmpty(pvNodes)) {
            // 按光伏设备名称排序
            pvNodes.sort(Comparator.comparing(TreeNode::getTitle));
            pvTypeNode.setChildren(pvNodes);
            pvTypeNode.setHasChildren(true);
        }
    }

    private List<TreeNode> buildUnifiedDeptList(List<Dept> allDept) {
        List<TreeNode> deptList = new ArrayList<>();
        for (Dept dept : allDept) {
            TreeNode vo = new TreeNode();
            vo.setTitle(dept.getDeptName());
            vo.setId(dept.getDeptCode());
            vo.setParentId(StringPool.ZERO);
            vo.setHasChildren(false);
            vo.setType("0"); // 部门类型
            deptList.add(vo);
        }
        // 按部门名称排序
        deptList.sort(Comparator.comparing(TreeNode::getTitle));
        return deptList;
    }

}
