package com.allcore.main.code.flywatch.service.impl;
import java.io.*;

import cn.afterturn.easypoi.excel.annotation.Excel;
import org.springframework.http.MediaType;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.system.cache.SysCache;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.constant.MessageConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.utils.AllCoreAuthUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.node.ForestNodeMerger;
import com.allcore.core.tool.utils.*;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.main.code.flywatch.dto.*;
import com.allcore.main.code.flywatch.entity.*;
import com.allcore.main.code.flywatch.mapper.*;
import com.allcore.main.code.flywatch.service.IAirspaceFileService;
import com.allcore.main.code.flywatch.service.IAirspaceService;
import com.allcore.main.code.flywatch.vo.*;
import com.allcore.main.utils.LatLng;
import com.allcore.main.utils.PolylineBufferUtil;
import com.allcore.main.utils.WordDataUtil;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.data.PictureType;
import com.deepoove.poi.data.Pictures;
import com.deepoove.poi.util.PoitlIOUtils;
import com.google.common.collect.Maps;
import com.opencsv.CSVReader;
import lombok.AllArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.map.HashedMap;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.core.io.ClassPathResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 *  服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18
 */
@Service
@AllArgsConstructor
public class AirspaceServiceImpl extends ZxhcServiceImpl<AirspaceMapper, Airspace> implements IAirspaceService {

	private final AirspaceUserMapper airspaceUserMapper;
	private final AirspaceUavPlaneMapper airspaceUavPlaneMapper;
	private final AirspaceDeviceTypeMapper airspaceDeviceTypeMapper;
	private final AirspaceDeviceDetailMapper airspaceDeviceDetailMapper;
	private final AirspaceFileMapper airspaceFileMapper;
	private final IAirspaceFileService airspaceFileService;

	private final IUserClient userClient;
	private final IOssClient ossClient;

	@Override
	public IPage<AirspaceVO> selectAirspacePage(IPage<AirspaceVO> page, AirspaceDTO airspace) {
		List<AirspaceVO> list = baseMapper.selectAirspacePage(page, airspace);
		return page.setRecords(list);
	}

	@Override
	public Boolean saveOrUpdateAirspace(AirspaceSaveOrUpdateDTO dto) {
		if (StringUtil.isNotBlank(dto.getId())) {
			airspaceUserMapper.delete(new LambdaQueryWrapper<AirspaceUser>().eq(AirspaceUser::getAirspaceId, dto.getId()));
			airspaceUavPlaneMapper.delete(new LambdaQueryWrapper<AirspaceUavPlane>().eq(AirspaceUavPlane::getAirspaceId, dto.getId()));
			airspaceDeviceTypeMapper.delete(new LambdaQueryWrapper<AirspaceDeviceType>().eq(AirspaceDeviceType::getAirspaceId, dto.getId()));
			airspaceDeviceDetailMapper.delete(new LambdaQueryWrapper<AirspaceDeviceDetail>().eq(AirspaceDeviceDetail::getAirspaceId, dto.getId()));
		}
		Airspace airspace = BeanUtil.copy(dto, Airspace.class);
		List<AirspaceUserDTO> users = dto.getUsers();
		List<AirspaceUavPlaneDTO> uavPlanes = dto.getUavPlanes();
		List<AirspaceDeviceTypeDTO> deviceTypes = dto.getDeviceTypes();
		//保存空域申请主体信息
		saveOrUpdate(airspace);

		String finalAirspaceId = airspace.getId();
		//保存飞行人员信息
		CollectionUtils.emptyIfNull(users).forEach(o -> {
			AirspaceUser airspaceUser = new AirspaceUser();
			airspaceUser.setAirspaceId(finalAirspaceId);
			airspaceUser.setUserId(o.getUserId());
			airspaceUserMapper.insert(airspaceUser);
		});
		//保存无人机信息
		CollectionUtils.emptyIfNull(uavPlanes).forEach(o -> {
			AirspaceUavPlane airspaceUavPlane = new AirspaceUavPlane();
			airspaceUavPlane.setAirspaceId(finalAirspaceId);
			airspaceUavPlane.setPlaneId(o.getPlaneId());
			airspaceUavPlaneMapper.insert(airspaceUavPlane);
		});

		//递归保存详情、设备、截图等信息
		CollectionUtils.emptyIfNull(deviceTypes).forEach(o -> {
			List<AirspaceDeviceDetailDTO> devices = o.getDevices();

			if(CollectionUtils.isNotEmpty(devices)){
				//保存类型表
				AirspaceDeviceType airspaceDeviceType = new AirspaceDeviceType();
				airspaceDeviceType.setAirspaceId(finalAirspaceId);
				airspaceDeviceType.setDeviceType(o.getDeviceType());
				airspaceDeviceTypeMapper.insert(airspaceDeviceType);
				buildDeviceList(finalAirspaceId, airspaceDeviceType.getId(), devices);
			}
		});

		return true;
	}


	private void buildDeviceList(String finalAirspaceId,String deviceTypeId, List<AirspaceDeviceDetailDTO> devices) {
		for (AirspaceDeviceDetailDTO d : devices) {
			AirspaceDeviceDetail airspaceDeviceDetail = new AirspaceDeviceDetail();
			airspaceDeviceDetail.setAirspaceDeviceTypeId(deviceTypeId);
			airspaceDeviceDetail.setAirspaceId(finalAirspaceId);
			airspaceDeviceDetail.setDeviceId(d.getDeviceId());
			airspaceDeviceDetail.setParentDeviceId(d.getParentDeviceId());
			airspaceDeviceDetail.setDeviceLevel(d.getDeviceLevel());
			airspaceDeviceDetail.setAncestors(d.getAncestors());
			airspaceDeviceDetail.setFileGuid(d.getFileGuid());
			airspaceDeviceDetail.setFlyStartDate(d.getFlyDate().split(StringPool.TILDA)[0]);
			airspaceDeviceDetail.setFlyEndDate(d.getFlyDate().split(StringPool.TILDA)[1]);
			airspaceDeviceDetailMapper.insert(airspaceDeviceDetail);
			if(CollectionUtils.isNotEmpty(d.getChildren())){
				buildDeviceList(finalAirspaceId, deviceTypeId,d.getChildren());
			}
		}
	}


	@Override
	public AirspaceDetailVO detail(String airspaceId) {
		AirspaceDetailVO rst = new AirspaceDetailVO();
		Airspace airspace = getById(airspaceId);

		rst.setId(airspaceId);
		rst.setFlyRealHigh(airspace.getFlyRealHigh());
		rst.setAirspaceType(airspace.getAirspaceType());
		rst.setDeptCode(airspace.getDeptCode());

		List<AirspaceUser> airspaceUsers = airspaceUserMapper.selectList(
				new LambdaQueryWrapper<AirspaceUser>()
						.eq(AirspaceUser::getAirspaceId, airspaceId));
		if(CollectionUtils.isNotEmpty(airspaceUsers)){
			//根据userIds查询人员信息
			List<String> userIds = airspaceUsers.stream().map(AirspaceUser::getUserId).collect(Collectors.toList());
			R<List<User>> rstR = userClient.listByIds(userIds);
			if(rstR.isSuccess()){
				List<AirspaceUserVO> users = new ArrayList<>();
				airspaceUsers.forEach(e->{
					rstR.getData().forEach(r->{
						if(e.getUserId().equals(r.getId())){
							AirspaceUserVO userVO = new AirspaceUserVO();
							userVO.setUserId(r.getId());
							userVO.setUserName(r.getRealName());
							users.add(userVO);
						}
					});
				});
				rst.setUsers(users);
			}
		}
		List<AirspaceUavPlaneVO> uavPlanes = airspaceUavPlaneMapper.selectUavModels(airspaceId);
		uavPlanes.forEach(e->{
			e.setUavTypeZh(DictBizCache.getValue(MainBizEnum.UAV_TYPE.getCode(), e.getUavType()));
		});
		rst.setUavPlanes(uavPlanes);

		//按type分类去查询
		//塔线
		List<AirspaceDeviceDetailVO> towerLineDevices = airspaceDeviceDetailMapper.selectAirspaceTowerLineDevices(airspaceId, BizDictEnum.DEVICE_TYPE_TMS_LINE.getCode());
		//风机
		List<AirspaceDeviceDetailVO> fanDevices = airspaceDeviceDetailMapper.selectAirspaceFanDevices(airspaceId, BizDictEnum.DEVICE_TYPE_FAN.getCode());
		//光伏
		List<AirspaceDeviceDetailVO> pvDevices = airspaceDeviceDetailMapper.selectAirspacePvDevices(airspaceId, BizDictEnum.DEVICE_TYPE_PV.getCode());


		/*按类型的设备集合*/
		List<AirspaceDeviceTypeVO> deviceTypeVOList = new ArrayList<>();
		AirspaceDeviceTypeVO deviceTypeVO = new AirspaceDeviceTypeVO();
		deviceTypeVO.setDeviceType(BizDictEnum.DEVICE_TYPE_FAN.getCode());
		deviceTypeVO.setDevices(fanDevices);
		deviceTypeVOList.add(deviceTypeVO);

		deviceTypeVO = new AirspaceDeviceTypeVO();
		deviceTypeVO.setDeviceType(BizDictEnum.DEVICE_TYPE_PV.getCode());
		deviceTypeVO.setDevices(pvDevices);
		deviceTypeVOList.add(deviceTypeVO);

		deviceTypeVO = new AirspaceDeviceTypeVO();
		deviceTypeVO.setDeviceType(BizDictEnum.DEVICE_TYPE_TMS_LINE.getCode());
		deviceTypeVO.setDevices(ForestNodeMerger.merge(towerLineDevices));
		deviceTypeVOList.add(deviceTypeVO);

		rst.setDeviceTypes(deviceTypeVOList);

		return rst;

	}


	@Override
	public List<AirspaceFileVO> fileList(String airspaceId, String fileType) {
		List<AirspaceFileVO> rst = new ArrayList<>();

		List<AirspaceFile> list = airspaceFileMapper.selectList(new LambdaQueryWrapper<AirspaceFile>()
				.eq(AirspaceFile::getAirspaceId, airspaceId));
		if(CollectionUtils.isNotEmpty(list)){
			List<String> fileGuids = list.stream().map(AirspaceFile::getFileGuid).collect(Collectors.toList());
			R<List<AllcoreFileVO>> rstR = ossClient.getFilesDetail(fileGuids);
			if(rstR.isSuccess()){
				list.forEach(e->{
					rstR.getData().forEach(r->{
						if(e.getFileGuid().equals(r.getFileGuid())){
							AirspaceFileVO vo = BeanUtil.copy(e,AirspaceFileVO.class);
							vo.setOriginalName(r.getOriginalName());
							vo.setStaticPath(r.getStaticPath());
							rst.add(vo);
						}
					});
				});
			}
		}
		return rst;
	}

	@Override
	public boolean fileSave(AirspaceFileDTO dto) {
		airspaceFileMapper.delete(new LambdaQueryWrapper<AirspaceFile>()
				.eq(AirspaceFile::getAirspaceId, dto.getAirspaceId())
				.eq(AirspaceFile::getFileType, dto.getFileType()));
		if(CollectionUtil.isNotEmpty(dto.getFileGuids())){
			List<AirspaceFile> list = new ArrayList<>();
			dto.getFileGuids().forEach(e->{
				AirspaceFile airspaceFile = new AirspaceFile();
				airspaceFile.setAirspaceId(dto.getAirspaceId());
				airspaceFile.setFileType(dto.getFileType());
				airspaceFile.setFileGuid(e);
				list.add(airspaceFile);
			});
			airspaceFileService.saveBatch(list);
			//修改批文状态
			Airspace airspace = new Airspace();
			airspace.setId(dto.getAirspaceId());
			airspace.setFileStatus(StringPool.ONE);
			return updateById(airspace);
		}else{
			//修改批文状态
			Airspace airspace = new Airspace();
			airspace.setId(dto.getAirspaceId());
			airspace.setFileStatus(StringPool.ZERO);
			return updateById(airspace);
		}
	}

	@Override
	public List<AirspaceShowVO> show(String airspaceId) {
		List<AirspaceShowVO> rst = new ArrayList<>();
		//风机
		List<AirspaceDeviceVO> fanDevices = airspaceDeviceDetailMapper.selectFanDevices(airspaceId, BizDictEnum.DEVICE_TYPE_FAN.getCode());
		AirspaceShowVO vo = new AirspaceShowVO();
		vo.setDeviceType(BizDictEnum.DEVICE_TYPE_FAN.getCode());
		vo.setLngLat(PolylineBufferUtil.getLineBufferEdgeCoords(toLatLng(fanDevices), CommonConstant.DOUBLE_RADIUS));
		if(StringUtil.isNotBlank(vo.getLngLat())){
			rst.add(vo);
		}

		List<AirspaceDeviceVO> pvDevices = airspaceDeviceDetailMapper.selectPvDevices(airspaceId, BizDictEnum.DEVICE_TYPE_PV.getCode());
		vo = new AirspaceShowVO();
		vo.setDeviceType(BizDictEnum.DEVICE_TYPE_PV.getCode());
		vo.setLngLat(PolylineBufferUtil.getLineBufferEdgeCoords(toLatLng(pvDevices), CommonConstant.DOUBLE_RADIUS));
		if(StringUtil.isNotBlank(vo.getLngLat())){
			rst.add(vo);
		}


		List<AirspaceDeviceVO> towerLineDevices = airspaceDeviceDetailMapper.selectTowerLineDevices(airspaceId, BizDictEnum.DEVICE_TYPE_TMS_LINE.getCode());
		//按线路分组
		Map<String,List<AirspaceDeviceVO>> map = towerLineDevices.stream().collect(Collectors.groupingBy(AirspaceDeviceVO::getParentDeviceId));
		for (Map.Entry<String, List<AirspaceDeviceVO>> entry : map.entrySet()) {
			vo = new AirspaceShowVO();
			vo.setDeviceType(BizDictEnum.DEVICE_TYPE_TMS_LINE.getCode());
			vo.setLngLat(PolylineBufferUtil.getLineBufferEdgeCoords(toLatLng(entry.getValue()), CommonConstant.DOUBLE_RADIUS));
			if(StringUtil.isNotBlank(vo.getLngLat())){
				rst.add(vo);
			}
		}
		//风机主表设备经纬度区间、光伏主表设备经纬度区间、同组杆塔经纬度区间
		return rst;
	}


	public static List<LatLng> toLatLng(List<AirspaceDeviceVO> deviceVOList) {
		List<LatLng> datas = new ArrayList<>();
		deviceVOList.forEach(e -> {
			datas.add(new LatLng(e.getLatitude(), e.getLongitude()));
		});
		return datas;
	}

	@Override
	public void exportWord(AirspaceApplyWordDTO airspaceApplyWordDTO, HttpServletResponse response) throws IOException {
		//函号
		String airNo = airspaceApplyWordDTO.getAirNo();
		//空域申请id集合
		String airspaceId = airspaceApplyWordDTO.getAirspaceId();
		if (StringUtil.isBlank(airspaceId)) {
			return;
		}
		//查询空域类型
		List<AirspaceDeviceType> airTypes = airspaceDeviceTypeMapper.selectList(
				new LambdaQueryWrapper<AirspaceDeviceType>().eq(AirspaceDeviceType::getAirspaceId, airspaceId));

		List<AirspaceDeviceDetail> airDetails = airspaceDeviceDetailMapper.selectList(
				new LambdaQueryWrapper<AirspaceDeviceDetail>().eq(AirspaceDeviceDetail::getAirspaceId, airspaceId));

		if (CollectionUtil.isEmpty(airTypes)) {
			return;
		}
		List<String> deviceTypeNameList = airTypes.stream().map(
				e -> DictBizCache.getValue(BizDictEnum.DEVICE_TYPE.getCode(),e.getDeviceType()))
				.distinct().collect(Collectors.toList());
		//airApply_1
		Map<String, Object> dataMap = Maps.newHashMap();
		dataMap.put("deviceTypeName",deviceTypeNameList.stream().collect(Collectors.joining("、")));
		//函号年份
		dataMap.put("year", WordDataUtil.getCurrentYear());
		//函号
		dataMap.put("airNo", airNo);
		//申请性质(年度或临时)
		dataMap.put("taskNature", DictBizCache.getValue(BizDictEnum.AIRSPACE_TYPE.getCode(), airspaceApplyWordDTO.getAirspaceType()));
		//系统时间
		dataMap.put("sysDate", WordDataUtil.dateToZhStr(new Date()));
		//系统时间大写
		dataMap.put("sysDateDX", WordDataUtil.dateToUpper(new Date()));
		//战区
		dataMap.put("warRegion", MessageConstant.AIRSPACE_APPLY_EXPORT_WORD_WARREGION);
		String loginDeptName = SysCache.getDept(AuthUtil.getDeptId()).getDeptName();
		String loginUserName = AuthUtil.getUserName();
		dataMap.put("fullName", loginDeptName);
		dataMap.put("deptName", loginDeptName);
		dataMap.put("userName", UserCache.getUser(AllCoreAuthUtil.getUserId()).getRealName());
		dataMap.put("personPhone", StringPool.EMPTY);
		//空域申请主表
		Airspace airspace = getById(airspaceId);
		// 多个空域申请的真高获取最高值
		Double flyHeight = airspace.getFlyRealHigh().doubleValue();

		//空域申请的真高
		dataMap.put("flyHeight", flyHeight);
		AirspaceDeviceDetail timeMap = airDetails.get(CommonConstant.INTEGER_NUM_ZERO);
		dataMap.put("planTime", WordDataUtil.dateToZhStr(DateUtil.parse(timeMap.getFlyStartDate(), DateUtil.PATTERN_DATE))
				+ "至" + WordDataUtil.dateToZhStr(DateUtil.parse(timeMap.getFlyEndDate(), DateUtil.PATTERN_DATE)));
		/*============================================无人机信息 ===========================================*/
		planeModelData(dataMap, airspaceId);
//		/*============================================线路巡检示意图 和 航线信息 ===========================================*/
		routeInfosData(airspaceId,dataMap);
//		/*============================================飞行人员信息 ====已去掉飞行人员证件信息=======================================*/
//		airFlyerData(airspaceId,dataMap);
		String time = DateUtil.formatDateTimeMini(new Date());
		String fileName = time + ".docx";

		XWPFTemplate template = XWPFTemplate.compile(new ClassPathResource("templates/airspace/airspaceApplyTemplate.docx").getInputStream()).render(dataMap);
		response.setContentType("application/force-download");
		response.setHeader("Content-disposition","attachment;filename=\"" + fileName + "\"");

		// HttpServletResponse response
		OutputStream out = response.getOutputStream();
		BufferedOutputStream bos = new BufferedOutputStream(out);
		template.write(bos);
		bos.flush();
		out.flush();
		PoitlIOUtils.closeQuietlyMulti(template, bos, out);
	}

	private void planeModelData(Map<String, Object> dataMap, String airspaceId) {
		//根据空域申请ids 查询word中所需的无人机信息
		List<ExportWordPlaneVo> uavPlanes = airspaceUavPlaneMapper.selectWordUavModels(airspaceId);
		List<Map<String, Object>> modelMapList = new ArrayList<Map<String, Object>>();
		StringBuilder sb = new StringBuilder();
		String detailUavModel = "";

		//拼接无人机类别
		String uavModel = "";

		for (int i = 0; i < uavPlanes.size(); i++) {
			String uavTypeZh = DictBizCache.getValue(BizDictEnum.UAV_TYPE.getCode(), uavPlanes.get(i).getUavType());
			String uavBrandZh = DictBizCache.getValue(MainBizEnum.DRONE_BRAND.getCode(), uavPlanes.get(i).getUavBrand());
			uavModel = uavModel + uavTypeZh;
			if (i == uavPlanes.size() - CommonConstant.INTEGER_NUM_TWO) {
				// 末2个
				uavModel += "和";
			} else if (i == uavPlanes.size() - CommonConstant.INTEGER_NUM_ONE) {
				// 末1个
			} else {
				uavModel += "、";
				// 中间的用顿号相连
			}
			//循环无人机信息列表 封装word内容所需的map
			ExportWordPlaneVo vo = uavPlanes.get(i);
			sb.append(uavBrandZh).append(uavTypeZh).append("型").append(vo.getModelName()).append(StringPool.SLASH);
			Map<String, Object> modelInfo = Maps.newHashMap();
			try {
				if (StringUtil.isNotBlank(vo.getFileGuids())) {
					String picPath = getThumbnailUrl(vo.getFileGuids().split(",")[0]);
					modelInfo.put("modelPicPath", Pictures.ofUrl(picPath, PictureType.suggestFileType(picPath))
							.size(385, 200).create());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			modelInfo.put("num", String.valueOf(i + 1));
			//类别
			modelInfo.put("modelTypeSecondName", uavTypeZh);
			//型号
			modelInfo.put("modelTypeName", vo.getModelName());
			//机身长度
			modelInfo.put("planeLength", vo.getFuselageLength());
			//机身高度
			modelInfo.put("planeHeight", vo.getFuselageHeight());
			//机身宽度
			modelInfo.put("planeWidth", vo.getFuselageWidth());
			//续航时间
			modelInfo.put("modelMaxTime", vo.getEnduranceTime());
			// 起降方式
			modelInfo.put("modelTakeoffType", vo.getTakeoffAndLandingMode());
			// 控制方式
			modelInfo.put("planeControlType", vo.getControlMode());
			//作业半径
			modelInfo.put("modelRadius", vo.getOperatingRadius());
			//速度
			modelInfo.put("modelSpeed", vo.getMaxSpeed());
			//最大抗风等级
			modelInfo.put("modelResistingWind", vo.getResistingWind());
			// 最大飞行高度
			modelInfo.put("maxFlightAltitude",vo.getMaxFlightAltitude());
			modelMapList.add(modelInfo);
		}
		// 飞机机型 相同的飞机机型去重
		dataMap.put("uavModel", uavModel);

		//机型规格
		dataMap.put("modelList", modelMapList);

		if (sb.length() > CommonConstant.INTEGER_NUM_ZERO) {
			sb.deleteCharAt(sb.length() - CommonConstant.INTEGER_NUM_ONE);
		}
		detailUavModel = sb.toString();
		// 具体飞行机型
		dataMap.put("detailUavModel", detailUavModel);
	}

	/**
	 * 获取缩略图
	 * @return
	 */
	private String getThumbnailUrl(String fileGuid) {
		R<AllcoreFileVO> fileDetailR = ossClient.getFileDetail(fileGuid);
		if (!fileDetailR.isSuccess()) {
			throw new ServiceException("获取文件信息失败");
		}
		// 原图
		String originalUrl = fileDetailR.getData().getStaticPath();
		// 缩略图
		String thumbnailUrl = fileDetailR.getData().getStaticThumbPath();
		if (StringUtil.isAllBlank(originalUrl,thumbnailUrl)) {
			throw new ServiceException("文件路径不存在");
		}
		if (StringUtil.isNotBlank(thumbnailUrl)) {
			return thumbnailUrl;
		}
		return originalUrl;
	}

	/**
	 * 设备信息
	 *@param dataMap
	 */
	private void routeInfosData(String airspaceId,Map<String, Object> dataMap) {
		List<Map<String, Object>> pointList = new ArrayList();
		List<Map<String, Object>> lineList = new ArrayList();
		//根据空域申请id查询设备id和fileGuid ()
		List<QueryDeviceAndFileVO> queryDeviceAndFileVos = baseMapper.queryDeviceAndFile(airspaceId);

		HashedMap lineInfo = null;
		HashedMap pointObj = null;
		for (int i = 0; i < queryDeviceAndFileVos.size(); i++) {
			QueryDeviceAndFileVO airspaceLineVo = queryDeviceAndFileVos.get(i);
			lineInfo = new HashedMap<String, Object>();
			lineInfo.put("num", String.valueOf(i + CommonConstant.INTEGER_NUM_ONE));
			lineInfo.put("lineName", airspaceLineVo.getDeviceName());
			try {
				if (StringUtil.isNotBlank(airspaceLineVo.getFileGuid())) {
					String picPath = getThumbnailUrl(airspaceLineVo.getFileGuid());
					lineInfo.put("image", Pictures.ofUrl(picPath, PictureType.suggestFileType(picPath))
							.size(CommonConstant.INTEGER_NUM_THREE_HUNDRED_EIGHTY_FIVE, CommonConstant.INTEGER_NUM_THREE_HUNDRED).create());
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
			lineList.add(lineInfo);
			pointObj = new HashedMap<String, Object>();
			pointObj.put("num", String.valueOf(i + CommonConstant.INTEGER_NUM_ONE));
			// 判断自己是父级也是子集
			// 根据线路和杆塔号查询拐点并获取经纬度
			StringBuilder pointInfo = new StringBuilder();
			List<AirspaceDevicePointVO> devicePointVOList = new ArrayList<>();

			if (airspaceLineVo.getHasChildren()) {
				//目前只有线路下面有杆塔 风机、光伏都是父级
				devicePointVOList = baseMapper.getTowerPointInfoByParentId(
						airspaceLineVo.getDeviceId(),
						StringPool.ZERO + StringPool.COMMA +airspaceLineVo.getDeviceId());

			} else {
				//查询无子设备的父设备的经纬度
				switch (airspaceLineVo.getDeviceType()){
					case "FAN":
						devicePointVOList = baseMapper.getFanPointInfo(airspaceLineVo.getDeviceId());
						break;
					case "PV":
						devicePointVOList = baseMapper.getPvPointInfo(airspaceLineVo.getDeviceId());
						break;
					default:
						break;
				}

			}
			for (AirspaceDevicePointVO devicePointVO : devicePointVOList) {
				double longitude = devicePointVO.getLongitude();
				double latitude = devicePointVO.getLatitude();
				String longitudeStr = CommonConstant.STRING_E + WordDataUtil.ddToDms(longitude);
				String latitudeStr = CommonConstant.STRING_N + WordDataUtil.ddToDms(latitude);
				pointInfo.append(latitudeStr).append(StringPool.SPACE).append(longitudeStr).append(StringPool.SPACE)
						.append(devicePointVO.getDeviceName()).append(StringPool.NEWLINE);
			}

			//119.87841878238677,32.32401312950261;119.87842951122283,32.32367314366051
			//要的结果 ps:
			//N xx°xx′xx" E xx°xx′xx"xxxx
			//N xx°xx′xx" E xx°xx′xx"xxxx
			//分解从设备服务拿到的经纬度集合

			if (pointInfo.length() > 0) {
				pointInfo.setLength(pointInfo.length() - 1);
			}
			pointObj.put("pointInfo", pointInfo.toString());
			pointList.add(pointObj);
		}
		dataMap.put("lineList",lineList);
		dataMap.put("pointList",pointList);
	}

	public void downloadExcel(MultipartFile file,  HttpServletResponse response, Set<String> siteNameSet, List<Map<String, List<ExcelLineData>>> siteNameList) {
		String[] headers = {"fl_name", "longitude", "latitude"};

		response.setContentType("application/octet-stream");
		response.setHeader("Content-Disposition", "attachment; filename=download.zip");
		response.setCharacterEncoding("UTF-8");

		try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream());
			InputStream ins = file.getInputStream()) {

			List<String> fileNames = getFileNameList(ins);
			for (int i = 0; i < fileNames.size(); i++) {
				String fileName = fileNames.get(i);
				List<ExcelLineData> dataList = getLineDataList(fileName,siteNameSet,siteNameList);

				//为每个文件创建新的ZipEntry
				ZipEntry zipEntry = new ZipEntry(fileName + ".csv");
				zos.putNextEntry(zipEntry);

				//使用BufferWriter将数据写入当前ZipEntry
				BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(zos));
				writer.write(String.join(",", headers));
				writer.newLine();

				for(ExcelLineData data : dataList){
					String line = data.getFlName() + "," + data.getLongitude() + "," + data.getLatitude();
					writer.write(line);
					writer.newLine();
				}
				writer.flush();
			}
		}catch(Exception e){
			e.printStackTrace();
		}
	}

	@Override
	public void getLineData(MultipartFile file, MultipartFile compareFile,HttpServletResponse response) {
		List<ExcelLineData> list = new ArrayList<>();

		try (InputStream fileIns = file.getInputStream()) {
			//Excel(oldnew需要提供杆线路塔杆区段)线路名称
			List<String> fileNameList = getFileNameList(fileIns);

			//Excel(twc_fl_gt_info_20250620111147)中根据siteName分组杆塔内容,20w数据
			List<Map<String,List<ExcelLineData>>> siteNameList = getCsvSiteNameMap(compareFile);
			Set<String> siteNameSet = getSiteNameSet(siteNameList);
			//根据拼接线路名查找线路下杆塔数据
			for(String fileName : fileNameList){
				List<ExcelLineData> resultData = getLineDataList(fileName,siteNameSet,siteNameList);
				list.addAll(resultData);
//				if(fileName.contains("Ⅰ") ){
//					int index = fileName.indexOf("Ⅰ");
//					String searchSiteName = fileName.substring(0,index);
//					List<ExcelLineData> resultData = getLineDataListBySiteName(searchSiteName, siteNameSet, siteNameList);
//					list.addAll(resultData);
//				}else if(fileName.contains("Ⅱ")){
//					int index = fileName.indexOf("Ⅱ");
//					String searchSiteName = fileName.substring(0,index);
//					List<ExcelLineData> resultData = getLineDataListBySiteName(searchSiteName,siteNameSet,siteNameList);
//					list.addAll(resultData);
//				}else{
//					//获取SiteName对应杆塔数据
//					List<ExcelLineData> resultData = getLineDataListBySiteName(fileName,siteNameSet,siteNameList);
//					list.addAll(resultData);
//				}
			}
			//生成对应CSV文件填充数据并压缩
			downloadExcel(file,response,siteNameSet,siteNameList);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	private List<ExcelLineData> getLineDataList(String fileName, Set<String> siteNameSet, List<Map<String, List<ExcelLineData>>> siteNameList) {
		List<ExcelLineData> list = new ArrayList<>();
		if(fileName.contains("Ⅰ") ){
			int index = fileName.indexOf("Ⅰ");
			String searchSiteName = fileName.substring(0,index);
			List<ExcelLineData> resultData = getLineDataListBySiteName(searchSiteName, siteNameSet, siteNameList);
			list.addAll(resultData);
		}else if(fileName.contains("Ⅱ")){
			int index = fileName.indexOf("Ⅱ");
			String searchSiteName = fileName.substring(0,index);
			List<ExcelLineData> resultData = getLineDataListBySiteName(searchSiteName,siteNameSet,siteNameList);
			list.addAll(resultData);
		}else{
			//获取SiteName对应杆塔数据
			List<ExcelLineData> resultData = getLineDataListBySiteName(fileName,siteNameSet,siteNameList);
			list.addAll(resultData);
		}
		return list;
	}

	private List<ExcelLineData> getLineDataListBySiteName(String searchSiteName, Set<String> siteNameSet, List<Map<String, List<ExcelLineData>>> siteNameList) {
		List<ExcelLineData> result = new ArrayList<>();
		if(siteNameSet.stream().anyMatch(siteName -> siteName.contains(searchSiteName))) {
			String singleSiteName = siteNameSet.stream().filter(name -> name.contains(searchSiteName)).findFirst().get();
			siteNameList.stream().forEach(siteNameMap -> {
				List<ExcelLineData> excelLineDataList = siteNameMap.get(singleSiteName);
				if(excelLineDataList != null){
					result.addAll(excelLineDataList);
				}
			});
		}else{
			siteNameSet.stream()
					.filter(siteName -> siteName.contains(searchSiteName) && (siteName.contains("Ⅰ") || siteName.contains("Ⅱ")))
					.forEach(siteName -> {
						String searchName = searchSiteName + (siteName.contains("Ⅰ") ? "Ⅰ" : (siteName.contains("Ⅱ") ? "Ⅱ" : ""));
						if (siteNameSet.stream().anyMatch(site -> site.contains(searchName))) {
							String doubleSiteName = siteNameSet.stream().filter(name -> name.contains(searchName)).findFirst().get();
							siteNameList.stream().forEach(siteNameMap ->{
								List<ExcelLineData> excelLineDataList = siteNameMap.get(doubleSiteName);
								if(excelLineDataList != null){
									result.addAll(excelLineDataList);
								}
							});
						}
					});
		}
		return result;
	}


	private Set<String> getSiteNameSet(List<Map<String, List<ExcelLineData>>> siteNameList) {
		 return siteNameList.stream().map(Map::keySet).flatMap(Collection::stream).collect(Collectors.toSet());
	}

	@Override
	public Integer compareSiteName(MultipartFile file, MultipartFile compareFile) {
		try (InputStream fileIns = file.getInputStream()) {
			// 通过流式处理按需加载文件名列表
			List<String> fileNameList = getFileNameList(fileIns);
			List<Map<String, List<ExcelLineData>>> siteNameList = getCsvSiteNameMap(compareFile);

			// 使用 HashSet 加速查找过程
			Set<String> allFileNames = new HashSet<>(fileNameList);

			int count = 0;

			// 遍历 siteNameList，按需处理数据
			for (Map<String, List<ExcelLineData>> siteNameMap : siteNameList) {
				Set<String> keys = siteNameMap.keySet();
				// 在每一批次中按需检查并计算符合条件的数量
				for (String fileName : allFileNames) {
					if (keys.contains(fileName)) {
						count++;
					}
				}
			}
			return count;
		} catch (Exception e) {
			e.printStackTrace();
			return 0;
		}
	}

	private List<Map<String,List<ExcelLineData>>> getCsvSiteNameMap(MultipartFile file) {
		List<ExcelLineData> lineDataList = new ArrayList<>();
		List<Map<String,List<ExcelLineData>>> list = new ArrayList<>();
		try{
			CSVReader reader = new CSVReader(new InputStreamReader(file.getInputStream(), "GBK"));
			//跳过表头
			String[] header = reader.readNext();
			List<String[]> allData = reader.readAll();
			allData.stream().forEach(data -> {
				ExcelLineData excelLineData = new ExcelLineData();
				List<String> dataList = new ArrayList<>(Arrays.asList(data));
				System.out.println("当前数据:" + dataList.get(1));
				Optional.ofNullable(dataList.get(4))
						.filter(StringUtil::isNotBlank)
						.map(Double::parseDouble)
						.ifPresent(longitude -> excelLineData.setLongitude(longitude));
				Optional.ofNullable(dataList.get(5))
						.filter(StringUtil::isNotBlank)
						.map(Double::parseDouble)
						.ifPresent(latitude -> excelLineData.setLatitude(latitude));
				excelLineData.setSiteName(StringUtil.isNotBlank(dataList.get(2)) ? dataList.get(2) : null);
				excelLineData.setFlName(StringUtil.isNotBlank(dataList.get(3)) ? dataList.get(3) : null);
				lineDataList.add(excelLineData);
			});
			Map<String, List<ExcelLineData>> map = lineDataList.stream()
					.filter(lineData -> lineData.getSiteName() != null)
					.collect(Collectors.groupingBy(ExcelLineData::getSiteName));
			list.add(map);
		}catch(Exception e){
			e.printStackTrace();
		};
		return list;
	}

	private List<String> getFileNameList(InputStream inputStream) throws IOException {
		// 读取 Excel 文件
		Workbook workbook = new XSSFWorkbook(inputStream);
		Sheet sheet = workbook.getSheetAt(0);

		List<String> fileNameList = new ArrayList<>();
		// 遍历 Excel 中的行，收集 CSV 文件名
		for (int i = 68; i <= 300; i++) {
			Row row = sheet.getRow(i);
			if (row != null) {
				Cell cell = row.getCell(1);
				if (cell != null) {
					fileNameList.add(cell.getStringCellValue());
				}
			}
		}
		return fileNameList;
	}
}
