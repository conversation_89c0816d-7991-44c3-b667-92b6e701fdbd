package com.allcore.main.code.common.service;

import java.util.List;
import java.util.Map;

import com.allcore.core.tool.api.R;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.main.code.common.dto.DeviceCommonDTO;
import com.allcore.main.code.common.dto.DeviceFindDTO;
import com.allcore.main.code.common.vo.BasicCommonVO;
import com.allcore.main.code.common.vo.DeptTreeVO;
import com.allcore.main.code.common.vo.DeviceFindVO;
import com.allcore.main.code.common.vo.PVTreeNode;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2023/11/2
 */
public interface CommonService {
    /**
     * 部门树
     * @param tenantId
     * @param deptId
     * @return
     */
    List<DeptTreeVO> deptTree(String tenantId, String deptId);

    /**
     * 通过设备id查询设备信息集合, 设备类型(LINE,FAN,PV,BOOSTER,OTHER), 外加线路下的杆塔TOWER
     *
     * @param dto
     *            同一种设备,以及id
     * @return 设备集合
     */
    List<DeviceFindVO> findDeviceListById(DeviceFindDTO dto);

    /**
     * 查询设备
     *
     * @param dto
     *            条件
     * @return R<List < BasicCommonVO>>
     */
    R<List<BasicCommonVO>> getCommonDeviceList(DeviceCommonDTO dto);

    /**
     * 查询设备详情
     *
     * @param deviceIds
     *            设备id列表
     * @param deviceType
     *            设备类型
     * @param deptCode
     * 通过id获取设备
     * @return BasicCommonVO
     */
    List<BasicCommonVO> getCommonDeviceByIds(List<String> deviceIds, String deviceType,String deptCode);

    /**
     * 据设备ids查设备信息
     * 
     * @param deviceIds
     * @param deviceType
     * @return
     */
    Map<String, BasicCommonVO> getDeviceByDeviceIds(List<String> deviceIds, String deviceType);

    /**
     * 获取设备所在的树，逗号拼接
     * 
     * @param deviceType
     * @param deviceId
     * @return
     */
    String getDeviceGuidTree(String deviceType, Long deviceId);

    /**
     * 根据设备名称查设备信息
     * 
     * @param deviceNames
     * @param deviceType
     * @return
     */
    Map<String, BasicCommonVO> getDeviceByNames(List deviceNames, String deviceType);

    /**
     * 查询无人机或机场列表
     * 
     * @param dto
     * @return
     */
    R<List<BasicCommonVO>> getUavOrAirPlaneList(DeviceCommonDTO dto);

    /**
     * 根据设备查询航线列表
     * 
     * @param dto
     * @return
     */
    R<List<BasicCommonVO>> getRouteList(DeviceCommonDTO dto);

    /**
     * 设备树——根据单位查询箱变、逆变器、组串
     */
    R<List<PVTreeNode>> pvTree(String deviceType, String deptCode);

    /**
     * 场站级光伏设备树
     * @param deptCode
     * @return
     */
    R<List<PVTreeNode>> getPvDevice(String deptCode);
}
