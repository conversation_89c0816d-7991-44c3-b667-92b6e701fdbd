package com.allcore.portal.dto;

import com.allcore.portal.entity.Project;
import com.allcore.portal.entity.ProjectFunction;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 数据传输对象实体类
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ProjectDTO extends Project {
	private static final long serialVersionUID = 1L;

	/**
	 * 项目分组
	 */
	private List<FunctionGroupDTO> functionGroupList;

}
