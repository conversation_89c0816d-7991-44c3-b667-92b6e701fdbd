package com.allcore.portal.mapper;

import com.allcore.portal.entity.ProjectFunction;
import com.allcore.portal.vo.ProjectFunctionVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 *  Mapper 接口
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
public interface ProjectFunctionMapper extends BaseMapper<ProjectFunction> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param projectFunction
	 * @return
	 */
	List<ProjectFunctionVO> selectProjectFunctionPage(IPage page, ProjectFunctionVO projectFunction);

}
