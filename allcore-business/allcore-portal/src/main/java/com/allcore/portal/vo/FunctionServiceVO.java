package com.allcore.portal.vo;

import com.allcore.portal.entity.FunctionService;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-04-12
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FunctionServiceVO对象", description = "FunctionServiceVO对象")
public class FunctionServiceVO extends FunctionService {
	private static final long serialVersionUID = 1L;

}
