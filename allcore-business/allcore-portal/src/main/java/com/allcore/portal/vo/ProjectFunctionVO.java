package com.allcore.portal.vo;

import com.allcore.portal.entity.ProjectFunction;
import lombok.Data;
import lombok.EqualsAndHashCode;
import io.swagger.annotations.ApiModel;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2023-03-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "ProjectFunctionVO对象", description = "ProjectFunctionVO对象")
public class ProjectFunctionVO extends ProjectFunction {
	private static final long serialVersionUID = 1L;

	/**
	 * 所属服务
	 */
	private String belongService;

	/**
	 * 所属服务别名
	 */
	private String belongServiceAlias;


}
