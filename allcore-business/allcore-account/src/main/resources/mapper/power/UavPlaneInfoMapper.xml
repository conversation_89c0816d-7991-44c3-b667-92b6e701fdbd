<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.account.power.mapper.UavPlaneInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseMap" type="com.allcore.account.power.vo.UavPlaneInfoPageVO">
        <result column="id" property="id"/>
        <result column="plane_guid" property="planeGuid"/>
        <result column="model_name" property="modelName"/>
        <result column="plane_name" property="planeName"/>
        <result column="sn_code" property="snCode"/>
        <result column="registration_time" property="registrationTime"/>
        <result column="asset_attribute" property="assetAttribute"/>
        <result column="dept_code" property="operationTeam"/>
        <result column="state" property="state"/>
        <result column="uav_brand" property="uavBrand"/>
        <result column="big_uav_type" property="uavType"/>
        <result column="out_unit" property="outUnit"/>
        <result column="unit_name" property="outUnitName"/>
        <result column="fly_time" property="flyTime"/>
        <result column="fly_sorties" property="sorties"/>
        <result column="maintenance_times" property="maintenanceTimes"/>
    </resultMap>

    <resultMap id="QjMap" type="com.allcore.account.power.vo.QjUavPlaneVO">
        <result column="plane_guid" property="planeGUID"/>
        <result column="planeEquipmentNo" property="planeEquipmentNo"/>
        <result column="modelBrand" property="modelBrand"/>
        <result column="modelUAVModel" property="modelUAVModel"/>
        <result column="planeRegisterTime" property="planeRegisterTime"/>
        <result column="planeAssetAttributesName" property="planeAssetAttributesName"/>
        <result column="sysCodeName" property="sysCodeName"/>
        <result column="planeReserve5" property="planeReserve5"/>
        <result column="planeEquipmentSource" property="planeEquipmentSource"/>
        <result column="planeCustodian" property="planeCustodian"/>
        <result column="planeReserve8" property="planeReserve8"/>
        <result column="unitGUID" property="unitGUID"/>
        <result column="receivingUnitName" property="receivingUnitName"/>
        <result column="workAreaGUID" property="workAreaGUID"/>
        <result column="workAreaUnitName" property="workAreaUnitName"/>
        <result column="teamGUID" property="teamGUID"/>
        <result column="teamUnitName" property="teamUnitName"/>
        <result column="planeRemark" property="planeRemark"/>
        <result column="planeUAVMake" property="planeUAVMake"/>
        <result column="unitName" property="unitName"/>
        <result column="planeReserve1" property="planeReserve1"/>
        <result column="wrjzt" property="wrjzt"/>
        <result column="modelGUID" property="modelGUID"/>
        <result column="modelType" property="modelType"/>
        <result column="planeAssetAttributes" property="planeAssetAttributes"/>
        <result column="countWorkOrder" property="countWorkOrder"/>
        <result column="planeID" property="planeID"/>
        <result column="professionalType" property="professionalType"/>
        <result column="professionalTypeName" property="professionalTypeName"/>
        <result column="planeNumber" property="planeNumber"/>
        <result column="planeFlyMileage" property="planeFlyMileage"/>
        <result column="planeFlyNumber" property="planeFlyNumber"/>
        <result column="planeFlyTime" property="planeFlyTime"/>
        <result column="deptCode" property="deptCode"/>
        <result column="uavDeptId" property="uavDeptId"/>
    </resultMap>

    <select id="pageList" resultMap="BaseMap">
        SELECT
        upi.id,
        upi.plane_guid,
        upi.plane_name,
        upi.sn_code,
        upi.registration_time,
        upi.asset_attribute,
        upi.specialty_type,
        upi.dept_code,
        upi.state,
        umi.model_name,
        umi.model_guid,
        umi.uav_brand,
        umi.big_uav_type,
        upi.uav_dept_id,
        upi.out_unit,
        ou.unit_name,
        COALESCE (upf.fly_sorties,0) fly_sorties,
        COALESCE (upf.fly_time,0) fly_time,
        COALESCE (mt.maintenance_times,0) maintenance_times
        FROM
        account_uav_plane_info upi
        LEFT JOIN account_uav_plane_fly upf ON upi.plane_guid = upf.plane_guid
        LEFT JOIN account_uav_model_info umi ON upi.model_guid = umi.model_guid
        LEFT JOIN account_out_unit ou on upi.out_unit = ou.out_unit_guid
        LEFT JOIN sys_dept bd ON upi.dept_code = bd.dept_code
        LEFT JOIN (SELECT product_guid,count(1) maintenance_times FROM maintain_info WHERE maintain_status =
        'maintain_finished' GROUP BY product_guid ) mt ON upi.plane_guid = mt.product_guid
        <where>
            and bd.`status` = 'yes'
            and upi.is_deleted = 0
            <if test="dto.deptCode != null and dto.deptCode !=''">
                and upi.dept_code like concat(#{dto.deptCode},'%')
            </if>
            <if test="dto.uavType != null and dto.uavType !=''">
                and umi.big_uav_type = #{dto.uavType}
            </if>
            <if test="dto.uavBrand != null and dto.uavBrand !=''">
                and umi.uav_brand = #{dto.uavBrand}
            </if>
            <if test="dto.modelName != null and dto.modelName !=''">
                and umi.model_name = #{dto.modelName}
            </if>
            <if test="dto.planeName != null and dto.planeName !=''">
                and upi.plane_name like concat('%',#{dto.planeName},'%')
            </if>
            <if test="dto.snCode != null and dto.snCode !=''">
                and upi.sn_code like concat('%',#{dto.snCode},'%')
            </if>
            <if test="dto.assetAttribute != null and dto.assetAttribute !=''">
                and upi.asset_attribute = #{dto.assetAttribute}
            </if>
            <if test="dto.state != null and dto.state !=''">
                and upi.state = #{dto.state}
            </if>
            <if test="dto.isOut != null">
                and upi.is_out = #{dto.isOut}
            </if>
            <if test="dto.outUnit != null and dto.outUnit !=''">
                and upi.out_unit = #{dto.outUnit}
            </if>
        </where>
        order by upi.create_time desc
    </select>


    <select id="getUavPoliceInfo" resultType="com.allcore.account.power.vo.UavWordInfoVO">
        select
            upi.id,
            upi.plane_guid,
            upi.model_guid,
            upi.manufacturer,
            upi.plane_name,
            upi.sn_code,
            upi.real_certification_number,
            upi.uav_dept_id,
            upi.registration_time,
            upi.device_source,
            upi.asset_attribute,
            upi.custodian,
            upi.specialty_type,
            upi.dept_code,
            upi.state,
            upi.remarks,
            upi.create_dept,
            upi.create_user,
            upi.create_time,
            upi.update_user,
            upi.update_time,
            upi.is_deleted,
            upi.is_out,
            upi.out_unit,
            umi.uav_brand,
            umi.model_name,
            umi.big_uav_type,
            umi.middle_uav_type,
            umi.max_speed,
            umi.max_flight_altitude,
            umi.max_takeoff_weight,
            umi.empty_weight
        from account_uav_plane_info upi left join account_uav_model_info umi on upi.model_guid = umi.model_guid
        <where>
            and upi.is_deleted = 0
            and upi.plane_guid in
            <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>

    </select>


    <select id="findListByUavTypeAndModelGuid" resultType="com.allcore.account.power.vo.UavPlaneNameVO">
        SELECT
        upi.sn_code,
        upi.plane_guid,
        upi.plane_name,
        umi.model_guid,
        umi.model_name
        FROM
        account_uav_plane_info upi
        LEFT JOIN account_uav_model_info umi ON upi.model_guid = umi.model_guid
        <where>
            and upi.is_deleted = 0

            <if test="deptCode != null  and deptCode !=''">
                and upi.dept_code like concat(#{deptCode},'%')
            </if>
            <if test="uavType != null  and uavType !=''">
                and umi.big_uav_type = #{uavType}
            </if>
            <if test="modelGuid != null  and modelGuid !=''">
                and umi.model_guid = #{modelGuid}
            </if>
            <if test="isOut == 1 ">
                and upi.is_out = #{isOut}
            </if>
            <if test="isOut == null or isOut == 0">
                and upi.is_out = 0
            </if>
        </where>
    </select>


    <insert id="insertDetailBatch" parameterType="java.util.List">
        INSERT INTO account_uav_plane_info
        (
        id,
        plane_guid,
        model_guid,
        manufacturer,
        plane_name,
        sn_code,
        uav_dept_id,
        registration_time,
        device_source,
        asset_attribute,
        custodian,
        dept_code,
        state,
        remarks,
        create_dept,
        create_user,
        create_time,
        update_user,
        update_time,
        is_deleted,
        out_unit,
        real_certification_number,
        is_out
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.planeGuid},
            #{item.modelGuid},
            #{item.manufacturer},
            #{item.planeName},
            #{item.snCode},
            #{item.uavDeptId},
            #{item.registrationTime},
            #{item.deviceSource},
            #{item.assetAttribute},
            #{item.custodian},
            #{item.deptCode},
            #{item.state},
            #{item.remarks},
            #{item.createDept},
            #{item.createUser},
            #{item.createTime},
            #{item.updateUser},
            #{item.updateTime},
            #{item.isDeleted},
            #{item.outUnit},
            #{item.realCertificationNumber},
            #{item.isOut}
            )
        </foreach>
    </insert>

    <insert id="insertDetailBatchAndUpdate" parameterType="java.util.List">
        INSERT INTO account_uav_plane_info
        (
        id,
        plane_guid,
        model_guid,
        manufacturer,
        plane_name,
        sn_code,
        uav_dept_id,
        registration_time,
        device_source,
        asset_attribute,
        custodian,
        dept_code,
        state,
        remarks,
        create_dept,
        create_user,
        create_time,
        update_user,
        update_time,
        is_deleted,
        out_unit,
        is_out
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.id},
            #{item.planeGuid},
            #{item.modelGuid},
            #{item.manufacturer},
            #{item.planeName},
            #{item.snCode},
            #{item.uavDeptId},
            #{item.registrationTime},
            #{item.deviceSource},
            #{item.assetAttribute},
            #{item.custodian},
            #{item.deptCode},
            #{item.state},
            #{item.remarks},
            #{item.createDept},
            #{item.createUser},
            #{item.createTime},
            #{item.updateUser},
            #{item.updateTime},
            #{item.isDeleted},
            #{item.outUnit},
            #{item.isOut}
            )
        </foreach>
        ON DUPLICATE KEY UPDATE
        update_time= now()

    </insert>

    <select id="getPlaneListByGuids" resultType="com.allcore.account.power.vo.UavPlaneInfoVO">
        SELECT
        upi.id,
        upi.plane_guid,
        upi.plane_name,
        upi.sn_code,
        upi.registration_time,
        upi.asset_attribute,
        upi.specialty_type,
        upi.dept_code,
        upi.state,
        umi.model_name,
        umi.model_guid,
        umi.uav_brand,
        umi.big_uav_type uav_type,
        upi.uav_dept_id,
        upi.out_unit,
        ou.unit_name,
        upi.device_source,
        upi.manufacturer,
        upi.real_certification_number
        FROM account_uav_plane_info upi
        LEFT JOIN account_uav_model_info umi ON upi.model_guid = umi.model_guid
        LEFT JOIN account_out_unit ou ON upi.out_unit = ou.out_unit_guid
        <where>
            AND upi.is_deleted = 0
            AND upi.plane_guid IN
            <foreach collection="planeGuids" item="item" index="index" open="(" close=")" separator=",">
                #{item}
            </foreach>
        </where>
        ORDER BY upi.create_time DESC
    </select>

    <select id="getPlanePageList" resultMap="QjMap">
        SELECT
        upi.id planeID,
        upi.plane_guid,
        upi.plane_name planeReserve8,
        upi.sn_code planeEquipmentNo,
        upi.registration_time planeRegisterTime,
        upi.asset_attribute planeAssetAttributes,
        upi.specialty_type professionalType,
        upi.dept_code deptCode,
        upi.uav_dept_id uavDeptId,
        upi.state planeReserve1,
        upi.manufacturer planeUAVMake,
        upi.real_certification_number planeReserve5,
        upi.device_source planeEquipmentSource,
        upi.custodian planeCustodian,
        upi.remarks planeRemark,
        umi.model_name modelUAVModel,
        umi.model_guid modelGUID,
        umi.uav_brand modelBrand,
        umi.big_uav_type modelType,
        upf.fly_sorties planeFlyNumber,
        upf.fly_time planeFlyTime,
        upf.fly_distance planeFlyMileage,
        mt.maintenance_times planeNumber
        FROM
        account_uav_plane_info upi
        LEFT JOIN account_uav_plane_fly upf ON upi.plane_guid = upf.plane_guid
        LEFT JOIN account_uav_model_info umi ON upi.model_guid = umi.model_guid
        LEFT JOIN account_out_unit ou on upi.out_unit = ou.out_unit_guid
        LEFT JOIN (SELECT product_guid,count(1) maintenance_times FROM maintain_info WHERE maintain_status =
        'maintain_finished' GROUP BY product_guid ) mt ON upi.plane_guid = mt.product_guid
        <where>
            and upi.is_deleted = 0
            and upi.is_out = 0
            <if test="dto.deptCode != null and dto.deptCode !=''">
                and upi.dept_code like concat(#{dto.deptCode},'%')
            </if>
            <if test="dto.planeUAVType != null and dto.planeUAVType !=''">
                and umi.big_uav_type = #{dto.planeUAVType}
            </if>
            <if test="dto.planeEnterpriseBrand != null and dto.planeEnterpriseBrand !=''">
                and umi.uav_brand = #{dto.planeEnterpriseBrand}
            </if>
            <if test="dto.planeEquipmentNo != null and dto.planeEquipmentNo !=''">
                and upi.sn_code like concat('%',#{dto.planeEquipmentNo},'%')
            </if>
            <if test="dto.planeAssetAttributes != null and dto.planeAssetAttributes !=''">
                and upi.asset_attribute = #{dto.planeAssetAttributes}
            </if>
        </where>
        order by upi.create_time desc
    </select>

</mapper>
