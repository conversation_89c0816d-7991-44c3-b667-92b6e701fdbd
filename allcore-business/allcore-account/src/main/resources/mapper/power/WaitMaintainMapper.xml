<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="com.allcore.account.power.mapper.WaitMaintainMapper">


    <sql id="Base_Column_List">
	wait_maintain_guid,
	line_guid,
	line_name,
	dept_guid,
	dept_name,
	id,
	create_time,
	update_time,
	dept_code
</sql>
    <resultMap id="BaseResultMap" type="com.allcore.account.power.entity.WaitMaintainEntity">
        <result column="wait_maintain_guid" property="waitMaintainGuid"/>
        <result column="line_guid" property="lineGuid"/>
        <result column="line_name" property="lineName"/>
        <result column="dept_guid" property="deptGuid"/>
        <result column="dept_name" property="deptName"/>
        <result column="id" property="id"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="dept_code" property="deptCode"/>
    </resultMap>
    <select id="queryWaitMaintainPage" resultType="com.allcore.account.power.vo.WaitMaintainLineVO">
    SELECT
        line.voltage_level AS lineVoltageLevel,
        line.`name` AS lineName,
        wa.line_guid AS lineGuid,
        line.city AS deptName,
        line.maint_org AS workAreaName,
        line.maint_group AS maintGroup,
        wa.dept_name AS waitMaintainName,
        line.length AS lineLength,
<!--        line.space_code as spaceCode,-->
        COUNT(w.tower_guid) AS towerNumber
    FROM
        account_wait_maintain_ledger wa
    LEFT JOIN account_line_ledger line ON wa.line_guid = line.line_guid
    LEFT JOIN account_tower_ledger w ON line.line_guid = w.tower_line_guid
    <where>
        <if test="dto.lineVoltageLevel != null and dto.lineVoltageLevel != ''">
            and line.voltage_level = #{dto.lineVoltageLevel,jdbcType=VARCHAR}
        </if>
        <if test="dto.lineName != null and dto.lineName != ''">
            and line.`name` like concat('%',#{dto.lineName,jdbcType=VARCHAR},'%')
        </if>
        <if test="dto.waitMaintainGuid != null and dto.waitMaintainGuid != ''">
            and wa.dept_guid = #{dto.waitMaintainGuid}
        </if>
    </where>
    GROUP BY
        wa.dept_guid,
        wa.line_guid
    ORDER BY
        wa.update_time DESC
    </select>

    <select id="queryTowerPage" resultType="com.allcore.account.power.vo.WaitMaintainTowerVO">
    SELECT
        w.tower_guid as towerGuid,
        w.work_area_guid AS workAreaName,
        w.maint_group AS workGroupName,
        w.`name` AS towerNo,
        w.tower_longitude AS towerLongitude,
        w.tower_latitude AS towerLatitude,
        w.real_longitude AS realLongitude,
        w.real_latitude AS realLatitude,
        w.update_time AS updateTime,
        w.pm_code AS pmCode,
        w.update_user AS updateUser,
        w.pole_nature AS poleNature,
        w.is_airworthy_area AS isAirworthyArea,
        w.model,
        w.pole_high AS poleHigh,
        w.is_same_pole AS isSamePole,
        w.is_turn AS isTurn,
        w.turn_degrees AS turnDegrees,
        w.turn_direction AS turnDirection,
        w.strain_length AS strainLength,
        w.is_terminal AS isTerminal,
        w.norminal_height AS norminalHeight,
        w.head_height AS headHeight,
        w.altitude,
        w.regionalism,
        w.geology
    FROM
        account_tower_ledger w
    WHERE
        w.tower_line_guid = #{lineGuid,jdbcType=VARCHAR}
    ORDER BY
        w.tower_sort
    </select>

    <select id="exportLineList" resultType="com.allcore.account.power.vo.WaitMaintainLineVO">
        SELECT
        line.voltage_level AS lineVoltageLevel,
        line.`name` AS lineName,
        wa.line_guid AS lineGuid,
        line.city AS deptName,
        line.maint_org AS workAreaName,
        line.maint_group AS maintGroup,
        wa.dept_name AS waitMaintainName,
        line.length AS lineLength,
        <!--        line.space_code as spaceCode,-->
        COUNT(w.tower_guid) AS towerNumber
        FROM
        account_wait_maintain_ledger wa
        LEFT JOIN account_line_ledger line ON wa.line_guid = line.line_guid
        LEFT JOIN account_tower_ledger w ON line.line_guid = w.tower_line_guid
        <where>
            <if test="dto.lineVoltageLevel != null and dto.lineVoltageLevel != ''">
                and line.voltage_level = #{dto.lineVoltageLevel,jdbcType=VARCHAR}
            </if>
            <if test="dto.lineName != null and dto.lineName != ''">
                and line.`name` like concat('%',#{dto.lineName,jdbcType=VARCHAR},'%')
            </if>
            <if test="dto.waitMaintainGuid != null and dto.waitMaintainGuid != ''">
                and wa.dept_guid = #{dto.waitMaintainGuid}
            </if>
            <if test="dto.lineGuids != null and dto.lineGuids.size()>0">
                AND line.line_guid IN
                <foreach collection="dto.lineGuids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY
        wa.dept_guid,
        wa.line_guid
        ORDER BY
        wa.update_time DESC
    </select>

    <select id="exportTowerList" resultType="com.allcore.account.power.vo.WaitMaintainTowerVO">
        SELECT
        w.work_area_guid AS workAreaName,
        w.maint_group AS workGroupName,
        w.`name` AS towerNo,
        w.tower_longitude AS towerLongitude,
        w.tower_latitude AS towerLatitude,
        w.real_longitude AS realLongitude,
        w.real_latitude AS realLatitude,
        w.update_time AS updateTime,
        w.pm_code AS pmCode,
        w.update_user AS updateUser,
        w.pole_nature AS poleNature,
        w.is_airworthy_area AS isAirworthyArea,
        w.model,
        w.pole_high AS poleHigh,
        w.is_same_pole AS isSamePole,
        w.is_turn AS isTurn,
        w.turn_degrees AS turnDegrees,
        w.turn_direction AS turnDirection,
        w.strain_length AS strainLength,
        w.is_terminal AS isTerminal,
        w.norminal_height AS norminalHeight,
        w.head_height AS headHeight,
        w.altitude,
        w.regionalism,
        w.geology
        FROM
        account_tower_ledger w
        <where>
            <if test="dto.lineGuid != null and dto.lineGuid != ''">
               and w.tower_line_guid = #{dto.lineGuid,jdbcType=VARCHAR}
            </if>
            <if test="dto.towerGuids != null and dto.towerGuids.size()>0">
                AND w.tower_guid IN
                <foreach collection="dto.towerGuids" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY
        w.tower_sort
    </select>

<!--auto generated by MybatisCodeHelper on 2022-10-11-->
    <select id="selectByDeptGuidAndLineGuid" resultMap="BaseResultMap">
        SELECT
        wait_maintain_guid,
        line_guid,
        line_name,
        dept_guid,
        dept_name,
        id,
        create_time,
        update_time,
        dept_code
        FROM
        account_wait_maintain_ledger
        where dept_guid=#{deptGuid} and line_guid=#{lineGuid}
    </select>

<!--auto generated by MybatisCodeHelper on 2022-10-12-->
    <delete id="deleteByDeptGuidAndLineGuid">
        delete from account_wait_maintain_ledger
        where dept_guid=#{deptGuid} and line_guid=#{lineGuid}
    </delete>

    <select id="getAccreditList" resultType="com.allcore.account.power.vo.AccreditListVo">
    SELECT
        l.city AS cityName,
        l.maint_org AS maintOrgName,
        l.maint_group AS maintGroupName,
        l.voltage_level AS lineVoltageLevel,
        l.`name` AS lineName,
        l.line_guid AS lineGuid
    FROM
        account_wait_maintain_ledger w
        LEFT JOIN account_line_ledger l ON w.line_guid = l.line_guid
        <where>
        <if test="lineType != null and lineType != ''">
            and l.professional_type = #{lineType}
        </if>
        <if test="waitDeptGuid != null and waitDeptGuid != ''">
            and w.dept_guid = #{waitDeptGuid}
        </if>
        <if test="deptCode != null and deptCode != ''">
            AND l.dept_code LIKE CONCAT('%',#{deptCode},'%')
        </if>
        </where>
        GROUP BY l.line_guid
    </select>

    <select id="getBelongsList" resultType="com.allcore.account.power.vo.AccreditListVo">
    SELECT
        l.city AS cityName,
        l.maint_org AS maintOrgName,
        l.maint_group AS maintGroupName,
        l.voltage_level AS lineVoltageLevel,
        l.`name` AS lineName,
        l.line_guid AS lineGuid
    FROM
        account_line_ledger l
    <where>
        <if test="lineType != null and lineType != ''">
            and l.professional_type = #{lineType}
        </if>
        <if test="deptCode != null and deptCode != ''">
            and l.dept_code like concat('%',#{deptCode},'%')
        </if>
        <if test="lineName != null and lineName !=''">
            AND l.`name` like concat('%', #{lineName},'%')
        </if>
        <if test="lineVoltageLevel != null and lineVoltageLevel !=''">
            AND	l.voltage_level = #{lineVoltageLevel}
        </if>
    </where>
    GROUP BY l.line_guid
    </select>
    <select id="getDeptGuidsByLineGuid" resultType="java.lang.String">
        select dept_guid from account_wait_maintain_ledger where line_guid = #{lineGuid}
    </select>

    <!--auto generated by MybatisCodeHelper on 2022-10-13-->
    <update id="updateByDeptGuidAndLineGuid">
        update account_wait_maintain_ledger
        <set>
            <if test="updated.lineGuid != null and updated.lineGuid != ''">
                line_guid = #{updated.lineGuid},
            </if>
            <if test="updated.lineName != null and updated.lineName != ''">
                line_name = #{updated.lineName},
            </if>
            <if test="updated.deptGuid != null and updated.deptGuid != ''">
                dept_guid = #{updated.deptGuid},
            </if>
            <if test="updated.deptName != null and updated.deptName != ''">
                dept_name = #{updated.deptName},
            </if>
            <if test="updated.createTime != null">
                create_time = #{updated.createTime},
            </if>
            <if test="updated.updateTime != null">
                update_time = #{updated.updateTime},
            </if>
            <if test="updated.deptCode != null and updated.deptCode != ''">
                dept_code = #{updated.deptCode},
            </if>
        </set>
        where dept_guid=#{deptGuid} and line_guid=#{lineGuid}
    </update>
    <select id="getWaitMaintainLine" resultType="com.allcore.account.power.vo.OutWaitMaintainLineVo">
    SELECT
        line.voltage_level AS lineVoltageLevel,
        line.`name` AS lineName,
        wa.line_guid AS lineGuid,
        line.city AS deptName,
        line.maint_group AS maintGroup,
        line.length AS lineLength,
        COUNT(w.tower_guid) AS towerNumber
    FROM
        account_wait_maintain_ledger wa
    LEFT JOIN account_line_ledger line ON wa.line_guid = line.line_guid
    LEFT JOIN account_tower_ledger w ON line.line_guid = w.tower_line_guid
    WHERE
        wa.dept_guid = #{deptGuid,jdbcType=VARCHAR}
    GROUP BY
        wa.dept_guid,
        wa.line_guid
    ORDER BY
        wa.update_time DESC
    </select>

</mapper>
