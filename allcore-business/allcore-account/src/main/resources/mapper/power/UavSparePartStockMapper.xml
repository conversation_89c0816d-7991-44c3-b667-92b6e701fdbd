<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.account.power.mapper.UavSparePartStockMapper">

    <select id="inPageList" resultType="com.allcore.account.power.vo.InStockVO">
        SELECT id,stock_type,stock_count,spare_part_guid,stock_remarks,out_reason,in_time,create_time,create_user,create_dept FROM account_uav_spare_part_stock
        <where>
            and stock_type = 'in'
            and spare_part_guid = #{sparePartGuid}
        </where>
    </select>
    <select id="outPageList" resultType="com.allcore.account.power.vo.OutStockVO">
        SELECT id,stock_type,stock_count,spare_part_guid,stock_remarks,out_reason,in_time,create_time,create_user,create_dept FROM account_uav_spare_part_stock
        <where>
            and stock_type = 'out'
            and spare_part_guid = #{sparePartGuid}
        </where>
    </select>
</mapper>
