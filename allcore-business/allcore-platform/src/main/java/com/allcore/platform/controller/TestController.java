//package com.allcore.platform.controller;
//
//import com.alibaba.fastjson.JSONObject;
//import com.allcore.platform.service.IAlgorithmService;
//import com.allcore.platform.service.IIgwService;
//import com.allcore.platform.service.IIscService;
//import com.allcore.platform.service.ZtQueryLogService;
//import com.allcore.platform.dto.*;
//import com.allcore.platform.feign.ZtClient;
//import com.allcore.platform.props.PlatformProperties;
//import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
//import com.sgcc.isc.core.orm.organization.BusinessOrganization;
//import com.sgcc.isc.ualogin.client.vo.IscSSOUserBean;
//import io.swagger.annotations.Api;
//import io.swagger.annotations.ApiOperation;
//import lombok.AllArgsConstructor;
//import com.allcore.common.utils.CommonUtil;
//import com.allcore.core.boot.ctrl.AllcoreController;
//import com.allcore.core.tool.api.R;
//import com.allcore.system.entity.Role;
//import com.allcore.system.feign.ISysClient;
//import com.allcore.system.user.entity.User;
//import com.allcore.system.user.feign.IUserClient;
//import org.springframework.web.bind.annotation.*;
//
//import java.util.List;
//
///**
// * isc登录认证
// *
// * <AUTHOR>
// * @date 2022/08/01 11:03
// **/
//@RestController
//@AllArgsConstructor
//@RequestMapping("/test")
//@Api(value = "test", tags = "test接口")
//public class TestController extends AllcoreController {
//
//	private final IIscService iIscService;
//	private final IIgwService igwService;
//
//	private final IAlgorithmService algorithmService;
//
//	private final PlatformProperties platformProperties;
//
//	private final ZtQueryLogService queryLogService;
//
//	private final IUserClient userClient;
//
//	private final ISysClient sysClient;
//
//	private final ZtClient client;
//
//	/********************************************isc接口测试*********************************************/
//	/**
//	 * （获取isc用户信息）
//	 * <AUTHOR>
//	 * @date 2022/09/21 09:27
//	 * @param userId
//	 * @return com.allcore.core.tool.api.R<com.allcore.system.user.entity.User>
//	 */
//	@GetMapping("/isc/getUserByIscUserId")
//	@ApiOperationSupport(order = 1)
//	@ApiOperation(value = "isc 获取isc用户信息", notes = "isc 获取isc用户信息")
//	public R<User> getUserByIscUserId(@RequestParam("userId") String userId) {
//		IscSSOUserBean iscSSOUserBean = new IscSSOUserBean();
//		iscSSOUserBean.setIscUserId(userId);
//		return R.data(iIscService.getUser(iscSSOUserBean));
//	}
//
//	/**
//	 * （测试isc 单位性质翻译）
//	 * <AUTHOR>
//	 * @date 2022/09/09 10:18
//	 * @return com.allcore.core.tool.api.R
//	 */
//	@GetMapping("/isc/queryDeptInfoById")
//	@ApiOperationSupport(order = 2)
//	@ApiOperation(value = "isc 单位性质翻译", notes = "isc 单位性质翻译")
//	public R<List<BusinessOrganization>> queryDeptInfoById(@RequestParam String id) {
//		return R.data(iIscService.queryDeptInfoById(id));
//	}
//
//
//	/**
//	 * （获取现有的单位性质翻译 用于字典配置）
//	 * <AUTHOR>
//	 * @date 2022/09/21 10:45
//	 * @return com.allcore.core.tool.api.R
//	 */
//	@GetMapping("/isc/queryNatureInfosForDictBiz")
//	@ApiOperationSupport(order = 3)
//	@ApiOperation(value = "isc 获取现有的单位性质翻译", notes = "isc 获取现有的单位性质翻译")
//	public R queryNatureInfosForDictBiz() {
//		return R.data(iIscService.queryNatureInfosForDictBiz());
//	}
//
//	/**
//	 * （isc ticket校验并获取userId等信息）
//	 * <AUTHOR>
//	 * @date 2022/09/21 10:48
//	 * @param ticket
//	 * @return com.allcore.core.tool.api.R<com.sgcc.isc.ualogin.client.vo.IscSSOUserBean>
//	 */
//	@GetMapping("/isc/iscValidate")
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "isc ticket校验并获取userId等信息", notes = "isc ticket校验并获取userId等信息")
//	public R<IscSSOUserBean> iscValidate(String ticket){
//		return R.data(iIscService.iscValidate(ticket));
//	}
//
//
//	/***********************************************i国网接口测试****************************************/
//	/**
//	 * （测试ticket）
//	 * <AUTHOR>
//	 * @date 2022/09/21 09:34
//	 * @param ticket
//	 * @return com.allcore.core.tool.api.R<com.alibaba.fastjson.JSONObject>
//	 */
//	@GetMapping("/igw/getUserByTicket")
//	@ApiOperationSupport(order = 5)
//	@ApiOperation(value = "igw 测试ticket", notes = "igw 测试ticket")
//	public R<JSONObject> getUserByTicket(@RequestParam("ticket") String ticket) {
//		return R.data(igwService.getUserByTicket(ticket));
//	}
//
//	/**************************************************算法测试**********************************************/
//
//	@GetMapping("/algorithm/invokingAlgorithm")
//	@ApiOperationSupport(order = 6)
//	@ApiOperation(value = "土星算法", notes = "土星算法")
//	public R<String> invokingAlgorithm(@RequestBody AlgorithmDTO algorithmDTO) {
//		return R.data(algorithmService.invokingAlgorithm(algorithmDTO));
//	}
//
//	@GetMapping("/algorithm/invokingAlgorithmLyy")
//	@ApiOperationSupport(order = 7)
//	@ApiOperation(value = "联研院算法", notes = "联研院算法")
//	public R<String> invokingAlgorithmLyy(@RequestBody AlgorithmLyyDTO algorithmLyyDTO) {
//		return R.data(algorithmService.invokingAlgorithmLyy(algorithmLyyDTO));
//	}
//
//	/**************************************测试 properties*********************************************/
//	@GetMapping("/properties")
//	@ApiOperationSupport(order = 8)
//	@ApiOperation(value = "测试properties", notes = "测试properties")
//	public void properties() {
//		platformProperties.isLocalIsc();
//	}
//
//	/*****************************************中台代码调用测试*********start******************************************/
//
//	@RequestMapping("/zt_nochange")
//	public void zt_nochange(@RequestParam String id){
//		//1-6
//		client.GetEquipByType("");
//		client.GetEquipByCode("");
//		client.SmartDeviceQuery("");
//		client.findSmartDevice("");
//		client.queryLineList();
//		client.QueryByLineContainer("");
//		//9-10
//		client.workPlanList("1","10");
//		client.workPlanQuery("");
//		//13
//		client.workorderQuerycxByPmsCode("",1,10);
//		//17-18
//		client.workRecordList("");
//		client.workRecordQuery("");
//		//20-21
//		client.getDefectDataFromZT("");
//		client.getSelectDefectOneDataByDetail("");
//	}
//	@PostMapping("/zt_7")
//	public void zt_7(@RequestBody WorkPlanRequest workPlanRequest){
//		client.workPlanCreate(workPlanRequest);
//	}
//
//	@PostMapping("/zt_8")
//	public void zt_8(@RequestBody WorkPlanRequest workPlanRequest){
//		client.workPlanChange(workPlanRequest);
//	}
//
//	@PostMapping("/zt_11")
//	public void zt_11(@RequestBody WorkOrderRequest workOrderRequest){
//		client.workorderCreate(workOrderRequest);
//	}
//
//	@PostMapping("/zt_12")
//	public void zt_12(@RequestBody WorkOrderRequest workOrderRequest){
//		client.workorderChange(workOrderRequest);
//	}
//
//	@RequestMapping("/zt_14")
//	public void zt_14(@RequestParam String inspectGuidListStr){
//		//14
//		client.workorderQuerycxByGuid(inspectGuidListStr);
//	}
//
//	@PostMapping("/zt_15")
//	public void zt_15(@RequestBody WorkRecordRequest workRecordRequest){
//		client.workRecordCreate(workRecordRequest);
//	}
//
//	@PostMapping("/zt_16")
//	public void zt_16(@RequestBody WorkRecordRequest workRecordRequest){
//		client.workRecordUpdate(workRecordRequest);
//	}
//
//	@PostMapping("/zt_19")
//	public void zt_16(@RequestBody List<DefectPictureDetailDTO> detailDTOList){
//		client.syncDefectData(detailDTOList);
//	}
//
//
//
//	/*****************************************中台代码调用测试*******end********************************************/
//
//
//	@RequestMapping("/userByAccount")
//	public void userByAccount(){
//		R<User> r = userClient.userByAccount(CommonUtil.ISC_TENANT_ID,"*************");
//
//		R<List<Role>> rr = sysClient.getRoleByRoleAlias(CommonUtil.ISC_TENANT_ID,"asdasdasdasd");
//
//
//		System.out.println(r);
//	}
//
//
//
//}
