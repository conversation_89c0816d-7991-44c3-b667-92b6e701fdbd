package com.allcore.platform.controller;

import com.alibaba.fastjson.JSON;
import com.allcore.platform.service.ZtQueryLogService;
import com.allcore.platform.entity.TbQueryZtLog;
import com.allcore.platform.vo.ZhongTaiparamVo;
import com.allcore.platform.util.ZtHttpUtils;
import com.allcore.platform.util.ZtUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import com.allcore.common.config.ZtConfig;
import org.springframework.context.annotation.Scope;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


@RestController
@RequestMapping("/inner/Equipment")
@Scope("prototype")
@Slf4j
@Api(value = "中台-设备", tags = "中台-设备 7个")
public class ZTInnerEquipmentController {

    @Resource
    private ZtQueryLogService queryLogService;

	/**
	 * （重庆中台21-5 输电线路列表查询
	 * 按照运维单位、所属城市、所属网省查询输电线路清单，
	 * 考虑跨区线路数量统计不准情况问题，如跨市线路，
	 * 在省公司层面查询统计线路时，只算一条，
	 * 在地市公司层面查询统计时，每个地方个算一条）
	 * <AUTHOR>
	 * @date 2022/10/09 16:32
	 * @return void
	 */
	@RequestMapping("/QueryLineList")
    public void QueryLineList(){
            ZhongTaiparamVo headerparam = new ZhongTaiparamVo();
            headerparam.set_api_name("PSRCenter.psrTSService.queryLineList");
            headerparam.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.QueryLineList);
            headerparam.setClient(ZtConfig.CLIENT);
            headerparam.setSignature(ZtConfig.SIGNATURE);
            headerparam.setToken( ZtUtil.getToken());
            headerparam.set_api_version("1.0.0");
            headerparam.setDate(System.currentTimeMillis() + "");
            Map<String,Object> map = new HashMap<>();
            map.put("page","1");
            map.put("perPage", 10);
            map.put("field", "name,id,class_name");
            List<Map<String, Object>> filters = new ArrayList<>();
            Map<String,Object> filtersMap = new HashMap<>();
            filtersMap.put("fieldName","depart");
            filtersMap.put("compare","=");
            filtersMap.put("fieldValue","112355545");
            filters.add(filtersMap);
            map.put("filters", filters);
            map.put("distribution", 0);
            map.put("psrType", "0103");
            List list = new ArrayList();
            list.add(map);
            String result = ZtHttpUtils.httpPostJsonArraySend(headerparam,list);
            log.info("----------------------------------输电线路查询----------------" + result);
			TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(map), result, ZtConfig.QueryLineList, 3);
			queryLogService.insertZtLog(tbQueryZtLog);
    }

}
