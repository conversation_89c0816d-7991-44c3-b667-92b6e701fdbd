package com.allcore.platform.wrapper;

import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.platform.entity.DataMngJson;
import com.allcore.platform.vo.DataMngJsonVO;
import java.util.Objects;

/**
 * 数据处理json表包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 * @since 2022-10-08
 */
public class DataMngJsonWrapper extends BaseEntityWrapper<DataMngJson, DataMngJsonVO>  {

	public static DataMngJsonWrapper build() {
		return new DataMngJsonWrapper();
 	}

	@Override
	public DataMngJsonVO entityVO(DataMngJson dataMngJson) {
		DataMngJsonVO dataMngJsonVO = Objects.requireNonNull(BeanUtil.copy(dataMngJson, DataMngJsonVO.class));

		//User createUser = UserCache.getUser(dataMngJson.getCreateUser());
		//User updateUser = UserCache.getUser(dataMngJson.getUpdateUser());
		//dataMngJsonVO.setCreateUserName(createUser.getName());
		//dataMngJsonVO.setUpdateUserName(updateUser.getName());

		return dataMngJsonVO;
	}

}
