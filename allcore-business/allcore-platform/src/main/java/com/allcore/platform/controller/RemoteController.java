package com.allcore.platform.controller;


import com.allcore.platform.service.RemoteService;
import com.allcore.platform.entity.Specific;
import com.allcore.platform.vo.HttpApiLog;
import com.allcore.common.constant.Constant;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import com.allcore.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;


/**
 * 接口调用及检测
 * <AUTHOR>
 * @date 2022/04/14 19:51
 **/
@RestController
@AllArgsConstructor
@RequestMapping("/remote")
public class RemoteController {

	@Autowired
	private RemoteService remoteService;


	/**
	 * 检测Ip和端口是否可用 timeOut是超时时间
	 *
	 * @return
	 */
	@PostMapping("/monitoringNetwork")
	@ApiOperationSupport(order = 1)
	public R isHostConnectable(@Valid @RequestBody Specific specific) {
		HttpApiLog httpApiLog = remoteService.isHostConnectable(specific.getHost(), Integer.parseInt(specific.getPort()), Constant.TIME_OUT, specific.getSpecificGuid());
		return R.data(specific.getHost()+httpApiLog.getMessage());
	}


	@PostMapping("/requestByParameter")
	@ApiOperationSupport(order = 2)
	public String  requestByParameter(@ApiParam(value = "urlCode", required = true) @RequestParam String urlCode,
							@ApiParam(value = "map", required = false) @RequestBody Map<String, String> map) {
		String message = remoteService.getRemoteDataByUriCode(urlCode, map);
		return message;
	}

	@PostMapping("/requestByJson")
	@ApiOperationSupport(order = 3)
	public String  requestByJson(@ApiParam(value = "urlCode", required = true) @RequestParam String urlCode,
							@ApiParam(value = "json", required = false) @RequestParam String json) {
		String message = remoteService.postRemoteDataByUriCode(urlCode, json);
		return message;
	}
}
