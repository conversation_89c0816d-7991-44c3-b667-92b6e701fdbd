package com.allcore.platform.controller;

import com.alibaba.fastjson.JSON;
import com.allcore.platform.service.ZtQueryLogService;
import com.allcore.platform.entity.TbQueryZtLog;
import com.allcore.platform.vo.ZhongTaiparamVo;
import com.allcore.platform.util.ZtHttpUtils;
import com.allcore.platform.util.ZtUtil;
import io.swagger.annotations.Api;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.allcore.common.config.ZtConfig;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/inner/Ztcontroller")
@Slf4j
@AllArgsConstructor
@Api(value = "中台-备品备件、智能装备", tags = "中台-备品备件、智能装备 7个")
public class ZTSparePartInfoController {
	private final ZtQueryLogService queryLogService;

    /**
	 * （重庆中台21-3 智能装备台帐查看--提供查看无人机、机器人等智能装备台帐参数详情的服务能力）
	 * <AUTHOR>
	 * @date 2022/10/09 16:27
	 * @param id
	 * @return void
	 */
    @RequestMapping("/SmartDeviceQuery")
    public void SmartDeviceQuery(@RequestParam String id){
            ZhongTaiparamVo headerparam = new ZhongTaiparamVo();
            headerparam.set_api_name("WRCenter.SmartDevice.SmartDeviceByID");
            headerparam.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.SmartDeviceByIDQuery+"?ID=e84e2d2218154971ac13409db2fd71f6&accountClass=9601");
            headerparam.setClient(ZtConfig.CLIENT);
            headerparam.setSignature(ZtConfig.SIGNATURE);
            headerparam.setToken( ZtUtil.getToken());
            headerparam.set_api_version("1.0.0");
            headerparam.setDate(System.currentTimeMillis() + "");

            Map<String, String> map = new HashMap<>();
            map.put("ID", "e84e2d2218154971ac13409db2fd71f6");
            map.put("accountClass", "9601");
            String rls= ZtHttpUtils.httpPostSend(headerparam,map);
            System.out.println("-3--------------------智能装备台帐查看--------------------------：" + rls);
            TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(map),rls, ZtConfig.SmartDeviceByIDQuery,5);
			queryLogService.insertZtLog(tbQueryZtLog);
            findSmartDevice(id);
    }

	/**
	 * （重庆中台21-4 智能装备台帐查询-提供查询无人机、机器人等智能装备台帐列表的服务能力）
	 * <AUTHOR>
	 * @date 2022/10/09 16:29
	 * @param id
	 * @return void
	 */
    @RequestMapping("/findSmartDevice")
    public void findSmartDevice(@RequestParam String id){
            ZhongTaiparamVo headerparam = new ZhongTaiparamVo();
            headerparam.set_api_name("WRCenter.SmartDevice.findSmartDevice");
            headerparam.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.findSmartDeviceList);
            headerparam.setClient(ZtConfig.CLIENT);
            headerparam.setSignature(ZtConfig.SIGNATURE);
            headerparam.setToken( ZtUtil.getToken());
            headerparam.set_api_version("1.0.0");
            headerparam.setDate(System.currentTimeMillis() + "");

            Map<String,Object> map = new HashMap<>();
            map.put("accountClass","9601");

            Map<String,Object> params = new HashMap<>();
            params.put("fields", "");
            params.put("page","1");
            params.put("perPage", 10);

            List<Map<String, Object>> filters = new ArrayList<>();
            Map<String,Object> filtersMap = new HashMap<>();
            filtersMap.put("fieldName","mRID");
            filtersMap.put("compare","=");
            filtersMap.put("fieldValue","e84e2d2218154971ac13409db2fd71f6");
            filters.add(filtersMap);

            params.put("filters", filters);
            params.put("orderBy", "mRID desc");

            map.put("params", params);


            String rls= ZtHttpUtils.httpPostSend(headerparam,map);
            System.out.println("-4--------------------智能装备台帐查询--------------------------：" + rls);
            TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(params),rls, ZtConfig.findSmartDeviceList,5);
			queryLogService.insertZtLog(tbQueryZtLog);
    }

	/**
	 * （重庆中台21-6 输电线路下辖设备查询-
	 *  按照指定输电线路ID、下辖设备类型查询输电线路下设备信息列表，支持多设备类型查询）
	 * <AUTHOR>
	 * @date 2022/10/09 16:32
	 * @return void
	 */
	@RequestMapping("/QueryByLineContainerForBJ")
	public void QueryByLineContainer(@RequestParam String id){
			log.info("-----------------------------接收到到请求开始查询输电线路所属设备---------------------------");
			ZhongTaiparamVo headerparam = new ZhongTaiparamVo();
			headerparam.set_api_name("PSRCenter.psrTSService.queryByLineContainer");
			headerparam.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.queryByLineContainer);
			headerparam.setClient(ZtConfig.CLIENT);
			headerparam.setSignature(ZtConfig.SIGNATURE);
			headerparam.setToken( ZtUtil.getToken());
			headerparam.set_api_version("1.0.0");
			headerparam.setDate(System.currentTimeMillis() + "");

			//默认是湖南，
			Map<String,Object> map = new HashMap<>();
			map.put("page","1");
			map.put("perPage", 10);
			map.put("psrId", "10000000");
			List list = new ArrayList();
			map.put("psrTypeList", list);
			String result = ZtHttpUtils.httpPostSend(headerparam,map);
			log.info("-----------------------------输电线路所属设备查询---------------------------" + result);
			TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(map), result, ZtConfig.queryByLineContainer, 3);
			queryLogService.insertZtLog(tbQueryZtLog);
	}


}

