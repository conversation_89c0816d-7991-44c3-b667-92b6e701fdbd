package com.allcore.platform.service.impl;

import com.allcore.platform.mapper.TbQueryZtLogMapper;
import com.allcore.platform.service.ZtQueryLogService;
import com.allcore.platform.entity.TbQueryZtLog;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Service
@Slf4j
@AllArgsConstructor
public class ZtQueryLogServiceImpl implements ZtQueryLogService {

	private final TbQueryZtLogMapper tbQueryZtLogMapper;

	@Override
	public void insertZtLog(TbQueryZtLog tbQueryZtLog) {
		try {
			tbQueryZtLogMapper.insert(tbQueryZtLog);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
}
