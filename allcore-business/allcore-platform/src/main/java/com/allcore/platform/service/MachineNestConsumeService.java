package com.allcore.platform.service;

import com.allcore.platform.entity.MachineNestConsume;
import com.allcore.platform.vo.DroneLedgerVo;
import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.allcore.system.entity.Dept;

import java.util.List;


/**
 * service接口
 *
 * @version 1.0
 * @date 2023-03-02
 */
public interface MachineNestConsumeService extends ZxhcService<MachineNestConsume> {

	/**
	 * 发送消息给前端
	 *
	 * @param jsonData
	 * @param type
	 */
	void sendToWeb(String jsonData, String type,String messageId);

	R<List<DroneLedgerVo>> selectInDeptCode(String deptCode);
}
