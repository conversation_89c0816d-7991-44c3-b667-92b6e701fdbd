package com.allcore.platform.mapper;


import com.allcore.platform.entity.PlatformInfo;
import com.allcore.platform.vo.PlatformInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 外部平台集成表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
public interface PlatformInfoMapper extends BaseMapper<PlatformInfo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param platformInfo
	 * @return
	 */
	List<PlatformInfoVO> selectPlatformInfoPage(IPage page,@Param("platform") PlatformInfoVO platformInfo);

}
