package com.allcore.platform.controller;

import com.alibaba.fastjson.JSONObject;
import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.platform.props.PlatformProperties;
import com.allcore.platform.util.HttpClientUtil;
import com.allcore.platform.vo.VideoRstVO;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * （统一视频接口）
 * <AUTHOR>
 * @date 2022/09/21 16:56
 * @return
 */
@RestController
@RequestMapping(value = "/unifiedVideo")
@AllArgsConstructor
@Slf4j
public class VideoController extends AllcoreController {

	private AllcoreRedis allcoreRedis;

	private final PlatformProperties platformProperties;


	/**
	 * 统一视频平台接口授权Token有效期
	 */
	private final long validateAuthExpire = 60 * 30;

	/**
	 * （统一视频平台视频播放接口）
	 *
	 * @param code   设备编号，必选
	 * @param format 码流类型，支持mp4、flv和ps，默认是mp4。必选
	 * @param codec  视频格式，支持h264、h265.默认是h264，可选
	 *               redirect  //默认是true，true的情况下HTTP状态码是301，自动重定向；false 是HTTP状态码是200，响应中有location重定向字段
	 * @return com.allcore.core.tool.api.R<com.alibaba.fastjson.JSONObject>
	 * <AUTHOR>
	 * @date 2022/09/21 17:01
	 */
	@RequestMapping("/getDeviceMediaPlay")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "统一视频平台视频播放接口", notes = "统一视频平台视频播放接口")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "设备编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "format", value = "码流类型", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "codec", value = "视频格式", paramType = "query", dataType = "string")
	})
	public R<VideoRstVO> getDeviceMediaPlay(@RequestParam(required = true) String code,
											@RequestParam(required = false) String format,
											@RequestParam(required = false) String codec,
											@RequestParam(required = false) String ak,
											@RequestParam(required = false) String nonce) {

		VideoRstVO vo = new VideoRstVO();

		String url = platformProperties.getUnifiedHostIp() + ":" + platformProperties.getUnifiedHostPort() + "/uvp-micro-service/mediatranscode/api/v1/play";

		Map<String, String> params = this.initPublicParams();
		if (StringUtils.isEmpty(params.get("token"))) {
			vo.setErrorMsg("token为空，【token未获取到】或【Redis未存储token】");
			return R.data(vo);
		}

		params.put("code", code);
		String sessionId = UUID.randomUUID().toString().replace("-", "").toUpperCase();
		params.put("sessionId", sessionId);
		params.put("format", format);
		params.put("codec", codec);
		params.put("ak", ak);
		params.put("nonce", nonce);
		params.put("redirect", Boolean.FALSE.toString());
		String result = HttpClientUtil.doGet(url, params);
		log.info("【调用统一视频平台视频-播放-接口，返回结果】 -> {}", result);
		vo.setOriginalObj(JSONObject.parseObject(result));
		return R.data(vo);
	}


    //TODO 需要app服务
//	/**
//	 * 供web端调用 获取视频流地址
//	 *
//	 * @param equipmentNo
//	 * @return
//	 * @throws NoSuchAlgorithmException
//	 */
//	@RequestMapping("/getMediaUrl")
//	public R<VideoRstVO> getMediaUrl(@RequestParam(required = true) String equipmentNo) throws NoSuchAlgorithmException {
//		VideoRstVO vo = new VideoRstVO();
//
//
//		if (StringUtils.isEmpty(equipmentNo)) {
//			vo.setErrorMsg("设备号必传！");
//			return R.data(vo);
//		}
//
//		//调用app feign获取通道
//		R<AppVideo> r = appClient.getAppVideo(equipmentNo, 1);
//		if (!r.isSuccess()) {
//			vo.setErrorMsg("查询直播失败！");
//			return R.data(vo);
//		}
//		if (Func.isEmpty(r.getData()) || Func.isEmpty(r.getData().getId())) {
//			vo.setErrorMsg("该设备未开始直播！");
//			return R.data(vo);
//		}
//
//
//		/*开发环境*/
//		if (platformProperties.isLocalIsc()) {
//			JSONObject result = new JSONObject();
//			result.put("location", "webrtc://**********/live/" + r.getData().getChannelCode());
//			vo.setOriginalObj(result);
//			return R.data(vo);
//		}
//
//		R<VideoRstVO> deviceMediaPlay = getDeviceMediaPlay(r.getData().getChannelCode(), "mp4", "h264", "piadmin", "KJGH87G8OYVIFTYVI6T7YO");
//		String videoName = buildVideoName(equipmentNo, r.getData().getDeviceInfo());
//		deviceMediaPlay.getData().getOriginalObj().put("videoName", videoName);
//		return deviceMediaPlay;
//
//	}

	/**
	 * （构造视频名称
	 * 格式：单位_sn码_飞手.mp4）
	 *
	 * @param equipmentNo
	 * @param deviceInfo
	 * @return java.lang.String
	 * <AUTHOR>
	 * @date 2022/12/16 14:35
	 */
	private String buildVideoName(String equipmentNo, String deviceInfo) {
		String videoName = equipmentNo + ".mp4";
		if (StringUtils.isNotBlank(deviceInfo)) {
			try {
				JSONObject deviceInfoJson = JSONObject.parseObject(deviceInfo);
				videoName =
					deviceInfoJson.getString("deptName") + StringPool.UNDERSCORE
						+ equipmentNo + StringPool.UNDERSCORE
						+ deviceInfoJson.getString("flyerName") + ".mp4";
			} catch (Exception e) {
				log.info("解析设备信息中的视频名称代码异常");
				log.error(e.getMessage());
			}
		}
		return videoName;
	}


    //TODO 需要app服务
//	@RequestMapping("/getAllMediaUrlByDept")
//	public R<VideoRstArrVO> getMediaUrlByDept(String snCodes) throws NoSuchAlgorithmException {
//
//		VideoRstArrVO arrVO = new VideoRstArrVO();
//
//		List<JSONObject> rst = new ArrayList<>();
//
//		if (StrUtil.isEmpty(snCodes)) {
//			return R.data(arrVO);
//		}
//
//		//调用app feign获取通道
//		R<List<AppVideo>> r = appClient.getAppVideoList(1, snCodes);
//		if (!r.isSuccess()) {
//			arrVO.setErrorMsg("查询直播失败");
//			return R.data(arrVO);
//		}
//		if (CollectionUtil.isEmpty(r.getData())) {
//			arrVO.setErrorMsg("该设备未开始直播");
//			return R.data(arrVO);
//		}
//
//		/*开发环境*/
//		if (platformProperties.isLocalIsc()) {
//			for (AppVideo appVideo : r.getData()) {
//				JSONObject result = new JSONObject();
//				result.put("location", "webrtc://**********/live/" + appVideo.getChannelCode());
//				String videoName = buildVideoName(appVideo.getEquipmentNo(), appVideo.getDeviceInfo());
//				result.put("videoName", videoName);
//				rst.add(result);
//			}
//			arrVO.setOriginalArrObj(rst);
//			return R.data(arrVO);
//		}
//
//		r.getData().forEach(e -> {
//			R<VideoRstVO> deviceMediaPlay = getDeviceMediaPlay(e.getChannelCode(), "mp4", "h264", "piadmin", "KJGH87G8OYVIFTYVI6T7YO");
//			String videoName = buildVideoName(e.getEquipmentNo(), e.getDeviceInfo());
//			deviceMediaPlay.getData().getOriginalObj().put("videoName", videoName);
//			rst.add(deviceMediaPlay.getData().getOriginalObj());
//		});
//		arrVO.setOriginalArrObj(rst);
//		return R.data(arrVO);
//
//	}


	//****************************************下面为基础工具类****************************************************************************

	/**
	 * （封装接口请求公共参数）
	 *
	 * @return java.util.Map<java.lang.String, java.lang.String>
	 * <AUTHOR>
	 * @date 2022/09/21 17:05
	 */
	public Map<String, String> initPublicParams() {
		String token = allcoreRedis.get("videoAuthToken");
		if (StringUtils.isEmpty(token)) {
			this.getVideoAuth();
			token = allcoreRedis.get("videoAuthToken");
		}

		Map<String, String> params = new HashMap<String, String>();
		params.put("ak", platformProperties.getUnifiedAssessKey());
		params.put("token", token);
		params.put("timestamp", String.valueOf(System.currentTimeMillis()));
		String nonce = UUID.randomUUID().toString().replace("-", "").toUpperCase();
		params.put("nonce", nonce);

		return params;
	}

	//************************请求的组装测试*******************************************************************//

	/**
	 * （应用接口访问授权，获取token，token过期周期30m）
	 *
	 * @return void
	 * <AUTHOR>
	 * @date 2022/09/21 17:05
	 */
	public void getVideoAuth() {
		String url = platformProperties.getUnifiedHostIp() + ":" + platformProperties.getUnifiedHostPort() + "/uvp-backend-common/api/v1/authorization";
		JSONObject params = new JSONObject();
		log.info("【调用统一视频平台应用授权接口，返回结果ak和sk】 -> ak：{}; sk：{}", platformProperties.getUnifiedAssessKey(), platformProperties.getUnifiedSecretKey());
		params.put("ak", platformProperties.getUnifiedAssessKey());
		params.put("sk", platformProperties.getUnifiedSecretKey());
		String result = HttpClientUtil.doPostJson(url, JSONObject.toJSONString(params));

		log.info("【调用统一视频平台应用授权接口，返回结果result】 -> {}", result);
		if (StringUtils.isNotEmpty(result)) {
			JSONObject resultJson = JSONObject.parseObject(result);
			Boolean successful = resultJson.getBoolean("successful");
			if (successful) {
				JSONObject resultValueJson = resultJson.getJSONObject("resultValue");
				log.info("【调用统一视频平台应用授权接口，返回结果token】 -> {}", resultValueJson);
				allcoreRedis.setEx("videoAuthToken", resultValueJson.getString("token"), validateAuthExpire);
			}
		}
	}
}
