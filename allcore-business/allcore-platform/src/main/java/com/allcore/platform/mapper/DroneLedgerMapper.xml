<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.platform.mapper.DroneLedgerMapper">

    <select id="selectBySnCode" resultType="com.allcore.platform.vo.DroneLedgerVo">
        SELECT
            id,
            manufactorName,
            brandName,
            nestModel,
            personLiable,
            longitude,
            latitude,
            takeOffLon,
            takeOffLat,
            operationUnit,
            operationCenter,
            operationTeam,
            commisDate,
            productDate,
            nestType,
            nestSNCode,
            nestName,
            uavPowerMode,
            uavControlNum,
            nestPowerMode,
            patrolRadius,
            highestHeight,
            altitude,
            nestState,
            takeOffHeight,
            communicationMode,
            doorOpenType,
            ipAddress,
            portNum,
            simCode
        FROM
            drone_ledger
        WHERE nestSNCode=#{snCode}
    </select>
    <select id="selectInDeptCode" resultType="com.allcore.platform.vo.DroneLedgerVo">
        SELECT
        id,
        manufactorName,
        brandName,
        nestModel,
        personLiable,
        longitude,
        latitude,
        takeOffLon,
        takeOffLat,
        operationUnit,
        operationCenter,
        operationTeam,
        commisDate,
        productDate,
        nestType,
        nestSNCode,
        nestName,
        uavPowerMode,
        uavControlNum,
        nestPowerMode,
        patrolRadius,
        highestHeight,
        altitude,
        nestState,
        takeOffHeight,
        communicationMode,
        doorOpenType,
        ipAddress,
        portNum,
        simCode
        FROM
        drone_ledger
        WHERE
        operationTeam like concat(#{deptCode,jdbcType=VARCHAR},'%')
    </select>
</mapper>
