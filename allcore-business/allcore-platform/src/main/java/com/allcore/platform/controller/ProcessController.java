package com.allcore.platform.controller;

import com.alibaba.fastjson.JSONObject;
import com.allcore.auth.feign.AllcoreAuthClient;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.common.utils.AESCrypt;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.node.ForestNodeMerger;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.dict.entity.DictBiz;
import com.allcore.dict.feign.IDictBizClient;
import com.allcore.platform.service.BlackBoxService;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;

import java.security.GeneralSecurityException;
import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
/**
 * 通用接口
 *
 * <AUTHOR>
 * @date 2022/9/27 19:15
 */
@RestController
@AllArgsConstructor
@Slf4j
@RequestMapping("/process")
@Api(value = "pc端航迹规划", tags = "process接口")
public class ProcessController {


	private final static String VERSION_V1 = "v1";

	private final static String VERSION_V2 = "v2";

	private final static String VERSION_V3 = "v3";

	private final AllcoreAuthClient authClient;
	private final ISysClient sysClient;

	private final IDictBizClient dictBizClient;




	@Autowired
	private BlackBoxService boxService;


	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "登录", notes = "登录")
	@PostMapping("/{version}/apiLogin")
	public R apiLogin(
		@RequestParam(value = "username") String username,
		@RequestParam(value = "password") String password,
		@RequestParam(value = "fromType") String fromType,
		@RequestParam(value = "authorization") String authorization,
		@RequestParam(value = "tenantId") String tenantId,
		@PathVariable String version) {
		if (CommonConstant.VERSION_V1.equals(version)) {

			//body
			MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
			/*登录账号*/
			body.add("username", username);
			/*密码需要MD5加密传输  Jsepc01!  32位 小写  对应 95c75b126b7382bdb0cedeafab114c1a*/
			body.add("password", password);
			body.add("grant_type", "password");
			body.add("scope", "all");
			/*来源app*/
			body.add("fromType", fromType);

			MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
			headers.add("Authorization", "Basic " + authorization);
			headers.add("Tenant-Id", tenantId);
			headers.add("Content-Type", "x-www-form-urlencoded");
			//调用auth服务
			log.info("[接口名称：调用auth][请求数据body：{}]]", body);
			Object token = authClient.postAccessToken(body,headers);
			if (token instanceof  String && token.toString().contentEquals("获取token失败")){
				return R.fail("登陆失败");
			}
			LinkedHashMap<String,Object> map = (LinkedHashMap<String, Object>) token;
			return R.data(map);
		}
		return R.fail("获取用户失败！");
	}

	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "机构树", notes = "机构树")
	@GetMapping("/{version}/apiDeptTree")
	public R apiDeptTree(@PathVariable String version) {
		if (CommonConstant.VERSION_V1.equals(version)) {
			R<List<TreeNode>> r = sysClient.getDepTree("", StringPool.YES, "");
			if (r.isSuccess()) {
				return R.data(ForestNodeMerger.merge(r.getData()));
			} else {
				return R.fail("获取数据失败！");
			}
		}

		return R.fail("获取数据失败！");
	}

	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "根据单位id查询单位信息", notes = "根据单位id查询单位信息")
	@GetMapping("/{version}/apiDeptById")
	public R apiDeptById(
		@RequestParam(value = "id") String id,
		@PathVariable String version) {
		if (CommonConstant.VERSION_V1.equals(version)) {
			R<Dept> r = sysClient.getDept(id);
			if (r.isSuccess()) {
				return R.data(r);
			} else {
				return R.fail("获取数据失败！");
			}
		}

		return R.fail("获取数据失败！");
	}




	// TODO 需要缺陷服务
//	@ApiOperationSupport(order = 5)
//	@ApiOperation(value = "查询缺陷字典", notes = "查询缺陷字典")
//	@GetMapping("/{version}/apiQueryDictionaries")
//	public R apiQueryDictionaries(
//		@RequestParam(value = "dictionariesFullCode") String dictionariesFullCode,
//		@RequestParam(value = "major") String major,
//		@PathVariable String version) {
//		if (CommonConstant.VERSION_V1.equals(version)) {
//
//			R<List<DictionariesVO>> r = defectClient.queryDictionaries(
//				dictionariesFullCode,
//				major
//			);
//
//			if (r.isSuccess()) {
//				return R.data(r.getData());
//			} else {
//				return R.fail("获取数据失败！");
//			}
//		}
//		return R.fail("获取数据失败！");
//	}


//	/**
//	 * 航迹规划-上传完成后返回上传结果；（照片，标注信息，杆塔guid）
//	 * TODO 需要缺陷服务
//	 * @param towerNo
//	 * @param lineName
//	 * @param version
//	 * @return R
//	 * <AUTHOR>
//	 * @date 2022/9/30 17:09
//	 */
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "上传完成后返回上传结果；（照片，标注信息，杆塔guid）", notes = "上传完成后返回上传结果；（照片，标注信息，杆塔guid）")
//	@PostMapping("/{version}/getInfoByUpload")
//	public R getInfoByUpload(@RequestParam(value = "files") MultipartFile[] files,
//							 @RequestParam(value = "towerNo") String towerNo,
//							 @RequestParam(value = "lineName") String lineName,
//							 @RequestParam(value = "inspectGuid") String inspectGuid,
//							 @RequestParam(value = "inspectNo") String inspectNo,
//							 @PathVariable String version) {
//		try {
//
//			if (StringUtils.isBlank(towerNo) || StringUtils.isBlank(lineName)) {
//				return R.fail("缺少入参!");
//			}
//
//
//			if (version.equals(VERSION_V1)) {
//				LineWrapper lineWrapper = new LineWrapper();
//				lineWrapper.setName(lineName);
//				R res = deviceClient.getOneLine(lineWrapper);
//				if (res.isSuccess()) {
//					List<AccountSpaceVo> accountSpaces = new ArrayList<>();
//					if (null == res.getData()) {
//						return R.fail("查询线路异常！");
//					}
//					accountSpaces = (List<AccountSpaceVo>) res.getData();
//					if (CollectionUtil.isEmpty(accountSpaces)) {
//						return R.success("没有该线路名称！");
//					}
//
//					TowerWrapper wrapper = new TowerWrapper();
//					wrapper.setTowerLineGuid(accountSpaces.get(0).getSpaceGuid());
//					wrapper.setName(towerNo);
//					R<TowerFeignVO> res2 = deviceClient.getOneTower(wrapper);
//					if (res2.isSuccess()) {
//						String towerGuid = res2.getData().getTowerGuid();
//						if (StringUtils.isBlank(towerGuid)) {
//							return R.success("没有该杆塔名称！");
//						}
//
//						//	TASK_TYPE_ARTIFICIAL("artificial","人工"), TASK_TYPE_ALGORITHM("algorithm","算法"),
//						String inspecGuid = "";
//						String isThumb = "no";
//						String strategyGuid = "artificial";
//						R<UpfileResultVO> rst = defectClient.uploadDefectPictures(files, "tms", accountSpaces.get(0).getSpaceGuid(), towerGuid, inspecGuid, inspectNo, isThumb, strategyGuid);
//						if (null == rst) {
//							return R.fail("保存失败");
//						}
//						return R.data(rst);
//
//					}
//
//				}
//
//			}
//
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//
//		return R.fail("处理失败！");
//	}

	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "查询缺陷字典2", notes = "查询缺陷字典2")
	@GetMapping("/{version}/apiFirstLevelDictionary")
	public R apiFirstLevelDictionary(@RequestParam(value = "code") String code, @PathVariable String version) {
		if (CommonConstant.VERSION_V1.equals(version)) {

			R<List<DictBiz>> r = dictBizClient.firstLevelDictionary(
				code
			);

			if (r.isSuccess()) {
				return R.data(r.getData());
			} else {
				return R.fail("获取数据失败！");
			}
		}
		return R.fail("获取数据失败！");
	}


	/**
	 * 重命名-电压等级列表
	 *
	 * @param userId
	 * @param version
	 * @return R
	 * <AUTHOR>
	 * @date 2022/10/9 14:37
	 */
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "获取电压等级列表", notes = "获取电压等级列表")
	@GetMapping("/{version}/getDictByVol")
	public R getDictByVol(@RequestParam(value = "userId", required = false) String userId, @PathVariable String version) {
		try {

			log.info("入参：{}", userId);
			//todo 需要根据人员专业过滤
			if (version.equals(VERSION_V1)) {

				R<List<DictBiz>> r = dictBizClient.firstLevelDictionary(BizDictEnum.VOLATE_LEVEL.getCode());
				if (r.isSuccess()) {
					return R.data(r.getData());
				} else {
					return R.fail("获取数据失败！");
				}
			}
			return R.fail("获取数据失败！");

		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}




//	/**
//	 * 重命名-查询算法信息
//	 * TODO 需要缺陷服务
//	 * @param version
//	 * @return R
//	 * <AUTHOR>
//	 * @date 2022/9/30 17:09
//	 */
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "查询算法信息", notes = "查询算法信息")
//	@GetMapping("/{version}/getStrategy")
//	public R getAlgorithmInfo(@PathVariable String version) {
//		try {
//
//			if (version.equals(VERSION_V1)) {
//				R res = defectClient.queryStrategy();
//				if (res.isSuccess()) {
//					return res;
//				}
//
//			}
//
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//
//		return R.fail("处理失败！");
//	}

//	/**
//	 * 重命名-缺陷信息上传信息接口
//	 * TODO 需要缺陷服务
//	 * <AUTHOR>
//	 * @date 2022/11/1 20:05
//	 * @param dto
//	 * @param version
//	 * @return R
//	 */
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "-缺陷信息上传信息接口", notes = "-缺陷信息上传信息接口")
//	@PostMapping("/{version}/uploadDefectTag")
//	public R uploadDefectTag(@ApiParam(value = "缺陷标注信息") @RequestBody DefectTagDTO dto, @PathVariable String version) {
//		try {
//
//			if (null == dto) {
//				return R.fail("缺少入参!");
//			}
//
//			if (version.equals(VERSION_V1)) {
//				R res = defectClient.uploadDefectTag(dto);
//				if (res.isSuccess()) {
//					return res;
//				}
//
//			}
//
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//
//		return R.fail("处理失败！");
//	}

	// TODO 需要工单服务
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "杆塔编号对应工单（运行杆塔guid）", notes = "杆塔编号对应工单（运行杆塔guid）")
//	@GetMapping("/{version}/getOrderByTowerGuid")
//	public R getOrderByTowerGuid(@RequestParam(value = "towerGuid") String towerGuid,
//								 @RequestParam(value = "deptCode") String deptCode,
//								 @PathVariable String version) {
//		try {
//
//			if (StringUtils.isBlank(towerGuid)||StringUtils.isBlank(deptCode)) {
//				return R.fail("缺少入参!");
//			}
//			List<String> towerGuids = new ArrayList<>();
//			towerGuids.add(towerGuid);
//
//			if (version.equals(VERSION_V1)) {
//				R res = workOrderClient.queryInspectInfoByTowerGuid(towerGuids);
//				if (res.isSuccess()) {
//					List<WorkOrder> workOrderList = (List<WorkOrder>) res.getData();
//					workOrderList =	workOrderList.stream().filter(s->s.getDeptCode().contains(deptCode)).collect(Collectors.toList());
//					res.setData(workOrderList);
//					return res;
//				}
//
//			}
//
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//
//		return R.fail("处理失败！");
//	}



//	/**
//	 * 重命名-请求每个照片对应部件的历史缺陷信息；（线路名称，杆塔号，部件code）
//	 * TODO 需要缺陷服务
//	 * @param
//	 * @param
//	 * @param elementCode
//	 * @param version
//	 * @return R
//	 */
//	@ApiOperationSupport(order = 4)
//	@ApiOperation(value = "请求每个照片对应部件的历史缺陷信息；（线路名称，杆塔号，部件code）", notes = "请求每个照片对应部件的历史缺陷信息；（线路名称，杆塔号，部件code）")
//	@GetMapping("/{version}/getDefectRecordByImage")
//	public R getDefectRecordByImage(@RequestParam(value = "towerGuid") String towerGuid,
//									@RequestParam(value = "elementCode") String elementCode,
//									@RequestParam(value = "startTime") String startTime,
//									@RequestParam(value = "endTime") String endTime,
//									@PathVariable String version) {
//		try {
//
//
//			if (version.equals(VERSION_V1)) {
//				R<List<HistoryTaggingVO>> res3 = defectClient.getHistoryTaggingList(towerGuid, elementCode,startTime,endTime);
//				if (res3.isSuccess()) {
//					if(CollectionUtil.isEmpty(res3.getData())){
//						return R.fail("没有该数据！");
//					}
//					return R.data(res3.getData());
//				}
//
//				return R.fail("没有该数据！");
//
//			}
//
//		} catch (Exception e) {
//			throw new RuntimeException(e);
//		}
//
//		return R.fail("处理失败！");
//	}

	/**
	 * 删除盒子信息
	 *
	 * @param ：加密的字符串
	 * @return
	 */
	@RequestMapping(value = "/deleteBoxById", method = RequestMethod.POST)
	public JSONObject deleteBoxById(@RequestParam("param") String param) {
		JSONObject object = new JSONObject();
		object.put("msg", "删除成功！");
		object.put("code", 200);
		JSONObject jsonObject = null;
		try {
			if (StringUtils.isEmpty(param)) {
				object.put("msg", "参数异常！");
				object.put("code", 500);
				return object;
			}
			try {
				String decrypt = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
				jsonObject = JSONObject.parseObject(decrypt);
			} catch (GeneralSecurityException e) {
				log.info("转换json出错");
				e.printStackTrace();
			}
			if (StringUtils.isEmpty(jsonObject.getString("id"))|| StringUtils.isEmpty(jsonObject.getString("value"))) {
				object.put("msg", "参数异常！");
				object.put("code", 500);
				return object;
			}

			System.out.println("接收到删除智能盒子请求 要删除的盒子id-> {}" + jsonObject.getString("value"));
			String value = jsonObject.getString("value");
			String replace = value.replace("{", "").trim();
			String params = replace.replace("}", "").trim();
			List<String> result = Arrays.asList(params.split(","));
			System.out.println("要删除的盒子id是->:"+result);
			boolean flag = boxService.deleteBoxByParam(result);
			if (flag) {
				return object;
			} else {
				object.put("msg", "删失败！");
				object.put("code", 500);
				return object;
			}

		} catch (Exception e) {
			log.info("【删除数据方法异常】-> {}", e);
		}
		object.put("msg", "删失败！");
		object.put("code", 500);
		return object;
	}


	/**
	 * 校验是否存在同一个时间创建的文件
	 *
	 * @param
	 * @return
	 */
	@RequestMapping(value = "/checkFileByTime", method = RequestMethod.POST)
	public JSONObject checkFileByTime(@RequestParam("param") String param) {
		JSONObject object = new JSONObject();
		object.put("msg", "查询成功！");
		object.put("code", 200);
		JSONObject jsonObject = null;
		try {
			if (StringUtils.isEmpty(param)) {
				object.put("msg", "参数异常！");
				object.put("code", 500);
				return object;
			}
			try {
				String decrypt = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, param);
				jsonObject = JSONObject.parseObject(decrypt);
				System.out.println(jsonObject);
			} catch (GeneralSecurityException e) {
				log.info("转换json出错");
				e.printStackTrace();
			}
			if (StringUtils.isEmpty(jsonObject.getString("id"))|| StringUtils.isEmpty(jsonObject.getString("value"))) {
				object.put("msg", "参数异常！");
				object.put("code", 500);
				return object;
			}
			String value = jsonObject.getString("value");
			JSONObject js = boxService.querySmartBoxByParam(value);
			if (null == js) {
				object.put("data", "");
				return object;
			} else {
				String decrypt = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, js.toJSONString());
				object.put("data", decrypt);
				return object;
			}
		} catch (Exception e) {
			log.info("【转换json出错】-> {}", e);

		}
		object.put("msg", "查询失败！");
		object.put("code", 500);
		return object;
	}

}
