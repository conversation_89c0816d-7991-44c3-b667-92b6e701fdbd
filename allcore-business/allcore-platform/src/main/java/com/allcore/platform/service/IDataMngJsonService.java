package com.allcore.platform.service;

import com.allcore.platform.entity.DataMngJson;
import com.allcore.platform.entity.DataMngLineReportVO;
import com.allcore.platform.vo.DataMngJsonVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.allcore.common.base.ZxhcService;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 数据处理json表 服务类
 *
 * <AUTHOR>
 * @since 2022-10-08
 */
public interface IDataMngJsonService extends ZxhcService<DataMngJson> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param dataMngJson
	 * @return
	 */
	IPage<DataMngJsonVO> selectDataMngJsonPage(IPage<DataMngJsonVO> page, DataMngJsonVO dataMngJson);

	/**
	 * （单档或者多档zip上传）
	 * <AUTHOR>
	 * @date 2022/10/08 13:32
	 * @param file
	 * @param type
	 * @return java.lang.Boolean
	 */
    Boolean upload(MultipartFile file, String type);


	/**
	 * （根据线路guid或者档距段guid查询隐患点数据）
	 * <AUTHOR>
	 * @date 2022/10/08 13:32
	 * @param lineGuid
	 * @param towerGuidRange
	 * @return java.lang.Boolean
	 */
	List<DataMngJsonVO> getDataMngDangerInfosByParamList(String lineGuid, String towerGuidRange,String dangerType,String defectLevel);
	IPage<DataMngJsonVO> getDataMngDangerInfosByParamPage(String lineGuid, String towerGuidRange,String dangerType,String defectLevel,IPage<DataMngJsonVO> page);

	/**
	 * （根据线路guid或者档距段guid查询交跨点数据）
	 * <AUTHOR>
	 * @date 2022/10/10 20:32
	 * @param lineGuid
	 * @param towerGuidRange
	 * @return java.util.List<com.allcore.platform.vo.DataMngJsonVO>
	 */
	List<DataMngJsonVO> getDataMngCrossInfosByParamList(String lineGuid, String towerGuidRange,String crossType);
	IPage<DataMngJsonVO> getDataMngCrossInfosByParamPage(String lineGuid, String towerGuidRange,String crossType,IPage<DataMngJsonVO> page);

	List<String> getCrossTypeList();

	List<String> getDangerTypeList();

	Boolean uploadReport(MultipartFile file, String reportType, String lineName,String startTowerNo,String endTowerNo);

	IPage<DataMngLineReportVO> lineReportPage(String lineName, String reportType, IPage<DataMngLineReportVO> page);
}
