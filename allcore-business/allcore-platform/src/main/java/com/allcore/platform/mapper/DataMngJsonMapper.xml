<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.platform.mapper.DataMngJsonMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="dataMngJsonResultMap" type="com.allcore.platform.entity.DataMngJson">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="dept_code" property="deptCode"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="json_guid" property="jsonGuid"/>
        <result column="content" property="content"/>
        <result column="line_id" property="lineId"/>
        <result column="line_name" property="lineName"/>
        <result column="tower_range" property="towerRange"/>
        <result column="tower_guid_range" property="towerGuidRange"/>
        <result column="cj_time" property="cjTime"/>
    </resultMap>


    <select id="selectDataMngJsonPage" resultMap="dataMngJsonResultMap">
        select id,
               json_guid,
               content,
               line_id,
               line_name,
               tower_range,
               create_user,
               create_dept,
               create_time,
               update_user,
               update_time,
               dept_code,
               is_deleted,
               tower_guid_range,
               cj_time from pc_data_mng_json where is_deleted = 0
    </select>
    <select id="getDataMngDangerInfosByParamList" resultType="com.allcore.platform.vo.DataMngJsonVO">
        select a.id,
        a.json_guid,
        a.content,
        a.line_id,
        a.line_name,
        a.tower_range,
        a.create_user,
        a.create_dept,
        a.create_time,
        a.update_user,
        a.update_time,
        a.dept_code,
        a.is_deleted,
        a.tower_guid_range,
        a.cj_time,b.danger_guid,b.danger_type,b.defect_level,b.dis_to_small_tower,b.dis_c,b.dis_h,b.dis_v,b.line_point_lng,b.line_point_lat,b.line_point_alt,
        b.location_lng,b.location_lat,b.location_alt from pc_data_mng_json a
        left join pc_data_mng_json_danger b on a.json_guid = b.json_guid
        where a.is_deleted = 0 and b.is_deleted = 0
        <if test="lineGuid != null and lineGuid !=''">
            and a.line_name = #{lineGuid}
        </if>
        <if test="towerGuidRange != null and towerGuidRange !=''">
            and a.tower_range = #{towerGuidRange}
        </if>
        <if test="dangerType != null and dangerType !=''">
            and b.danger_type = #{dangerType}
        </if>
        <if test="defectLevel != null and defectLevel !=''">
            and b.defect_level = #{defectLevel}
        </if>
    </select>

    <select id="getDataMngCrossInfosByParamList" resultType="com.allcore.platform.vo.DataMngJsonVO">
        select a.id,
        a.json_guid,
        a.content,
        a.line_id,
        a.line_name,
        a.tower_range,
        a.create_user,
        a.create_dept,
        a.create_time,
        a.update_user,
        a.update_time,
        a.dept_code,
        a.is_deleted,
        a.tower_guid_range,
        a.cj_time,
               b.cross_guid,b.cross_type,b.dis_c,b.dis_h,b.dis_v,b.line_point_lng,b.line_point_lat,b.line_point_alt,
               b.location_lng,b.location_lat,b.location_alt from pc_data_mng_json a
        left join pc_data_mng_json_cross b on a.json_guid = b.json_guid
        where a.is_deleted = 0 and b.is_deleted = 0
        <if test="lineGuid != null and lineGuid !=''">
            and a.line_name = #{lineGuid}
        </if>
        <if test="towerGuidRange != null and towerGuidRange !=''">
            and a.tower_range = #{towerGuidRange}
        </if>
        <if test="crossType != null and crossType !=''">
            and b.cross_type = #{crossType}
        </if>
    </select>

    <select id="getDataMngDangerInfosByParamPage" resultType="com.allcore.platform.vo.DataMngJsonVO">
        select a.id,
        a.json_guid,
        a.content,
        a.line_id,
        a.line_name,
        a.tower_range,
        a.create_user,
        a.create_dept,
        a.create_time,
        a.update_user,
        a.update_time,
        a.dept_code,
        a.is_deleted,
        a.tower_guid_range,
        a.cj_time,b.danger_guid,b.danger_type,b.defect_level,b.dis_to_small_tower,b.dis_c,b.dis_h,b.dis_v,b.line_point_lng,b.line_point_lat,b.line_point_alt,
        b.location_lng,b.location_lat,b.location_alt from pc_data_mng_json a
        left join pc_data_mng_json_danger b on a.json_guid = b.json_guid
        where a.is_deleted = 0 and b.is_deleted = 0
        <if test="lineGuid != null and lineGuid !=''">
            and a.line_name = #{lineGuid}
        </if>
        <if test="towerGuidRange != null and towerGuidRange !=''">
            and a.tower_range = #{towerGuidRange}
        </if>
        <if test="dangerType != null and dangerType !=''">
            and b.danger_type = #{dangerType}
        </if>
        <if test="defectLevel != null and defectLevel !=''">
            and b.defect_level = #{defectLevel}
        </if>
    </select>

    <select id="getDataMngCrossInfosByParamPage" resultType="com.allcore.platform.vo.DataMngJsonVO">
        select a.id,
        a.json_guid,
        a.content,
        a.line_id,
        a.line_name,
        a.tower_range,
        a.create_user,
        a.create_dept,
        a.create_time,
        a.update_user,
        a.update_time,
        a.dept_code,
        a.is_deleted,
        a.tower_guid_range,
        a.cj_time,
        b.cross_guid,b.cross_type,b.dis_c,b.dis_h,b.dis_v,b.line_point_lng,b.line_point_lat,b.line_point_alt,
        b.location_lng,b.location_lat,b.location_alt from pc_data_mng_json a
        left join pc_data_mng_json_cross b on a.json_guid = b.json_guid
        where a.is_deleted = 0 and b.is_deleted = 0
        <if test="lineGuid != null and lineGuid !=''">
            and a.line_name = #{lineGuid}
        </if>
        <if test="towerGuidRange != null and towerGuidRange !=''">
            and a.tower_range = #{towerGuidRange}
        </if>
        <if test="crossType != null and crossType !=''">
            and b.cross_type = #{crossType}
        </if>
    </select>

</mapper>
