package com.allcore.platform.service;

import com.allcore.platform.entity.Specific;
import com.allcore.platform.vo.SpecificVO;
import com.allcore.common.base.ZxhcService;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 平台模板基础信息 服务类
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
public interface ISpecificService extends ZxhcService<Specific> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param specific
	 * @return
	 */
	IPage<SpecificVO> selectSpecificPage(IPage<SpecificVO> page, SpecificVO specific);

}
