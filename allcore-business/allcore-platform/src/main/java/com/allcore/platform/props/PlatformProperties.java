package com.allcore.platform.props;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * PlatformProperties
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "platform")
public class PlatformProperties {
	/**
	 * 名称
	 */
	private String name;

	/**
	 * 是否本地模拟isc
	 */
	private boolean localIsc;
	/**
	 * 统一视频平台IP地址
	 */
	private String unifiedHostIp;
	/**
	 * 统一视频平台网关服务端口
	 */
	private String unifiedHostPort;
	/**
	 * 统一视频平台应用连接密钥
	 */
	private String unifiedAssessKey;
	/**
	 * 统一视频平台应用唯一密匙
	 */
	private String unifiedSecretKey;
}
