package com.allcore.platform.controller;

import com.allcore.auth.feign.AllcoreAuthClient;
import com.allcore.common.constant.CommonConstant;
import com.allcore.common.utils.AESCrypt;
import com.allcore.core.jwt.JwtUtil;
import com.allcore.core.tool.api.R;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.security.GeneralSecurityException;
import java.util.LinkedHashMap;
/**
 * 全景对接模块
 *
 * <AUTHOR>
 * @date 2022/11/15 15:56
 */

@RestController
@RequestMapping("/overview")
@AllArgsConstructor
@Slf4j
@Api(value = "全景对接模块", tags = "全景对接模块")
public class OverallViewController {
	private final AllcoreAuthClient authClient;

	/**
	 * 根据用户名免登录接口
	 * @param userName
	 * @return R
	 * <AUTHOR>
	 * @date 2022/11/16 9:43
	 */
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "登录", notes = "登录")
	@PostMapping(value = "/apiLogin", produces = "application/json;charset=UTF-8")
	public R apiLogin(@RequestParam(value = "userName") String userName) {
		log.info("【全景平台调用免密登录接口】 --> {}", userName);
		String ticket = "vos";
//		userName = StringUtils.isBlank(userName.trim()) ? "04747ab06da86689d04bd0a8e63a9b8a5b5f29399ae1437dc17e83e8cc76ca3" +
//			"675fff2ea5eba39ff26050fd304fc51c882ea0c01ecd509daf991f974bd8fe9fe0edf525955b7e7b3d40eee677040763f2e09" +
//			"f355cfeaa1235e5a7dec74c8fb6a445809f4e7" : userName.trim();
		try {
			userName = AESCrypt.decrypt(CommonConstant.AES_SECRETKEY, userName.trim());
		} catch (GeneralSecurityException e) {
			throw new RuntimeException(e);
		}
		//body
		MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
		/*登录账号*/
		body.add("username", userName);
		/*密码需要MD5加密传输  Jsepc01!  32位 小写  对应 95c75b126b7382bdb0cedeafab114c1a*/
		//ticket方式密码随便填写但是不能为空
		body.add("password", "95c75b126b7382bdb0cedeafab114c1a");
		body.add("grant_type", "password");
		body.add("scope", "all");
		/*来源app*/
		//1app 2树障 3航迹 4PC 5全景
		body.add("fromType", JwtUtil.OV);

		MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
		headers.add("Authorization", "Basic " + "c2FiZXI6c2FiZXJfc2VjcmV0");
		//默认值947313
		headers.add("Tenant-Id", "947313");
		headers.add("Content-Type", "x-www-form-urlencoded");
		if (StringUtils.isNotBlank(ticket)) {
			headers.add("ticket", ticket);
			body.add("ticket", ticket);
		}
		//调用auth服务
		log.info("[接口名称：调用auth][请求数据body：{}]]", body);
		Object token = authClient.postAccessToken(body, headers);
		if (token instanceof String && token.toString().contentEquals("获取token失败")) {
			return R.fail("登陆失败");
		}
		LinkedHashMap<String, Object> resMap = new LinkedHashMap<>();
		resMap.put("Allcore-Auth", ((LinkedHashMap<String, Object>) token).get("access_token"));
		return R.data(resMap);
	}






//	/**
//	 * 巡检计划列表
//	 * TODO 需要工单服务
//	 * @param unitGUID
//	 * @param lineGUID
//	 * @param uavCategory
//	 * @param auditStatu
//	 * @param planStartTime
//	 * @param planEndTime
//	 * @param planType
//	 * @param workState
//	 * @param bizType
//	 * @param unitLevel
//	 * @param page
//	 * @param limit
//	 * @return IPage<QjWorkPlanInfoVO>
//	 * <AUTHOR>
//	 * @date 2022/11/16 10:11
//	 */
//	@ApiOperationSupport(order = 5)
//	@ApiOperation(value = "巡检计划", notes = "巡检计划")
//	@PostMapping(value = "/queryPlanList", produces = "application/json;charset=UTF-8")
//	public R<Object> queryPlanList(@RequestParam(value = "unitGUID") String unitGUID, @RequestParam(value = "lineGUID", required = false) String lineGUID,
//								 @RequestParam(value = "uavCategory", required = false) String uavCategory, @RequestParam(value = "auditStatu", required = false) String auditStatu,
//								 @RequestParam(value = "planStartTime", required = false) String planStartTime, @RequestParam(value = "planEndTime", required = false) String planEndTime,
//								 @RequestParam(value = "planType", required = false) String planType, @RequestParam(value = "workState", required = false) String workState,
//								 @RequestParam(value = "bizType", required = false, defaultValue = "1") Integer bizType, @RequestParam(value = "unitLevel", required = false, defaultValue = "1") Integer unitLevel,
//								 @RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit) {
//		Object object = new Object();
//		try {
//			object = workOrderClient.qjPagePlan(unitGUID, lineGUID, uavCategory, auditStatu, planStartTime, planEndTime, planType, workState, bizType, unitLevel, page, limit).getData();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return R.data(object);
//	}

//	/**
//	 * 巡检工单列表
//	 * TODO 需要工单服务
//	 * @param page
//	 * @param limit
//	 * @param workstarttime
//	 * @param workendtime
//	 * @param unitGUID
//	 * @param inperformstatus
//	 * @return R
//	 * <AUTHOR>
//	 * @date 2022/11/15 17:36
//	 */
//	@ApiOperationSupport(order = 6)
//	@ApiOperation(value = "巡检工单", notes = "巡检工单")
//	@PostMapping(value = "/queryInspectInfoList", produces = "application/json;charset=UTF-8")
//	public R<Object> queryInspectInfoList(@RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit,
//											   @RequestParam(value = "workstarttime", required = false) String workstarttime, @RequestParam(value = "workendtime", required = false) String workendtime,
//											   @RequestParam(value = "unitGUID", required = false) String unitGUID, @RequestParam(value = "inperformstatus", required = false) String inperformstatus) {
//		Object object  = new Object();
//		try {
//			object = workOrderClient.queryInspectInfoByOverallView(page, limit, workstarttime, workendtime, unitGUID, inperformstatus).getData();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return R.data(object);
//	}

//	/**
//	 * 巡检记录列表
//	 * TODO 需要工单服务
//	 * @param page
//	 * @param limit
//	 * @param unitGUID
//	 * @return R
//	 * <AUTHOR>
//	 * @date 2022/11/15 17:35
//	 */
//	@ApiOperationSupport(order = 7)
//	@ApiOperation(value = "巡检记录", notes = "巡检记录")
//	@PostMapping(value = "/queryInspectRecordList", produces = "application/json;charset=UTF-8")
//	public R<Object> queryInspectRecordList(@RequestParam(value = "page", required = false, defaultValue = "1") Integer page, @RequestParam(value = "limit", required = false, defaultValue = "10") Integer limit, @RequestParam(value = "unitGUID", required = false) String unitGUID) {
//		Object object  = new Object();
//		try {
//			object = workOrderClient.queryInspectRecordByOverallView(page, limit, unitGUID).getData();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return R.data(object);
//	}


//	/**
//	 * 本体缺陷列表
//	 * TODO 需要工单服务
//	 * @param page
//	 * @param limit
//	 * @param inspectGuid
//	 * @param unitGUID
//	 * @param lineGUID
//	 * @param startDate
//	 * @param endDate
//	 * @param component
//	 * @param componentType
//	 * @param part
//	 * @return R
//	 * <AUTHOR>
//	 * @date 2022/11/16 10:46
//	 */
//	@ApiOperationSupport(order = 8)
//	@ApiOperation(value = "本体缺陷", notes = "本体缺陷")
//	@PostMapping(value = "/queryDefectList", produces = "application/json;charset=UTF-8")
//	public R<Object> queryDefectList(@RequestParam(value = "page", required = false, defaultValue = "1") int page, @RequestParam(value = "limit", required = false, defaultValue = "10") int limit, @RequestParam(value = "inspectGuid", required = false) String inspectGuid,
//								   @RequestParam(value = "unitGUID", required = false) String unitGUID, @RequestParam(value = "lineGUID", required = false) String lineGUID,
//								   @RequestParam(value = "startDate") String startDate, @RequestParam(value = "endDate") String endDate,
//								   @RequestParam(value = "component", required = false) String component, @RequestParam(value = "componentType", required = false) String componentType,
//								   @RequestParam(value = "part", required = false) String part) {
//		QueryListByMapDTO queryListByMapDTO = new QueryListByMapDTO();
//		queryListByMapDTO.setPage(page);
//		queryListByMapDTO.setLimit(limit);
//		queryListByMapDTO.setInspectGuid(inspectGuid);
//		queryListByMapDTO.setUnitGUID(unitGUID);
//		queryListByMapDTO.setLineGUID(lineGUID);
//		queryListByMapDTO.setStartDate(startDate);
//		queryListByMapDTO.setEndDate(endDate);
//		queryListByMapDTO.setComponent(component);
//		queryListByMapDTO.setComponentType(componentType);
//		queryListByMapDTO.setPart(part);
//		Object object  = new Object();
//		try {
//			object = defectClient.queryListByMap(queryListByMapDTO).getData();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		return R.data(object);
//	}

	/**
	 * AES加密用户名
	 * <AUTHOR>
	 * @date 2023/2/14 17:15
	 * @param userName
	 * @return R
	 */
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "加密用户名", notes = "加密用户名")
	@PostMapping(value = "/sm2EncryptUserName", produces = "application/json;charset=UTF-8")
	public R<String> sm2EncryptUserName(@RequestParam(value = "userName") String userName){
		String encryptUserName = "";
		try {
			encryptUserName = AESCrypt.encrypt(CommonConstant.AES_SECRETKEY, userName);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
		return R.data(encryptUserName);
	}
}
