<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.platform.mapper.SampleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="sampleResultMap" type="com.allcore.platform.vo.SampleVO">
        <result column="id" property="id"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="sample_guid" property="sampleGuid"/>
        <result column="sample_name" property="sampleName"/>
        <result column="platform_guid" property="platformGuid"/>
        <result column="appkey" property="appkey"/>
        <result column="appsecret" property="appsecret"/>
        <result column="sort" property="sort"/>
        <result column="platform_name" property="platformName"/>
    </resultMap>


    <select id="selectSamplePage" parameterType="com.allcore.platform.vo.SampleVO" resultMap="sampleResultMap">
        select sample.id,
        sample.sample_guid,
        sample.sample_name,
        sample.platform_guid,
        sample.status,
        sample.appkey,
        sample.appsecret,
        sample.is_deleted,
        sample.sort,
        sample.create_user,
        sample.create_dept,
        sample.dept_code,
        sample.update_user,
        sample.create_time,
        sample.update_time,info.platform_name from external_sample sample left join external_platform_info info
        on sample.platform_guid = info.platform_guid where sample.is_deleted = 0
        <if test="sample.status != null and sample.status != ''">
            and sample.status = #{sample.status}
        </if>
        <if test="sample.platformGuid != null and sample.platformGuid != ''">
            and info.platform_guid = #{sample.platformGuid}
        </if>
        <if test="sample.sampleName != null and sample.sampleName != ''">
            and sample.sample_name = #{sample.sampleName}
        </if>
        <if test="sample.appkey != null and sample.appkey != ''">
            and sample.appkey = #{sample.appkey}
        </if>
        <if test="sample.appsecret != null and sample.appsecret != ''">
            and sample.appsecret = #{sample.appsecret}
        </if>
    </select>

    <select id="listSample" resultMap="sampleResultMap">
        select sample.sample_name,sample.sample_guid,info.platform_name,info.platform_guid
        from external_sample sample left join external_platform_info info
        on sample.platform_guid = info.platform_guid where sample.is_deleted = 0
    </select>

</mapper>
