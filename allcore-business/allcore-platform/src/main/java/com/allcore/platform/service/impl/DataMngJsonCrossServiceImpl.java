package com.allcore.platform.service.impl;

import com.allcore.platform.entity.DataMngJsonCross;
import com.allcore.platform.vo.DataMngJsonCrossVO;
import com.allcore.platform.mapper.DataMngJsonCrossMapper;
import com.allcore.platform.service.IDataMngJsonCrossService;
import com.allcore.common.base.ZxhcServiceImpl;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 数据处理json交跨点表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
@Service
public class DataMngJsonCrossServiceImpl extends ZxhcServiceImpl<DataMngJsonCrossMapper, DataMngJsonCross> implements IDataMngJsonCrossService {

	@Override
	public IPage<DataMngJsonCrossVO> selectDataMngJsonCrossPage(IPage<DataMngJsonCrossVO> page, DataMngJsonCrossVO dataMngJsonCross) {
		return page.setRecords(baseMapper.selectDataMngJsonCrossPage(page, dataMngJsonCross));
	}

    @Override
    public List<String> getCrossTypeList() {
        return baseMapper.getCrossTypeList();
    }

}
