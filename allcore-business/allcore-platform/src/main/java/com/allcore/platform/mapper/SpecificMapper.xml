<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.platform.mapper.SpecificMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="specificResultMap" type="com.allcore.platform.vo.SpecificVO">
        <result column="id" property="id"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="create_user" property="createUser"/>
        <result column="create_dept" property="createDept"/>
        <result column="update_user" property="updateUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="specific_guid" property="specificGuid"/>
        <result column="sample_guid" property="sampleGuid"/>
        <result column="ywly" property="ywly"/>
        <result column="server_name" property="serverName"/>
        <result column="url_code" property="urlCode"/>
        <result column="request_type" property="requestType"/>
        <result column="prefix_url" property="prefixUrl"/>
        <result column="suffix_url" property="suffixUrl"/>
        <result column="sort" property="sort"/>
        <result column="platform_name" property="platformName"/>
        <result column="sample_name" property="sampleName"/>
    </resultMap>


    <select id="selectSpecificPage" resultMap="specificResultMap">
        select spec.id,
        spec.specific_guid,
        spec.sample_guid,
        spec.ywly,
        spec.server_name,
        spec.url_code,
        spec.request_type,
        spec.host,
        spec.port,
        spec.prefix_url,
        spec.suffix_url,
        spec.status,
        spec.is_deleted,
        spec.sort,
        spec.create_user,
        spec.create_dept,
        spec.dept_code,
        spec.update_user,
        spec.create_time,
        spec.update_time,sample.sample_name,info.platform_name from external_specific spec
        left join external_sample sample on spec.sample_guid = sample.sample_guid
        left join external_platform_info info on sample.platform_guid = info.platform_guid
        where spec.is_deleted = 0
        <if test="specific.platformName != null and specific.platformName != ''">
            and info.platform_name = #{specific.platformName}
        </if>
        <if test="specific.sampleName != null and specific.sampleName != ''">
            and sample.sample_name = #{specific.sampleName}
        </if>
        <if test="specific.serverName != null and specific.serverName != ''">
            and spec.server_name = #{specific.serverName}
        </if>
        <if test="specific.status != null and specific.status != ''">
            and spec.status = #{specific.status}
        </if>
    </select>

</mapper>
