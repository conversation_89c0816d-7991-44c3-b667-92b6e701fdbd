package com.allcore.platform.controller;

import com.allcore.platform.entity.PlatformInfo;
import com.allcore.platform.service.IPlatformInfoService;
import com.allcore.platform.vo.PlatformInfoVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import lombok.AllArgsConstructor;
import javax.validation.Valid;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import org.springframework.web.bind.annotation.*;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.allcore.core.boot.ctrl.AllcoreController;

/**
 * 外部平台集成表 控制器
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/platform")
@Api(value = "外部平台集成表", tags = "外部平台集成表接口")
public class PlatformInfoController extends AllcoreController {

	private final IPlatformInfoService platformInfoService;


	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入platformInfo")
	public R<PlatformInfo> detail(PlatformInfo platformInfo) {
		PlatformInfo detail = platformInfoService.getOne(Condition.getQueryWrapper(platformInfo));
		return R.data(detail);
	}


	/**
	 * 自定义分页 外部平台集成表
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入platformInfo")
	public R<IPage<PlatformInfoVO>> page(PlatformInfoVO platformInfo, Query query) {
		IPage<PlatformInfoVO> pages = platformInfoService.selectPlatformInfoPage(Condition.getPage(query), platformInfo);
		return R.data(pages);
	}

	/**
	 * 新增 外部平台集成表
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入platformInfo")
	public R save(@Valid @RequestBody PlatformInfo platformInfo) {
		platformInfo.setPlatformGuid( CommonUtil.generateUuid());
		return R.status(platformInfoService.save(platformInfo));
	}

	/**
	 * 修改 外部平台集成表
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入platformInfo")
	public R update(@Valid @RequestBody PlatformInfo platformInfo) {
		return R.status(platformInfoService.update(platformInfo,new QueryWrapper<PlatformInfo>().lambda().eq(PlatformInfo::getPlatformGuid,platformInfo.getPlatformGuid())));
	}

	/**
	 * 新增或修改 外部平台集成表
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入platformInfo")
	public R submit(@Valid @RequestBody PlatformInfo platformInfo) {
		return R.status(platformInfoService.saveOrUpdate(platformInfo));
	}


	/**
	 * 删除 外部平台集成表
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入platformGuid")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestBody PlatformInfo platformInfo) {
		platformInfoService.removeChild(platformInfo.getPlatformGuid());
		return R.status(platformInfoService.remove(new QueryWrapper<PlatformInfo>().lambda().eq(PlatformInfo::getPlatformGuid, platformInfo.getPlatformGuid())));
	}

}
