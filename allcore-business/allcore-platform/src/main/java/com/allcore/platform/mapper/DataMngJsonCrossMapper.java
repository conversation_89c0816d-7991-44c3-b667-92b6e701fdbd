package com.allcore.platform.mapper;

import com.allcore.platform.entity.DataMngJsonCross;
import com.allcore.platform.vo.DataMngJsonCrossVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.List;

/**
 * 数据处理json交跨点表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-10-10
 */
public interface DataMngJsonCrossMapper extends BaseMapper<DataMngJsonCross> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param dataMngJsonCross
	 * @return
	 */
	List<DataMngJsonCrossVO> selectDataMngJsonCrossPage(IPage page, DataMngJsonCrossVO dataMngJsonCross);

    List<String> getCrossTypeList();
}
