package com.allcore.platform.controller;

import com.allcore.platform.service.RemoteService;
import com.allcore.platform.service.ISpecificService;
import com.allcore.platform.entity.Specific;
import com.allcore.platform.vo.SpecificVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import com.allcore.common.constant.Constant;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.tool.api.R;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

/**
 * 平台模板基础信息 控制器
 *
 * <AUTHOR>
 * @since 2022-04-02
 */
@RestController
@AllArgsConstructor
@RequestMapping("/specific")
@Api(value = "平台模板基础信息", tags = "平台模板基础信息接口")
public class SpecificController extends AllcoreController {

	private final ISpecificService specificService;

	@Autowired
	private RemoteService remoteService;

	/**
	 * 详情
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入specific")
	public R<Specific> detail(Specific specific) {
		Specific detail = specificService.getOne(Condition.getQueryWrapper(specific));
		return R.data(detail);
	}

	/**
	 * 分页 平台模板基础信息
	 */
	@GetMapping("/list")
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "分页", notes = "传入specific")
	public R<IPage<Specific>> list(Specific specific, Query query) {
		IPage<Specific> pages = specificService.page(Condition.getPage(query), Condition.getQueryWrapper(specific));
		return R.data(pages);
	}

	/**
	 * 自定义分页 平台模板基础信息
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "分页", notes = "传入specific")
	public R<IPage<SpecificVO>> page(SpecificVO specific, Query query) {
		IPage<SpecificVO> pages = specificService.selectSpecificPage(Condition.getPage(query), specific);
		return R.data(pages);
	}

	/**
	 * 新增 平台模板基础信息
	 */
	@PostMapping("/save")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "新增", notes = "传入specific")
	public R save(@Valid @RequestBody Specific specific) {
		specific.setSpecificGuid( CommonUtil.generateUuid());
		boolean reachable = remoteService.isHostReachable(specific.getHost(), Constant.TIME_OUT);
		if (reachable){
			specific.setStatus(Constant.SUCCESS_STATUS);
		}else {
			specific.setStatus(Constant.FAILED__STATUS);
		}
		return R.status(specificService.save(specific));
	}

	/**
	 * 修改 平台模板基础信息
	 */
	@PostMapping("/update")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "修改", notes = "传入specific")
	public R update(@Valid @RequestBody Specific specific) {
		return R.status(specificService.update(specific,new QueryWrapper<Specific>().lambda().eq(Specific::getSpecificGuid,specific.getSpecificGuid())));
	}

	/**
	 * 新增或修改 平台模板基础信息
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入specific")
	public R submit(@Valid @RequestBody Specific specific) {
		return R.status(specificService.saveOrUpdate(specific));
	}


	/**
	 * 删除 平台模板基础信息
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "逻辑删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestBody Specific specific) {
		return R.status(specificService.remove(new QueryWrapper<Specific>().lambda().eq(Specific::getSpecificGuid, specific.getSpecificGuid())));
	}


}
