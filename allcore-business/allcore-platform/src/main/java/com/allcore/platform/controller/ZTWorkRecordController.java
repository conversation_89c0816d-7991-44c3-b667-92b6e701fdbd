package com.allcore.platform.controller;

import com.alibaba.fastjson.JSON;
import com.allcore.platform.service.ZtQueryLogService;
import com.allcore.platform.dto.WorkOrderResultZtDTO;
import com.allcore.platform.dto.WorkOrderZtDTO;
import com.allcore.platform.dto.WorkRecordRequest;
import com.allcore.platform.entity.TbQueryZtLog;
import com.allcore.platform.param.filters;
import com.allcore.platform.param.queryvo;
import com.allcore.platform.vo.PartrolRecordVo;
import com.allcore.platform.vo.PatrolPerson;
import com.allcore.platform.vo.ZhongTaiparamVo;
import com.allcore.platform.util.ZtHttpUtils;
import com.allcore.platform.util.ZtUtil;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import com.allcore.common.config.ZtConfig;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/externaldata/ztrecord")
@Slf4j
@Api(value = "中台-巡视记录", tags = "中台-巡视记录 4个")
public class ZTWorkRecordController {

	// 39+4
    @Resource
    private ZtQueryLogService ztQueryLog;


    /**
	 * （重庆中台21-18 巡视记录查看）
	 * <AUTHOR>
	 * @date 2022/10/10 10:53
	 * @param msg
	 * @return void
	 */
    @RequestMapping("/workRecordQuery")
    public void workRecordQuery(String msg){
            ZhongTaiparamVo headerparam = new ZhongTaiparamVo();
            headerparam.set_api_name("WMCenter.patrol.recodeQuery");
            headerparam.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.workRecordQuery);
            headerparam.setClient(ZtConfig.CLIENT);
            headerparam.setSignature(ZtConfig.SIGNATURE);
            headerparam.setToken( ZtUtil.getToken());
            headerparam.set_api_version("1.0.0");
            headerparam.setDate(System.currentTimeMillis() + "");

            queryvo params = new queryvo("SGSubstationmajor","123456789","02");
            String result = ZtHttpUtils.httpPostSend(headerparam, params);
            TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(params),result, ZtConfig.workRecordQuery,4);
            ztQueryLog.insertZtLog(tbQueryZtLog);
    }

    /**
	 * （重庆中台21-17 巡视记录查询）
	 * <AUTHOR>
	 * @date 2022/10/10 10:53
	 * @param msg
	 * @return void
	 */
    @RequestMapping("/workRecordList")
    public void workRecordList(String msg) {
            ZhongTaiparamVo headerparam = new ZhongTaiparamVo();
            headerparam.set_api_name("WMCenter.patrol.recordList");
            headerparam.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.workRecordList);
            headerparam.setClient(ZtConfig.CLIENT);
            headerparam.setSignature(ZtConfig.SIGNATURE);
            headerparam.setToken( ZtUtil.getToken());
            headerparam.set_api_version("1.0.0");
            headerparam.setDate(System.currentTimeMillis() + "");

            Map<String,Object> map = new HashMap<>();
            filters filters = new filters("02","02","变电运维一班","");
            map.put("filter", filters);
            map.put("sorter", "");
            map.put("pageIndex", 1);
            map.put("pageSize", 10);
            String result = ZtHttpUtils.httpPostSend(headerparam, map);
            TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(map),result, ZtConfig.workRecordList,4);
            ztQueryLog.insertZtLog(tbQueryZtLog);
    }

    /**
	 * （重庆中台21-15 巡视记录创建）
	 * <AUTHOR>
	 * @date 2022/10/10 10:51
	 * @param workRecordRequest
	 * @return void
	 */
    @PostMapping("/workRecordCreate")
    public void workRecordCreate(@RequestBody WorkRecordRequest workRecordRequest) {

			WorkOrderResultZtDTO workOrderResultZtDTO = workRecordRequest.getWorkOrderResultZtDTO();
			WorkOrderZtDTO workOrderZtDTO = workRecordRequest.getWorkOrderZtDTO();
            ZhongTaiparamVo param = new ZhongTaiparamVo();
            param.set_api_name("WMCenter.patrol.recordCreate");
            param.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.workRecordCreate);
            param.setClient(ZtConfig.CLIENT);
            param.setSignature(ZtConfig.SIGNATURE);
            param.setToken( ZtUtil.getToken());
            param.set_api_version("1.0.0");
            param.setDate(System.currentTimeMillis() + "");

            System.out.println("workRecordCreate > user---" +
				"------------------------------>" + workOrderResultZtDTO.getWorkDirectorGuid());

            PartrolRecordVo partrolRecordVo = new PartrolRecordVo();
            partrolRecordVo.setWorkOrder(workOrderResultZtDTO.getInspectGuid());
            partrolRecordVo.setContainerName(workOrderResultZtDTO.getLineName());
            partrolRecordVo.setContainer(workOrderResultZtDTO.getLineReserve2());
            partrolRecordVo.setContainerType("02");
            partrolRecordVo.setPatrolResult("02");
            partrolRecordVo.setIsCompleted("02");
            partrolRecordVo.setRelateBusiId(workOrderResultZtDTO.getInspectGuid());
            partrolRecordVo.setNoFinishReason(workOrderZtDTO.getInspectReserve4());
            List<PatrolPerson> personList = new ArrayList<>();
            PatrolPerson p = new PatrolPerson();
            p.setWorkStaffId(workOrderZtDTO.getOperatorGuid());
            p.setWorkStaffName(workOrderZtDTO.getOperatorName());
            personList.add(p);
            partrolRecordVo.setPatrolPerson(personList);
            partrolRecordVo.setStartedDateTime(workOrderZtDTO.getInspectStartTime());
            partrolRecordVo.setCompletedDateTime(workOrderZtDTO.getInspectEndTime());
			partrolRecordVo.setWorkCrewId(workOrderZtDTO.getThirdGuid());
			partrolRecordVo.setWorkCrewName(workOrderZtDTO.getThirdName());
			partrolRecordVo.setMaintcrewId(workOrderZtDTO.getThirdGuid());
			partrolRecordVo.setMaintcrewName(workOrderZtDTO.getThirdName());
			partrolRecordVo.setMaintainerId(workOrderZtDTO.getFirstGuid());
			partrolRecordVo.setMaintainerName(workOrderZtDTO.getFirstName());
			partrolRecordVo.setCityOrgId(workOrderZtDTO.getCityCode());
            partrolRecordVo.setCityOrgName(workOrderZtDTO.getCityName());
            partrolRecordVo.setCreaterId(workOrderZtDTO.getCreateUser());
			partrolRecordVo.setCreaterName(workOrderZtDTO.getUserName());

			partrolRecordVo.setRemark(workOrderZtDTO.getRemark());
            partrolRecordVo.setWeather("06");

            partrolRecordVo.setSystemSourceId("01");
            partrolRecordVo.setSystemSource("01");
            partrolRecordVo.setDataSource("01");
            partrolRecordVo.setRelateBusiId("02");
            partrolRecordVo.setRelateBusiType("02");
            partrolRecordVo.setLineName(workOrderResultZtDTO.getLineName());
            partrolRecordVo.setLineId(workOrderResultZtDTO.getLineReserve2());
            partrolRecordVo.setPatrolType("NormalPatrol");

            partrolRecordVo.setPatrolMode("13");
            partrolRecordVo.setContainerVoltageLevel(workOrderResultZtDTO.getLineVoltageLevel());

            partrolRecordVo.setPatrolLen(workOrderResultZtDTO.getKilometers().toString());
            partrolRecordVo.setTowerCount(workOrderResultZtDTO.getTowerCount().toString());
            partrolRecordVo.setPatrolRange(workOrderZtDTO.getAuditAirspace());

            Map<String, Object> map1 = new HashMap<>();

            List<PartrolRecordVo> list = new ArrayList<>();
            list.add(partrolRecordVo);
            map1.put("patrolRecordTsListTs", list);

            String rls = ZtHttpUtils.httpPostSend(param, map1);

            log.info("------------巡视记录编制成功 param:"+param.toString());
            TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(map1).substring(0,200),rls, ZtConfig.workRecordCreate,4);
            ztQueryLog.insertZtLog(tbQueryZtLog);
			//TODO 回调更新  WorkRecordObjId

//            try{
//                JSONObject obj = JSONObject.parseObject(rls);
//                TbFlyInspectioninfoWithBLOBs tbFlyInspectioninfoWithBLOBs = new TbFlyInspectioninfoWithBLOBs();
//                tbFlyInspectioninfoWithBLOBs.setInspecid(inspectioninfo.getInspecid());
//                if ("000000".equals(obj.getString("status"))) {
//                    try {
//                        String objId = obj.getJSONObject("result").getJSONArray("objId").getString(0);
//                        tbFlyInspectioninfoWithBLOBs.setWorkRecordObjId(objId);
//                    } catch (Exception e) {
//                        tbFlyInspectioninfoWithBLOBs.setWorkRecordObjId("22F44990-050C-47AB-B0B4-35FEB5444A62-83386");
//                    }
//                } else {
//                    tbFlyInspectioninfoWithBLOBs.setWorkRecordObjId("22F44990-050C-47AB-B0B4-35FEB5444A62-83386");
//                }
//                tbFlyInspectioninfoMapper.updateByPrimaryKeySelective(tbFlyInspectioninfoWithBLOBs);
//            }catch (Exception e) {
//                e.printStackTrace();
//            }
    }

    /**
	 * （重庆中台21-16 巡视记录变更）
	 * <AUTHOR>
	 * @date 2022/10/10 10:52
	 * @param workRecordRequest
	 * @return void
	 */
    @PostMapping("/workRecordUpdate")
    public void workRecordUpdate(@RequestBody WorkRecordRequest workRecordRequest) {
            WorkOrderResultZtDTO workOrderResultZtDTO = workRecordRequest.getWorkOrderResultZtDTO();
            WorkOrderZtDTO workOrderZtDTO = workRecordRequest.getWorkOrderZtDTO();
            ZhongTaiparamVo param = new ZhongTaiparamVo();
            param.set_api_name("WMCenter.patrol.recordUpdate");
            param.setUrl(ZtConfig.ZhongTaiUrl + ZtConfig.workRecordUpdate);
            param.setClient(ZtConfig.CLIENT);
            param.setSignature(ZtConfig.SIGNATURE);
            param.setToken( ZtUtil.getToken());
            param.set_api_version("1.0.0");
            param.setDate(System.currentTimeMillis() + "");

            PartrolRecordVo partrolRecordVo = new PartrolRecordVo();

            partrolRecordVo.setObjId(null==workOrderZtDTO.getZtObjId()?"22F44990-050C-47AB-B0B4-35FEB5444A62-83386":workOrderZtDTO.getZtObjId());
            partrolRecordVo.setWorkOrder(workOrderResultZtDTO.getInspectGuid());
            partrolRecordVo.setPatrolPlanId("getAnnualguid()");
            partrolRecordVo.setContainerName(workOrderResultZtDTO.getLineName());
            partrolRecordVo.setContainer(workOrderResultZtDTO.getLineReserve2());
            List<PatrolPerson> personList = new ArrayList<>();
            PatrolPerson p = new PatrolPerson();
            p.setObjId(null==workOrderZtDTO.getZtObjId()?"22F44990-050C-47AB-B0B4-35FEB5444A62-83386":workOrderZtDTO.getZtObjId());
            p.setWorkStaffId(workOrderZtDTO.getOperatorGuid());
            p.setWorkStaffName(workOrderZtDTO.getOperatorName());
            personList.add(p);

            partrolRecordVo.setPatrolPerson(personList);

            partrolRecordVo.setStartedDateTime(workOrderZtDTO.getInspectStartTime());
            partrolRecordVo.setCompletedDateTime(workOrderZtDTO.getInspectEndTime());
            partrolRecordVo.setWorkCrewId(workOrderZtDTO.getThirdGuid());
            partrolRecordVo.setWorkCrewName(workOrderZtDTO.getThirdName());
            partrolRecordVo.setMaintcrewId(workOrderZtDTO.getThirdGuid());
            partrolRecordVo.setMaintcrewName(workOrderZtDTO.getThirdName());
            partrolRecordVo.setMaintainerId(workOrderZtDTO.getFirstGuid());
            partrolRecordVo.setMaintainerName(workOrderZtDTO.getFirstName());
			partrolRecordVo.setCityOrgId(workOrderZtDTO.getCityCode());
			partrolRecordVo.setCityOrgName(workOrderZtDTO.getCityName());
            partrolRecordVo.setCreaterId(workOrderZtDTO.getCreateUser());
            partrolRecordVo.setEditorId(workOrderZtDTO.getCreateUser());

            partrolRecordVo.setCreaterName(workOrderZtDTO.getUserName());
            partrolRecordVo.setEditorName(workOrderZtDTO.getUserName());
            partrolRecordVo.setRemark(workOrderZtDTO.getRemark());
            partrolRecordVo.setWeather("06");
            partrolRecordVo.setSystemSourceId("01");
            partrolRecordVo.setSystemSource("01");
            partrolRecordVo.setDataSource("01");
            partrolRecordVo.setRelateBusiId("02");
            partrolRecordVo.setRelateBusiType("02");
            partrolRecordVo.setLineName(workOrderResultZtDTO.getLineName());
            partrolRecordVo.setLineId(workOrderResultZtDTO.getLineGuid());
            partrolRecordVo.setPatrolType("NormalPatrol");

            partrolRecordVo.setPatrolMode("13");
            partrolRecordVo.setContainerVoltageLevel(workOrderResultZtDTO.getLineVoltageLevel());
            partrolRecordVo.setPatrolLen(workOrderResultZtDTO.getKilometers().toString());
            partrolRecordVo.setTowerCount(workOrderResultZtDTO.getTowerCount().toString());

            Map<String, Object> map1 = new HashMap<>();
            map1.put("operateMode", "update");
            //map.put("objid", inspectioninfo.getWorkRecordObjId());
            List list = new ArrayList();
            list.add(partrolRecordVo);
            map1.put("patrolRecordTsListTs", list);
            log.info("------------巡视记录更新-----:"+param.toString());
            String rls = ZtHttpUtils.httpPostSend(param, map1);

            log.info("------------巡视记录更新 成功-----:"+rls);
            TbQueryZtLog tbQueryZtLog = new TbQueryZtLog(JSON.toJSONString(map1).substring(0,200),rls, ZtConfig.workRecordUpdate,4);
            ztQueryLog.insertZtLog(tbQueryZtLog);
    }

}
