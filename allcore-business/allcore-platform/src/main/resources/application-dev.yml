#服务器端口
server:
  port: 18090

spring:
  #排除DruidDataSourceAutoConfigure
  autoconfigure:
    exclude: com.alibaba.druid.spring.boot.autoconfigure.DruidDataSourceAutoConfigure
  datasource:
    dynamic:
      druid:
        #通用校验配置
        validation-query: select 1
        #启用sql日志拦截器
        proxy-filters:
          - sqlLogInterceptor
      #设置默认的数据源或者数据源组,默认值即为master
      primary: master
      datasource:
        master:
          druid:
            #独立校验配置
            validation-query: select 1
            #oracle校验
            #validation-query: select 1 from dual
          url: ${platform.master.url}
          username: ${platform.master.username}
          password: ${platform.master.password}
        slave:
          druid:
            #独立校验配置
            validation-query: select 1
            #oracle校验
            #validation-query: select 1 from dual
          url: ${platform.slave.url}
          username: ${platform.slave.username}
          password: ${platform.slave.password}
  rabbitmq:
    host: ${platform.rabbitmq.host}
    port: ${platform.rabbitmq.port}
    username: ${platform.rabbitmq.username}
    password: ${platform.rabbitmq.password}
    virtual-host: /
