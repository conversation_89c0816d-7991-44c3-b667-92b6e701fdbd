package com.allcore.netty.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.filesystem.feign.IOssClient;
import com.allcore.filesystem.vo.AllcoreFileVO;
import com.allcore.netty.dto.ModelForSaveDTO;
import com.allcore.netty.dto.ModelForUpdateDTO;
import com.allcore.netty.entity.*;
import com.allcore.netty.mapper.*;
import com.allcore.netty.service.ModelService;
import com.allcore.netty.vo.ModelListVO;
import com.allcore.netty.vo.ModelVO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@AllArgsConstructor
public class ModelServiceImpl extends ZxhcServiceImpl<ModelMapper, ModelEntity> implements ModelService {

	private UavMapper uavMapper;

	private BatteryMapper batteryMapper;

	private SpareMapper spareMapper;

	private EquipmentCabinetMapper equipmentCabinetMapper;

	private MainControlCabinetMapper mainControlCabinetMapper;

	private IOssClient ossClient;

	@Override
	public R<ModelVO> saveInfo(ModelForSaveDTO dto) {
		List<ModelEntity> checkList = baseMapper.selectList(new LambdaQueryWrapper<ModelEntity>()
							.eq(ModelEntity::getName, dto.getName()).eq(ModelEntity::getType, dto.getType()));
		if(CollectionUtil.isNotEmpty(checkList)){
			return R.fail("有重名，不可新增！");
		}
		List<ModelEntity> modelEntityList = baseMapper.selectList(new LambdaQueryWrapper<ModelEntity>()
							.eq(ModelEntity::getType,dto.getType()).orderByDesc(ModelEntity::getSort));
		ModelEntity entity = BeanUtil.copy(dto, ModelEntity.class);
		String leftCode = addZeroForStr(dto.getType(),2,2);
		if(CollectionUtil.isNotEmpty(modelEntityList)){
			int sort = modelEntityList.get(0).getSort() + 1;
			entity.setSort(sort);
			String code = leftCode.concat(addZeroForStr(String.valueOf(sort),4, 1));
			entity.setCode(code);
		}else{
			String code = leftCode.concat(addZeroForStr(StringPool.ONE, 4, 1));
			entity.setCode(code);
			entity.setSort(1);
		}
		ModelVO vo = new ModelVO();
		if(save(entity)){
			vo.setId(entity.getId());
			vo.setCode(entity.getCode());
			vo.setName(entity.getName());
		}
		return R.data(vo);
	}

	@Override
	public R<ModelVO> updateInfo(ModelForUpdateDTO dto) {
		List<ModelEntity> checkList = baseMapper.selectList(new LambdaQueryWrapper<ModelEntity>()
			.eq(ModelEntity::getName, dto.getName()).eq(ModelEntity::getType, dto.getType())
			.ne(ModelEntity::getId, dto.getId()));
		if(CollectionUtil.isNotEmpty(checkList)){
			return R.fail("有重名，不可修改！");
		}
		ModelEntity entity = baseMapper.selectById(dto.getId());
		entity.setName(dto.getName());
		if(StringUtil.hasText(dto.getFileGuid())){
			entity.setFileGuid(dto.getFileGuid());
		}
		ModelVO vo = new ModelVO();
		if(updateById(entity)){
			vo.setId(entity.getId());
			vo.setCode(entity.getCode());
			vo.setName(entity.getName());
		}
		return R.data(vo);
	}

	@Override
	public List<ModelVO> getDropDownList(String type) {
		List<ModelEntity> modelEntityList = baseMapper.selectList(new LambdaQueryWrapper<ModelEntity>()
			.eq(ModelEntity::getType,type).orderByDesc(ModelEntity::getSort));
		List<ModelVO> voList = BeanUtil.copy(modelEntityList, ModelVO.class);
		return voList;
	}

	@Override
	public R delete(String id, String type) {
		ModelEntity modelEntity = baseMapper.selectById(id);
		if(MainBizEnum.DEVICE_TYPE_1.getCode().equals(type)){
			Long count = uavMapper.selectCount(new LambdaQueryWrapper<UavEntity>()
				.eq(UavEntity::getUavModel, modelEntity.getCode()));
			if(count > 0){
				return R.fail("该型号已被使用，不可删除");
			}
		}else if(MainBizEnum.DEVICE_TYPE_2.getCode().equals(type)){
			Long count = batteryMapper.selectCount(new LambdaQueryWrapper<BatteryEntity>()
				.eq(BatteryEntity::getBatteryModel, modelEntity.getCode()));
			if(count > 0){
				return R.fail("该型号已被使用，不可删除");
			}
		}else if(MainBizEnum.DEVICE_TYPE_3.getCode().equals(type)){
			Long count = spareMapper.selectCount(new LambdaQueryWrapper<SpareEntity>()
				.eq(SpareEntity::getSpareModel, modelEntity.getCode()));
			if(count > 0){
				return R.fail("该型号已被使用，不可删除");
			}
		}else if(MainBizEnum.DEVICE_TYPE_4.getCode().equals(type)){
			Long count1 = mainControlCabinetMapper.selectCount(new LambdaQueryWrapper<MainControlCabinetEntity>()
				.eq(MainControlCabinetEntity::getCabinetModel, modelEntity.getCode()));
			Long count2 = equipmentCabinetMapper.selectCount(new LambdaQueryWrapper<EquipmentCabinetEntity>()
				.eq(EquipmentCabinetEntity::getCabinetModel, modelEntity.getCode())
				.eq(EquipmentCabinetEntity::getCabinetType, MainBizEnum.DEVICE_TYPE_4.getCode()));
			if(count1 > 0 || count2 > 0){
				return R.fail("该型号已被使用，不可删除");
			}
		}else if(MainBizEnum.DEVICE_TYPE_5.getCode().equals(type)){
			Long count = equipmentCabinetMapper.selectCount(new LambdaQueryWrapper<EquipmentCabinetEntity>()
				.eq(EquipmentCabinetEntity::getCabinetModel, modelEntity.getCode())
				.eq(EquipmentCabinetEntity::getCabinetType, MainBizEnum.DEVICE_TYPE_5.getCode()));
			if(count > 0){
				return R.fail("该型号已被使用，不可删除");
			}
		}else if(MainBizEnum.DEVICE_TYPE_6.getCode().equals(type)){
			Long count = equipmentCabinetMapper.selectCount(new LambdaQueryWrapper<EquipmentCabinetEntity>()
				.eq(EquipmentCabinetEntity::getCabinetModel, modelEntity.getCode())
				.eq(EquipmentCabinetEntity::getCabinetType, MainBizEnum.DEVICE_TYPE_6.getCode()));
			if(count > 0){
				return R.fail("该型号已被使用，不可删除");
			}
		}else if(MainBizEnum.DEVICE_TYPE_99.getCode().equals(type)){
			Long uavCount = uavMapper.selectCount(new LambdaQueryWrapper<UavEntity>()
				.eq(UavEntity::getFactoryCode, modelEntity.getCode()));
			Long batteryCount = batteryMapper.selectCount(new LambdaQueryWrapper<BatteryEntity>()
				.eq(BatteryEntity::getFactoryCode, modelEntity.getCode()));
			Long spareCount = spareMapper.selectCount(new LambdaQueryWrapper<SpareEntity>()
				.eq(SpareEntity::getFactoryCode, modelEntity.getCode()));
			Long mainControlCabinetCount = mainControlCabinetMapper.selectCount(new LambdaQueryWrapper<MainControlCabinetEntity>()
				.eq(MainControlCabinetEntity::getFactory, modelEntity.getCode()));
			Long equipmentCabinetCount = equipmentCabinetMapper.selectCount(new LambdaQueryWrapper<EquipmentCabinetEntity>()
				.eq(EquipmentCabinetEntity::getFactory, modelEntity.getCode()));
			if(uavCount > 0 || batteryCount > 0 || spareCount > 0 || mainControlCabinetCount > 0 || equipmentCabinetCount > 0){
				return R.fail("该厂家已被使用，不可删除");
			}
		}

		return R.status(removeById(id));
	}

	@Override
	public List<ModelListVO> getModelList(String type) {
		List<ModelEntity> modelEntityList = baseMapper.selectList(new LambdaQueryWrapper<ModelEntity>()
			.eq(ModelEntity::getType,type).orderByDesc(ModelEntity::getSort));
		List<ModelListVO> voList = BeanUtil.copy(modelEntityList, ModelListVO.class);
		voList.stream().forEach(e->{
			if(StringUtil.hasText(e.getFileGuid())){
				R<AllcoreFileVO> result = ossClient.getFileDetail(e.getFileGuid());
				if (result.isSuccess()){
					AllcoreFileVO file = result.getData();
					e.setPicUrl(file.getStaticPath());
				}
			}
		});
		return voList;
	}


	/**
	 * 给字符串的左补0或右补0
	 * @param str  要处理的字符串
	 * @param length 补0后字符串总长度
	 * @param type  1-左补0  2-右补0
	 * @return
	 */
	private static String addZeroForStr(String str, int length,int type) {
		int strLen = str.length();
		if (strLen < length) {
			while (strLen < length) {
				StringBuffer sb = new StringBuffer();
				if (type == 1) {
					// 左补0
					sb.append("0").append(str);
				} else if (type == 2) {
					//右补0
					sb.append(str).append("0");
				}
				str = sb.toString();
				strLen = str.length();
			}
		}
		return str;
	}

}
