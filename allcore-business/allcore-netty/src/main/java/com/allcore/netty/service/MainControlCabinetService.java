package com.allcore.netty.service;

import com.allcore.common.base.ZxhcService;
import com.allcore.netty.entity.MainControlCabinetEntity;
import com.allcore.netty.vo.ControlCabinetVO;

import java.util.List;

/**
 * 主控柜表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-22 10:21:29
 */
public interface MainControlCabinetService extends ZxhcService<MainControlCabinetEntity> {

	/**
	 * 根据主控柜guid查询主控柜
	 *
	 * @param guidList
	 * @return
	 */
	List<ControlCabinetVO> selectByGuidList(List<String> guidList);


	/**
	 * 根据主控柜guid查询主控柜
	 *
	 * @param guid
	 * @return
	 */
	ControlCabinetVO selectByGuid(String guid);

}

