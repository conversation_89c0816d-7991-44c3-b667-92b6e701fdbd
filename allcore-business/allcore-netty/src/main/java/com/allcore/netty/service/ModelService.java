package com.allcore.netty.service;

import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.allcore.netty.dto.ModelForSaveDTO;
import com.allcore.netty.dto.ModelForUpdateDTO;
import com.allcore.netty.entity.ModelEntity;
import com.allcore.netty.vo.ModelListVO;
import com.allcore.netty.vo.ModelVO;

import java.util.List;

/**
 * 型号表
 *
 * <AUTHOR>
 * @date 2022-11-30 15:17:49
 */
public interface ModelService extends ZxhcService<ModelEntity> {

	/**
	 * 型号保存
	 * @param dto
	 * @return
	 */
	R<ModelVO> saveInfo(ModelForSaveDTO dto);

	/**
	 * 型号修改
	 * @param dto
	 * @return
	 */
	R<ModelVO> updateInfo(ModelForUpdateDTO dto);

	/**
	 * 根据类型查询下拉
	 * @param type
	 * @return
	 */
	List<ModelVO> getDropDownList(String type);

	/**
	 * 删除
	 * @param id
	 * @param type
	 * @return
	 */
    R delete(String id, String type);

	/**
	 * 根据类型查询列表
	 * @param type
	 * @return
	 */
	List<ModelListVO> getModelList(String type);
}
