package com.allcore.netty.wrapper;

import cn.hutool.core.bean.BeanUtil;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.netty.entity.WarningEntity;
import com.allcore.netty.vo.WarningVO;
import com.allcore.user.cache.UserCache;

/**
 * @Author: Jiy
 * @Date: 2022-11-25 14:39
 */
public class WarningWrapper extends BaseEntityWrapper<WarningEntity, WarningVO> {

	public static WarningWrapper build() {
		return new WarningWrapper();
	}

	@Override
	public WarningVO entityVO(WarningEntity entity) {
		WarningVO warningVO = BeanUtil.copyProperties(entity, WarningVO.class);
		// 发送人姓名
		warningVO.setSendName(UserCache.getUser(warningVO.getCreateUser()).getName());
		// 阅读状态
		warningVO.setWarnStatusName(DictBizCache.getValue(MainBizEnum.READ_STATE.getCode(), warningVO.getWarnStatus()));
		// 消息状态
		warningVO.setWarnTypeName(DictBizCache.getValue(MainBizEnum.WARN_STATE.getCode(), warningVO.getWarnType()));
		return warningVO;
	}
}
