package com.allcore.netty.handler;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.nacos.shaded.io.grpc.netty.shaded.io.netty.handler.codec.http.websocketx.TextWebSocketFrame;
import com.allcore.netty.enums.WebSocketHandlerTypeEnum;
import com.allcore.netty.components.WebSocketServiceFactory;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;

import javax.annotation.PostConstruct;

/**
 * <AUTHOR>
 * @date 2021-10-25 15:19
 */
@Slf4j
public class WebWarehouseHandler extends WebHandlerAbstract {

//    @Autowired
//    private IRkxhWarehouseService rkxhWarehouseService;
//    @Autowired
//    private ISysBaseAPI sysBaseAPI;

    @Override
    @PostConstruct
    public void init() {
        WebSocketServiceFactory.seviceMap.put(WebSocketHandlerTypeEnum.WAREHOUSE.getId(), this);
    }

    @Override
    public void handler(Channel ch, Object msg) {
        String[] positions = msg.toString().split(",");
        log.info("接收到前端发来的解锁请求:positions={}", positions);
        //Result result = rkxhWarehouseService.unlock(positions);
       // if (!result.getCode().equals(CommonConstant.SC_OK_200)) {
            //sysBaseAPI.addLog("【机柜货位】发起解锁电池指令失败：" + result.getMessage(), LogTypeEnum.LogType.LOG_TYPE_WAREHOUSE, LogHandleTypeEnum.LogHandleType.LOG_HANDLE_TYPE_UNLOCK);
      //  }
       // this.send(ch, result);
    }

    @Override
    public void send(Channel ch, Object obj) {
        // 返回处理结果
        String jsonString = JSONObject.toJSONString(obj);
        ch.writeAndFlush(new TextWebSocketFrame(jsonString));
    }
}
