package com.allcore.netty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allcore.auth.feign.AllcoreAuthClient;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.BasicConstant;
import com.allcore.common.enums.BatteryModelEnum;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.secure.AllcoreUser;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.dict.entity.DictBiz;
import com.allcore.netty.dto.*;
import com.allcore.netty.entity.*;
import com.allcore.netty.feign.INettyBusinessClient;
import com.allcore.netty.mapper.*;
import com.allcore.netty.service.*;
import com.allcore.netty.utils.HttpUrlConnectionToInterface;
import com.allcore.netty.utils.NacosConfig;
import com.allcore.netty.utils.SpringContextUtil;
import com.allcore.netty.vo.*;
import com.allcore.netty.wrapper.BatteryWrapper;
import com.allcore.netty.wrapper.CabinetWrapper;
import com.allcore.netty.wrapper.CellWrapper;
import com.allcore.netty.wrapper.FloorWrapper;
import com.allcore.netty.zk.jni.JavaToBiokey;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import com.allcore.user.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;


@Service
@Slf4j
public class EquipmentCabinetServiceImpl extends ZxhcServiceImpl<EquipmentCabinetMapper, EquipmentCabinetEntity> implements EquipmentCabinetService {

	@Autowired
	private MainControlCabinetService mainControlCabinetService;

	@Autowired
	private WarnDeviceService warnDeviceService;

	@Autowired
	private EquipmentCabinetFloorMapper equipmentCabinetFloorMapper;

	@Autowired
	private EquipmentCabinetMapper equipmentCabinetMapper;

	@Autowired
	private DeviceAuthInfoMapper deviceAuthInfoMapper;

	@Resource
	private ISysClient sysClient;

	@Resource
	private ModelService modelService;

	@Autowired
	private EquipmentCabinetCellMapper cellMapper;

	@Autowired
	private EquipmentCabinetFloorService floorService;

	@Autowired
	private BatteryMapper batteryMapper;

	@Autowired
	private ReceiveLogMapper receiveLogMapper;

	@Autowired
	private AllcoreRedis allcoreRedis;

	@Resource
	private AllcoreAuthClient authClient;

	@Resource
	private UavMapper uavMapper;

	@Resource
	private SpareMapper spareMapper;

	@Resource
	private INettyBusinessClient nettyBuinessClient;

	@Resource
	private ApplicationContext applicationContext;

	@Resource
	private WsService wsService;

	@Resource
	private MaintanceLogMapper maintanceLogMapper;

	@Resource
	@Lazy
	private StoreRoomService storeRoomService;

	@Resource
	private LogService logService;

	@Resource
	private VisitInfoService visitInfoService;

	@Resource
	private RfidBindService rfidBindService;

	/**
	 * 新增机柜信息
	 *
	 * @param cabinetDTO
	 * @return
	 */
	@Override
	@Transactional(rollbackFor = Exception.class)
	public CabinetVO add(CabinetDTO cabinetDTO) {
		if (StrUtil.isEmpty(cabinetDTO.getCabinetType())) {
			throw new ServiceException("设备类型不能为空");
		}
		if (StrUtil.isEmpty(cabinetDTO.getSn())) {
			throw new ServiceException("SN码不能为空");
		}
		if (StrUtil.isEmpty(cabinetDTO.getStoreRoomGuid())) {
			cabinetDTO.setStoreRoomGuid(null);
		}
		cabinetDTO.setEquipmentCabinetGuid(IdUtil.simpleUUID());
		EquipmentCabinetEntity equipmentCabinetEntity = BeanUtil.copyProperties(cabinetDTO, EquipmentCabinetEntity.class);
		equipmentCabinetEntity.setDeptCode(AuthUtil.getDeptCode());
		//校验SN码是否已存在
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(EquipmentCabinetEntity::getSn, cabinetDTO.getSn());
		Long num = getBaseMapper().selectCount(queryWrapper);
		if (num != null && num > 0) {
			throw new ServiceException("SN码已存在");
		}

		queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(EquipmentCabinetEntity::getCabinetName, cabinetDTO.getCabinetName());
		num = getBaseMapper().selectCount(queryWrapper);
		if (num != null && num > 0) {
			throw new ServiceException("机柜名称已存在");
		}

		if (StrUtil.isNotEmpty(cabinetDTO.getCabinetNo())) {
			queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(EquipmentCabinetEntity::getCabinetNo, cabinetDTO.getCabinetNo());
			num = getBaseMapper().selectCount(queryWrapper);
			if (num != null && num > 0) {
				throw new ServiceException("设备编号已存在");
			}
		}

		if (!cabinetDTO.getCabinetType().equals(MainBizEnum.CABINET_TYPE_2.getCode())) {
			if (StrUtil.isEmpty(cabinetDTO.getCabinetGuid())) {
				throw new ServiceException("主控柜不能为空");
			}
			//查询主控柜信息
			ControlCabinetVO controlCabinetVO = mainControlCabinetService.selectByGuid(cabinetDTO.getCabinetGuid());
			if (controlCabinetVO == null) {
				throw new ServiceException("主控柜不存在");
			}
			equipmentCabinetEntity.setStoreRoomGuid(controlCabinetVO.getStoreRoomGuid());
			save(equipmentCabinetEntity);
			CabinetVO cabinetVO = detail(cabinetDTO.getEquipmentCabinetGuid());
			if (!"0".equals(cabinetDTO.getStatus())){
				sendCabinetInfo(cabinetVO);
			}
			return cabinetVO;
		}
		MainControlCabinetEntity mainControlCabinetEntity = BeanUtil.copyProperties(cabinetDTO, MainControlCabinetEntity.class);
		mainControlCabinetEntity.setCabinetGuid(IdUtil.simpleUUID());
//		mainControlCabinetEntity.setDeptCode(AuthUtil.getDeptCode());
		mainControlCabinetService.save(mainControlCabinetEntity);
		equipmentCabinetEntity.setCabinetGuid(mainControlCabinetEntity.getCabinetGuid());
		save(equipmentCabinetEntity);
		CabinetVO cabinetVO = detail(cabinetDTO.getEquipmentCabinetGuid());
		if (!"0".equals(cabinetDTO.getStatus())){
			sendCabinetInfo(cabinetVO);
		}
		return cabinetVO;
	}

	private void sendCabinetInfo(CabinetVO cabinetVO) {
		//TODO 前端需要加入机柜编号的输入
		nettyBuinessClient.sendCabinetInfo(cabinetVO.getSn(),cabinetVO.getCabinetNo(),cabinetVO.getCabinetType(),"1");
	}

	/**
	 * 修改机柜信息
	 *
	 * @param cabinetDTO
	 * @return
	 */
	@Override
	public CabinetVO update(CabinetDTO cabinetDTO) {
		if (StrUtil.isEmpty(cabinetDTO.getEquipmentCabinetGuid())) {
			throw new ServiceException("机柜ID不能为空");
		}
		if (storeRoomService.checkOperation(cabinetDTO.getEquipmentCabinetGuid(), AuthUtil.getUserId())) {
			throw new ServiceException("当前用户没有操作权限");
		}

		EquipmentCabinetEntity equipmentCabinetEntity = BeanUtil.copyProperties(cabinetDTO, EquipmentCabinetEntity.class);
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper();
		queryWrapper.lambda().eq(EquipmentCabinetEntity::getEquipmentCabinetGuid, equipmentCabinetEntity.getEquipmentCabinetGuid());
		EquipmentCabinetEntity original = getBaseMapper().selectOne(queryWrapper);
		if (original == null) {
			throw new ServiceException("未找到设备");
		}

		if (StrUtil.isNotEmpty(cabinetDTO.getCabinetNo())) {
			queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(EquipmentCabinetEntity::getCabinetNo, cabinetDTO.getCabinetNo());
			queryWrapper.lambda().ne(EquipmentCabinetEntity::getEquipmentCabinetGuid, cabinetDTO.getEquipmentCabinetGuid());
			Long num = getBaseMapper().selectCount(queryWrapper);
			if (num != null && num > 0) {
				throw new ServiceException("设备编号已存在");
			}
		}



		queryWrapper = new QueryWrapper();
		queryWrapper.lambda().eq(EquipmentCabinetEntity::getEquipmentCabinetGuid, equipmentCabinetEntity.getEquipmentCabinetGuid());

		equipmentCabinetEntity.setCabinetType(original.getCabinetType());
//		equipmentCabinetEntity.setCabinetGuid(original.getCabinetGuid());
		if (!original.getCabinetType().equals(MainBizEnum.CABINET_TYPE_2.getCode())) {
			update(equipmentCabinetEntity, queryWrapper);
			CabinetVO cabinetVO = detail(cabinetDTO.getEquipmentCabinetGuid());
			if (!"0".equals(cabinetDTO.getStatus())){
				sendCabinetInfo(cabinetVO);
			}
			return cabinetVO;
		}
		update(equipmentCabinetEntity, queryWrapper);
		MainControlCabinetEntity mainControlCabinetEntity = BeanUtil.copyProperties(cabinetDTO, MainControlCabinetEntity.class);
		QueryWrapper<MainControlCabinetEntity> mainQueryWrapper = new QueryWrapper();
		mainQueryWrapper.lambda().eq(MainControlCabinetEntity::getCabinetGuid, original.getCabinetGuid());
		mainControlCabinetService.update(mainControlCabinetEntity, mainQueryWrapper);
		CabinetVO cabinetVO = detail(cabinetDTO.getEquipmentCabinetGuid());
		if (!"0".equals(cabinetDTO.getStatus())){
			sendCabinetInfo(cabinetVO);
		}
		return cabinetVO;
	}

	/**
	 * 根据SN修改机柜信息
	 *
	 * @param cabinetDTO
	 * @return
	 */
	@Override
	public CabinetVO updateBySn(CabinetDTO cabinetDTO) {
		if (StrUtil.isEmpty(cabinetDTO.getSn())) {
			throw new ServiceException("SN码不能为空");
		}
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper();
		queryWrapper.lambda().eq(EquipmentCabinetEntity::getSn, cabinetDTO.getSn());
		EquipmentCabinetEntity equipmentCabinetEntity = getBaseMapper().selectOne(queryWrapper);
		if (equipmentCabinetEntity == null) {
			throw new ServiceException("未找到设备!");
		}
		cabinetDTO.setEquipmentCabinetGuid(equipmentCabinetEntity.getEquipmentCabinetGuid());

		return update(cabinetDTO);
	}

	/**
	 * 机柜信息详情
	 *
	 * @param guid
	 * @return
	 */
	@Override
	public CabinetVO detail(String guid) {
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper();
		queryWrapper.lambda().eq(EquipmentCabinetEntity::getEquipmentCabinetGuid, guid);
		EquipmentCabinetEntity equipmentCabinetEntity = getBaseMapper().selectOne(queryWrapper);
		if (equipmentCabinetEntity == null) {
			return null;
		}
		List<ModelVO> factoryList = modelService.getDropDownList("99");
		Map<String, String> factoryMap = factoryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		List<ModelVO> modelList = modelService.getDropDownList(equipmentCabinetEntity.getCabinetType());
		Map<String, String> map = modelList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		Map<String, Map<String, String>> modeMap = new HashMap<>();
		modeMap.put(equipmentCabinetEntity.getCabinetType(), map);
		CabinetVO cabinetVO = CabinetWrapper.build(factoryMap, modeMap).entityVO(equipmentCabinetEntity);
		cabinetVO.setControlCabinetVO(mainControlCabinetService.selectByGuid(cabinetVO.getCabinetGuid()));

		if (!cabinetVO.getCabinetType().equals(MainBizEnum.CABINET_TYPE_2.getCode())) {
			return cabinetVO;
		}
		queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(EquipmentCabinetEntity::getCabinetGuid, Arrays.asList(cabinetVO.getCabinetGuid()));
		List<EquipmentCabinetNumDTO> equipmentCabinetNumList = getBaseMapper().selectEquipmentCabinetNum(queryWrapper);
		for (EquipmentCabinetNumDTO equipmentCabinetNumDTO : equipmentCabinetNumList) {
			if (equipmentCabinetNumDTO.getCabinetType().equals(MainBizEnum.CABINET_TYPE_1.getCode())) {
				cabinetVO.setUavCabinetNum(equipmentCabinetNumDTO.getNum());
			}
			if (equipmentCabinetNumDTO.getCabinetType().equals(MainBizEnum.CABINET_TYPE_3.getCode())) {
				cabinetVO.setBatteryCabinetNum(equipmentCabinetNumDTO.getNum());
			}
		}
		return cabinetVO;
	}

	/**
	 * 删除机柜信息
	 *
	 * @param guids
	 */
	@Override
	public void delete(String guids) {
		List<String> split = StrUtil.split(guids, ",");
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(EquipmentCabinetEntity::getEquipmentCabinetGuid, split);
		List<EquipmentCabinetEntity> equipmentCabinetEntities = getBaseMapper().selectList(queryWrapper);
		for (EquipmentCabinetEntity equipmentCabinetEntity : equipmentCabinetEntities) {
			if (storeRoomService.checkOperation(equipmentCabinetEntity.getEquipmentCabinetGuid(), AuthUtil.getUserId())) {
				throw new ServiceException("当前用户没有操作权限");
			}
			if (equipmentCabinetEntity.getCabinetType().equals(MainBizEnum.CABINET_TYPE_2.getCode())) {
				//如果删除的是主控柜，则需要判断是否关联的无人机设备柜和标准电池柜
				queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().eq(EquipmentCabinetEntity::getCabinetGuid, equipmentCabinetEntity.getCabinetGuid());
				queryWrapper.lambda().ne(EquipmentCabinetEntity::getId, equipmentCabinetEntity.getId());
				Long num = getBaseMapper().selectCount(queryWrapper);
				if (num != null && num > 0) {
					throw new ServiceException("主控柜有关联无人机设备柜和标准电池柜，暂时无法删除");
				}
				QueryWrapper<MainControlCabinetEntity> deleteWrapper = new QueryWrapper<>();
				deleteWrapper.lambda().eq(MainControlCabinetEntity::getCabinetGuid, equipmentCabinetEntity.getCabinetGuid());

				mainControlCabinetService.getBaseMapper().delete(deleteWrapper);
				getBaseMapper().deleteById(equipmentCabinetEntity.getId());
			}
		}
		getBaseMapper().delete(queryWrapper);
	}

	/**
	 * 机柜列表信息
	 *
	 * @param page
	 * @param cabinetDTO
	 * @return
	 */
	@Override
	public IPage<CabinetVO> pageList(IPage<EquipmentCabinetEntity> page, CabinetDTO cabinetDTO) {
//		if (StrUtil.isEmpty(cabinetDTO.getCabinetType())) {
//			throw new ServiceException("设备类型不能为空");
//		}
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(StrUtil.isNotEmpty(cabinetDTO.getCabinetType()), EquipmentCabinetEntity::getCabinetType, cabinetDTO.getCabinetType());
		queryWrapper.lambda().eq(StrUtil.isNotEmpty(cabinetDTO.getStoreRoomGuid()), EquipmentCabinetEntity::getStoreRoomGuid, cabinetDTO.getStoreRoomGuid());
		queryWrapper.lambda().like(StrUtil.isNotEmpty(cabinetDTO.getCabinetName()), EquipmentCabinetEntity::getCabinetName, cabinetDTO.getCabinetName());
		queryWrapper.lambda().like(StrUtil.isNotEmpty(AuthUtil.getDeptCode()), EquipmentCabinetEntity::getDeptCode, AuthUtil.getDeptCode());
		queryWrapper.lambda().like(StrUtil.isNotEmpty(cabinetDTO.getStatus()), EquipmentCabinetEntity::getStatus, cabinetDTO.getStatus());
		queryWrapper.lambda().like(StrUtil.isNotEmpty(cabinetDTO.getDeptCode()), EquipmentCabinetEntity::getDeptCode, cabinetDTO.getDeptCode());
		queryWrapper.lambda().eq(StrUtil.isNotEmpty(cabinetDTO.getEquipmentCabinetGuid()), EquipmentCabinetEntity::getEquipmentCabinetGuid, cabinetDTO.getEquipmentCabinetGuid());
		queryWrapper.and(cabinetDTO.getStoreRoomGuidSetNull() != null && cabinetDTO.getStoreRoomGuidSetNull(), Wrapper -> Wrapper.lambda().isNull(EquipmentCabinetEntity::getStoreRoomGuid).or().eq(EquipmentCabinetEntity::getStoreRoomGuid, ""));
		IPage<EquipmentCabinetEntity> equipmentCabinetEntityIPage = getBaseMapper().selectPage(page, queryWrapper);

		IPage<CabinetVO> cabinetVOIPage = getCabinetVOIPage(equipmentCabinetEntityIPage);

		if (CollectionUtil.isEmpty(cabinetVOIPage.getRecords())) {
			return cabinetVOIPage;
		}

		//查询主控柜
		List<ControlCabinetVO> controlCabinetVOS = mainControlCabinetService.selectByGuidList(cabinetVOIPage.getRecords().stream().map(CabinetVO::getCabinetGuid).collect(Collectors.toList()));
		for (CabinetVO record : cabinetVOIPage.getRecords()) {
			for (ControlCabinetVO controlCabinetVO : controlCabinetVOS) {
				if (record.getCabinetGuid().equals(controlCabinetVO.getCabinetGuid())) {
					record.setControlCabinetVO(controlCabinetVO);
					break;
				}
			}
		}

//		if (!cabinetDTO.getCabinetType().equals(MainBizEnum.CABINET_TYPE_2.getCode())) {
//			return cabinetVOIPage;
//		}

		//如果是主控电池柜需要查询电池柜数量、设备柜数量
		selectNum(cabinetVOIPage);

		//判断是否查询关联的层级和插槽信息
		if (cabinetDTO.getIncludeFloor() != null && cabinetDTO.getIncludeFloor()) {
			selectFloorAndCellInfo(cabinetVOIPage.getRecords());
		}
		return cabinetVOIPage;
	}

	/**
	 * 查询关联的层级和插槽信息
	 *
	 * @param records
	 */
	private void selectFloorAndCellInfo(List<CabinetVO> records) {
		//先查询层级信息
		QueryWrapper<EquipmentCabinetFloorEntity> floorEntityQueryWrapper = new QueryWrapper<>();
		floorEntityQueryWrapper.lambda().in(EquipmentCabinetFloorEntity::getEquipmentCabinetGuid, records.stream().map(record -> record.getEquipmentCabinetGuid()).collect(Collectors.toList()));
		floorEntityQueryWrapper.lambda().orderByAsc(EquipmentCabinetFloorEntity::getFloorNo);
		List<EquipmentCabinetFloorEntity> floorList = floorService.getBaseMapper().selectList(floorEntityQueryWrapper);
		if (CollectionUtil.isEmpty(floorList)) {
			return;
		}
		//查询层级里存放的是无人机还是配件
		QueryWrapper<SpareEntity> spareEntityQueryWrapper = new QueryWrapper<>();
		spareEntityQueryWrapper.lambda().in(SpareEntity::getPosition, floorList.stream().map(entity -> entity.getPosition()).collect(Collectors.toList()));
		List<SpareEntity> spareEntities = spareMapper.selectList(spareEntityQueryWrapper);
		HashMap<String, SpareEntity> spareMap = new HashMap<>();
		for (SpareEntity spareEntity : spareEntities) {
			spareMap.put(spareEntity.getPosition(), spareEntity);
		}

		QueryWrapper<UavEntity> uavEntityQueryWrapper = new QueryWrapper<>();
		uavEntityQueryWrapper.lambda().in(UavEntity::getPosition, floorList.stream().map(entity -> entity.getPosition()).collect(Collectors.toList()));
		List<UavEntity> uavEntities = uavMapper.selectList(uavEntityQueryWrapper);
		HashMap<String, UavEntity> uavMap = new HashMap<>();
		for (UavEntity uavEntity : uavEntities) {
			uavMap.put(uavEntity.getPosition(), uavEntity);
		}

		List<EquipmentCabinetFloorVO> floorVOList = FloorWrapper.build().listVO(floorList);
		Map<String, List<EquipmentCabinetFloorVO>> floorMap = floorVOList.stream().collect(Collectors.groupingBy(floor -> floor.getEquipmentCabinetGuid()));
		for (CabinetVO record : records) {
			List<EquipmentCabinetFloorVO> list = floorMap.getOrDefault(record.getEquipmentCabinetGuid(), new ArrayList<>());
			list = list.stream().sorted(Comparator.comparingInt(cell -> Integer.parseInt(cell.getPosition().split("-")[1]))).collect(Collectors.toList());
			record.setFloorVOList(list);
		}
		//查询插槽信息
		List<String> floorGuidList = floorVOList.stream().map(floor -> floor.getFloorGuid()).collect(Collectors.toList());
		if (CollectionUtil.isEmpty(floorGuidList)) {
			return;
		}
		QueryWrapper<EquipmentCabinetCellEntity> cellEntityQueryWrapper = new QueryWrapper<>();
		cellEntityQueryWrapper.lambda().in(EquipmentCabinetCellEntity::getFloorGuid, floorGuidList);
		List<EquipmentCabinetCellEntity> cellEntityList = cellMapper.selectList(cellEntityQueryWrapper);
		List<EquipmentCabinetCellVO> cellVOList = CellWrapper.build().listVO(cellEntityList);

		//查询插槽里面的电池信息
		QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(BatteryEntity::getCellGuid, cellVOList.stream().map(cell -> cell.getCellGuid()).collect(Collectors.toList()));
		List<BatteryEntity> batteryEntities = batteryMapper.selectList(queryWrapper);
		List<ModelVO> batteryList = modelService.getDropDownList(MainBizEnum.DEVICE_TYPE_2.getCode());
		Map<String, String> modelMap = batteryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		List<BatteryVO> batteryVOS = BatteryWrapper.build(new HashMap<>(), modelMap).listVO(batteryEntities);
		HashMap<String, BatteryVO> cellPositionMap = new HashMap<>();
		for (BatteryVO batteryVO : batteryVOS) {
			cellPositionMap.put(batteryVO.getCellGuid(), batteryVO);
		}

		Map<String, List<EquipmentCabinetCellVO>> cellMap = cellVOList.stream().collect(Collectors.groupingBy(cell -> cell.getFloorGuid()));
		for (EquipmentCabinetFloorVO floorVO : floorVOList) {
			floorVO.setBatteryModelZh("");
			if (uavMap.containsKey(floorVO.getPosition())) {
				floorVO.setBatteryModelZh("/无人机");
			}
			if (spareMap.containsKey(floorVO.getPosition())) {
				floorVO.setBatteryModelZh(floorVO.getBatteryModelZh() + "/配件");
			}

			if (StrUtil.isNotEmpty(floorVO.getBatteryModelZh())) {
				if (floorVO.getBatteryModelZh().startsWith("/")) {
					floorVO.setBatteryModelZh(StrUtil.removePrefix(floorVO.getBatteryModelZh(), "/"));
				}
			}

			List<EquipmentCabinetCellVO> list = cellMap.getOrDefault(floorVO.getFloorGuid(), new ArrayList<>());
			list = list.stream().sorted(Comparator.comparingInt(cell -> Integer.parseInt(cell.getPosition().split("-")[2]))).collect(Collectors.toList());
			if (!MainBizEnum.DEVICE_TYPE_2.getCode().equals(floorVO.getFloorType())) {
				// 如果非电池设备层，则不去查询电池型号
				if (StrUtil.isEmpty(floorVO.getBatteryModelZh())) {
					floorVO.setBatteryModelZh("暂无设备");
				}
				continue;
			}
			for (EquipmentCabinetCellVO cellVO : list) {
				if (cellPositionMap.containsKey(cellVO.getCellGuid())) {
					floorVO.setBatteryModelZh(cellPositionMap.get(cellVO.getCellGuid()).getBatteryModelZh());
				}

				if (MainBizEnum.CELL_STATUS_0.getCode().equals(cellVO.getCellStatus())) {
					floorVO.setBatteryStateZh(MainBizEnum.CELL_STATUS_0.getName());
					break;
				} else {
					floorVO.setBatteryStateZh(MainBizEnum.CELL_STATUS_1.getName());
				}
			}
			floorVO.setCellVOList(list);
		}
	}


	/**
	 * 字典翻译 返回类型转换
	 *
	 * @param equipmentCabinetEntityIPage
	 * @return
	 */
	private IPage<CabinetVO> getCabinetVOIPage(IPage<EquipmentCabinetEntity> equipmentCabinetEntityIPage) {
		List<ModelVO> factoryList = modelService.getDropDownList("99");
		Map<String, String> factoryMap = factoryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		List<ModelVO> modelList = modelService.getDropDownList(MainBizEnum.CABINET_TYPE_2.getCode());
		Map<String, String> map1 = modelList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		Map<String, Map<String, String>> modeMap = new HashMap<>();
		modeMap.put(MainBizEnum.CABINET_TYPE_2.getCode(), map1);

		modelList = modelService.getDropDownList(MainBizEnum.CABINET_TYPE_3.getCode());
		Map<String, String> map2 = modelList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		modeMap.put(MainBizEnum.CABINET_TYPE_3.getCode(), map2);

		modelList = modelService.getDropDownList(MainBizEnum.CABINET_TYPE_1.getCode());
		Map<String, String> map3 = modelList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		modeMap.put(MainBizEnum.CABINET_TYPE_1.getCode(), map3);

		IPage<CabinetVO> cabinetVOIPage = CabinetWrapper.build(factoryMap, modeMap).pageVO(equipmentCabinetEntityIPage);
		return cabinetVOIPage;
	}

	//如果是主控电池柜需要查询电池柜数量、设备柜数量
	private void selectNum(IPage<CabinetVO> cabinetVOIPage) {
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(EquipmentCabinetEntity::getCabinetGuid, cabinetVOIPage.getRecords().stream().map(CabinetVO::getCabinetGuid).collect(Collectors.toList()));
		List<EquipmentCabinetNumDTO> equipmentCabinetNumList = getBaseMapper().selectEquipmentCabinetNum(queryWrapper);
		for (CabinetVO record : cabinetVOIPage.getRecords()) {
			record.setBatteryCabinetNum(0);
			record.setUavCabinetNum(0);
			if (!record.getCabinetType().equals(MainBizEnum.CABINET_TYPE_2.getCode())) {
				continue;
			}
			for (EquipmentCabinetNumDTO equipmentCabinetNumDTO : equipmentCabinetNumList) {
				if (!record.getCabinetGuid().equals(equipmentCabinetNumDTO.getCabinetGuid())) {
					continue;
				}
				if (equipmentCabinetNumDTO.getCabinetType().equals(MainBizEnum.CABINET_TYPE_1.getCode())) {
					record.setUavCabinetNum(equipmentCabinetNumDTO.getNum());
				}
				if (equipmentCabinetNumDTO.getCabinetType().equals(MainBizEnum.CABINET_TYPE_3.getCode())) {
					record.setBatteryCabinetNum(equipmentCabinetNumDTO.getNum());
				}
			}
		}
	}

	/**
	 * 批量导入
	 *
	 * @param file
	 */
	@Override
	public void importExcel(MultipartFile file) {
		ExcelReader reader;
		try {
			reader = ExcelUtil.getReader(file.getInputStream());
		} catch (IOException e) {
			e.printStackTrace();
			throw new ServiceException("文件读取失败");
		}

		List<List<Object>> read = reader.read(0);
		if (CollectionUtil.isEmpty(read) || read.size() <= 1) {
			throw new ServiceException("文件不能为空");
		}
		//获取到表头
		List<String> excelHeaderList = Arrays.asList("机柜类型", "所属主控柜SN", "机柜编号", "机柜名称", "SN码", "RFID码", "所属部门", "厂家");
		List<Object> headerList = read.get(0);
		for (int i = 0; i < headerList.size(); i++) {
			if (!excelHeaderList.get(i).equals(getCellValue(headerList.get(i)))) {
				throw new ServiceException("模版不正确,导入失败!");
			}
		}
		read.remove(0);
		List<CabinetDTO> cabinetDTOList = new ArrayList<>();
		Set<String> deptNameSet = new HashSet<>();
		Set<String> snSet = new HashSet<>();
		for (List<Object> objects : read) {
			deptNameSet.add(getCellValue(objects.get(7)));
			if (StrUtil.isNotEmpty(getCellValue(objects.get(5)))) {
				snSet.add(getCellValue(objects.get(5)));
			}
			CabinetDTO cabinetDTO = new CabinetDTO();
			cabinetDTO.setCabinetType(getCellValue(objects.get(0)));
			cabinetDTO.setCabinetGuid(getCellValue(objects.get(1)));//临时存放主控柜SN码
//			cabinetDTO.setCabinetModel(getCellValue(objects.get(2)));
			cabinetDTO.setCabinetNo(getCellValue(objects.get(2)));
			cabinetDTO.setCabinetName(getCellValue(objects.get(3)));
			cabinetDTO.setSn(getCellValue(objects.get(4)));
			cabinetDTO.setRfidGuid(getCellValue(objects.get(5)));
			cabinetDTO.setDeptCode(getCellValue(objects.get(6)));
			cabinetDTO.setFactory(getCellValue(objects.get(7)));
			cabinetDTOList.add(cabinetDTO);
		}
		List<ModelVO> factoryList = modelService.getDropDownList("99");

		//机柜型号
//		List<ModelVO> modelList = modelService.getDropDownList(MainBizEnum.CABINET_TYPE_2.getCode());
//		Map<String, String> map1 = modelList.stream().collect(Collectors.toMap(ModelVO::getName, ModelVO::getCode, (existing, replace) -> replace));
//		Map<String, Map<String, String>> modeMap = new HashMap<>();
//		modeMap.put(MainBizEnum.CABINET_TYPE_2.getCode(), map1);
//
//		modelList = modelService.getDropDownList(MainBizEnum.CABINET_TYPE_3.getCode());
//		Map<String, String> map2 = modelList.stream().collect(Collectors.toMap(ModelVO::getName, ModelVO::getCode, (existing, replace) -> replace));
//		modeMap.put(MainBizEnum.CABINET_TYPE_3.getCode(), map2);
//
//		modelList = modelService.getDropDownList(MainBizEnum.CABINET_TYPE_1.getCode());
//		Map<String, String> map3 = modelList.stream().collect(Collectors.toMap(ModelVO::getName, ModelVO::getCode, (existing, replace) -> replace));
//		modeMap.put(MainBizEnum.CABINET_TYPE_1.getCode(), map3);
		//机柜类型
		List<DictBiz> list = DictBizCache.getList(MainBizEnum.CABINET_TYPE.getCode());
		HashMap<String, String> cabinetTypeMap = new HashMap<>();
		for (DictBiz dictBiz : list) {
			cabinetTypeMap.put(dictBiz.getDictValue(), dictBiz.getDictKey());
		}

		//厂家
		HashMap<String, String> factoryMap = new HashMap<>();
		for (ModelVO modelVO : factoryList) {
			factoryMap.put(modelVO.getName(), modelVO.getCode());
		}

		//单位信息
		String join = CollectionUtil.join(deptNameSet, ",");
		R<List<Dept>> deptInfoResult = sysClient.getDeptInfos(AuthUtil.getTenantId(), join);
		if (!deptInfoResult.isSuccess()) {
			throw new ServiceException("获取单位信息失败，请稍后重试！");
		}
		HashMap<String, String> deptMap = new HashMap<>();
		for (Dept dept : deptInfoResult.getData()) {
			deptMap.put(dept.getDeptName(), dept.getDeptCode());
		}

		for (CabinetDTO cabinetDTO : cabinetDTOList) {
			String cabinetType = cabinetTypeMap.get(cabinetDTO.getCabinetType());
			if (StrUtil.isEmpty(cabinetType)) {
				throw new ServiceException("机柜类型不正确，请检查机柜类型");
			}
			cabinetDTO.setCabinetType(cabinetType);
			if (!cabinetType.equals(MainBizEnum.CABINET_TYPE_2.getCode())) {
				if (StrUtil.isEmpty(cabinetDTO.getCabinetGuid())) {
					throw new ServiceException("设备类型非主控柜时，主控柜SN码不能为空！");
				}
				QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper<>();
				queryWrapper.lambda().eq(EquipmentCabinetEntity::getSn, cabinetDTO.getCabinetGuid());
				EquipmentCabinetEntity equipmentCabinetEntity = getBaseMapper().selectOne(queryWrapper);
				if (equipmentCabinetEntity == null) {
					throw new ServiceException("未找到SN码为：" + cabinetDTO.getCabinetGuid() + "的主控设备！");
				}
				cabinetDTO.setCabinetGuid(equipmentCabinetEntity.getCabinetGuid());
			} else {
				cabinetDTO.setCabinetGuid(null);
			}

//			String model = modeMap.getOrDefault(cabinetType, new HashMap<>()).get(cabinetDTO.getCabinetModel());
//			cabinetDTO.setCabinetModel(model);

			cabinetDTO.setFactory(factoryMap.get(cabinetDTO.getFactory()));
			cabinetDTO.setDeptCode(deptMap.get(cabinetDTO.getDeptCode()));
			add(cabinetDTO);
		}
	}

	/**
	 * 批量导出
	 *
	 * @param cabinetDTO
	 * @param response
	 * @return
	 */
	@Override
	public void exportExcel(CabinetDTO cabinetDTO, HttpServletResponse response) {
		IPage<EquipmentCabinetEntity> page = Condition.getPage(new Query());
		page.setSize(-1);
		List<CabinetVO> recordList = pageList(page, cabinetDTO).getRecords();
		//表格头部
		List<String> headerList = Arrays.asList("机柜类型", "机柜编号", "机柜名称", "SN码", "RFID码", "所属部门", "厂家", "状态");
		if (MainBizEnum.CABINET_TYPE_2.getCode().equals(cabinetDTO.getCabinetType())) {
			headerList = headerList.stream().collect(Collectors.toList());
			headerList.add("电池柜数量");
			headerList.add("设备柜数量");
		}
		List<List<String>> rows = getRows(recordList, headerList, cabinetDTO.getCabinetType());
		ExcelWriter writer = ExcelUtil.getWriter();
		writer.renameSheet("机柜信息导出");
		writer.merge(headerList.size() - 1, "机柜信息");
		writer.write(rows, true);
		response.setContentType("application/vnd.ms-excel;charset=utf-8");
		String fileName = DateUtil.now().replace(" ", "") + ".xls";
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
		ServletOutputStream outputStream = null;
		try {
			outputStream = response.getOutputStream();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		writer.flush(outputStream, true);
		// 关闭writer，释放内存
		writer.close();
		//此处记得关闭输出Servlet流
		IoUtil.close(outputStream);
	}

	private List<List<String>> getRows(List<CabinetVO> recordList, List<String> headerList, String cabinetType) {
		List<List<String>> rows = new ArrayList<>();
		rows.add(headerList);
		for (CabinetVO cabinetVO : recordList) {
			List<String> row = new ArrayList<>();
			row.add(cabinetVO.getCabinetTypeZh());
			row.add(cabinetVO.getCabinetNo());
			row.add(cabinetVO.getCabinetName());
			row.add(cabinetVO.getSn());
			row.add(cabinetVO.getRfidGuid());
			row.add(cabinetVO.getDeptName());
			row.add(cabinetVO.getFactoryName());
			row.add(cabinetVO.getStatusZh());
			if (MainBizEnum.CABINET_TYPE_2.getCode().equals(cabinetType)) {
				row.add(cabinetVO.getBatteryCabinetNum() + "");
				row.add(cabinetVO.getUavCabinetNum() + "");
			}
			rows.add(row);
		}
		return rows;
	}

	/**
	 * 获取表格内容
	 *
	 * @param o
	 * @return
	 */
	public String getCellValue(Object o) {
		if (o == null) {
			return null;
		}
		return StrUtil.trim(o.toString());
	}

	@Override
	public List<HomeCabinetVO> homeList(String storeRoomGuid) {
		QueryWrapper<EquipmentCabinetEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(StrUtil.isNotEmpty(storeRoomGuid), EquipmentCabinetEntity::getStoreRoomGuid, storeRoomGuid);
		queryWrapper.lambda().eq(EquipmentCabinetEntity::getIsDeleted, 0).orderByAsc(EquipmentCabinetEntity::getCabinetType);

		List<EquipmentCabinetEntity> equipmentCabinetEntityList = getBaseMapper().selectList(queryWrapper);
		List<HomeCabinetVO> homeCabinetVOList = BeanUtil.copyToList(equipmentCabinetEntityList, HomeCabinetVO.class);
		homeCabinetVOList.forEach(e -> {
			e.setCabinetTypeZh(DictBizCache.getValue(MainBizEnum.CABINET_TYPE.getCode(), e.getCabinetType()));
		});

		//按型号分组
		Map<String, List<HomeCabinetVO>> listMap = homeCabinetVOList.stream().collect(Collectors.groupingBy(t -> t.getCabinetType()));
		//根据key找value
		Set<String> set = listMap.keySet(); //取出所有的key值
		List<HomeCabinetVO> result = new ArrayList<>();
		for (String key : set) {
			Collections.sort(listMap.get(key), new Comparator<HomeCabinetVO>() {
				@Override
				public int compare(HomeCabinetVO o1, HomeCabinetVO o2) {
					String s1 = o1.getCabinetName().substring(0, 1);
					String s2 = o2.getCabinetName().substring(0, 1);
					try {
						int diff = Integer.parseInt(s1) - Integer.parseInt(s2);
						if (diff > 0) {
							return 1;
						} else if (diff < 0) {
							return -1;
						}
						return 0;
					} catch (Exception e) {
						e.printStackTrace();
						return 0;
					}
				}
			});
			result.addAll(listMap.get(key));
		}

		allcoreRedis.setEx(BasicConstant.DISPLAY_ROOM_GUID + AuthUtil.getUser().getAccount(), storeRoomGuid,BasicConstant.HOURS_1);
		wsService.reflushExtendPage();
		return result;
	}

	@Override
	public R openEquipmentCabinet(String equipmentCabinetGuid, String flag) {
		QueryWrapper<EquipmentCabinetEntity> equipmentCabinetEntityQueryWrapper = new QueryWrapper<>();
		equipmentCabinetEntityQueryWrapper.lambda().eq(EquipmentCabinetEntity::getEquipmentCabinetGuid, equipmentCabinetGuid);
		equipmentCabinetEntityQueryWrapper.lambda().eq(EquipmentCabinetEntity::getIsDeleted, 0);
		EquipmentCabinetEntity equipmentCabinetEntity = equipmentCabinetMapper.selectOne(equipmentCabinetEntityQueryWrapper);
		if ("1".equals(flag)) {
			boolean bool = storeRoomService.checkOperation(equipmentCabinetGuid, AuthUtil.getUserId());
			if (bool) {
				return R.fail("当前用户没有权限！");
			}
			nettyBuinessClient.unlockDoor(equipmentCabinetEntity.getSn());
		}

		if (MainBizEnum.CABINET_TYPE_1.getCode().equals(equipmentCabinetEntity.getCabinetType())){
			allcoreRedis.setEx(BasicConstant.NETTY_RFID_NUM, BasicConstant.INT_ZERO,BasicConstant.HOURS_1);
			allcoreRedis.setEx(BasicConstant.CABINET_INFO, equipmentCabinetEntity.getSn() + BasicConstant.COMMA + equipmentCabinetEntity.getCabinetNo(),BasicConstant.HOURS_1);
			allcoreRedis.setEx(equipmentCabinetEntity.getSn()+BasicConstant.LOPPNUM, BasicConstant.INT_ZERO,BasicConstant.HOURS_1);
		}
		QueryWrapper<EquipmentCabinetFloorEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(EquipmentCabinetFloorEntity::getEquipmentCabinetGuid, equipmentCabinetGuid);
		queryWrapper.lambda().eq(EquipmentCabinetFloorEntity::getIsDeleted, 0);

		List<EquipmentCabinetFloorEntity> equipmentCabinetFloorEntities = equipmentCabinetFloorMapper.selectList(queryWrapper);
		if (MainBizEnum.CABINET_TYPE_1.getCode().equals(equipmentCabinetEntity.getCabinetType())){
			equipmentCabinetFloorEntities.forEach(e -> {
				allcoreRedis.set(equipmentCabinetEntity.getSn() + "_" + (Integer.valueOf(e.getFloorNo())-1), null);
			});
		}

		NettyCabinetDTO dto = new NettyCabinetDTO();
		dto.setCabinetSnCode(equipmentCabinetEntity.getSn());
		dto.setPosition(equipmentCabinetEntity.getCabinetNo());
		nettyBuinessClient.monitorRfidStock(dto);

		List<EquipmentCabinetFloorVO> equipmentCabinetFloorVOList = BeanUtil.copyToList(equipmentCabinetFloorEntities, EquipmentCabinetFloorVO.class);
		equipmentCabinetFloorVOList.forEach(e -> {
			if (!"6".equals(equipmentCabinetEntity.getCabinetType())) {
				QueryWrapper<EquipmentCabinetCellEntity> entityQueryWrapper = new QueryWrapper<>();
				entityQueryWrapper.lambda().eq(EquipmentCabinetCellEntity::getFloorGuid, e.getFloorGuid());
				entityQueryWrapper.lambda().eq(EquipmentCabinetCellEntity::getIsDeleted, 0);

				List<EquipmentCabinetCellEntity> equipmentCabinetCellEntities = cellMapper.selectList(entityQueryWrapper);
				e.setBatteryStateZh("存储");
				equipmentCabinetCellEntities.forEach(item -> {
					if ("0".equals(item.getCellStatus())) {
						e.setBatteryStateZh("充电");
					}
//					QueryWrapper<BatteryEntity> batteryEntityQueryWrapper = new QueryWrapper<>();
//					batteryEntityQueryWrapper.lambda().eq(BatteryEntity::getCellGuid, item.getCellGuid());
//					batteryEntityQueryWrapper.lambda().eq(BatteryEntity::getIsDeleted, 0);
//					BatteryEntity batteryEntity = batteryMapper.selectOne(batteryEntityQueryWrapper);
//					if (null != batteryEntity) {
//						e.setBatteryModelZh(selectModelName(batteryEntity.getBatteryModel()));
//					}
				});
				e.setFloorTypeZh("电池");
				if(!"0".equals(e.getFloorType())) {
					e.setBatteryModelZh(BatteryModelEnum.getByCode(e.getFloorType()).getValue());
				}
			} else {
				QueryWrapper<UavEntity> entityQueryWrapper = new QueryWrapper<>();
				entityQueryWrapper.lambda().eq(UavEntity::getFloorGuid, e.getFloorGuid());
				entityQueryWrapper.lambda().eq(UavEntity::getIsDeleted, 0);
				List<UavEntity> uavEntity = uavMapper.selectList(entityQueryWrapper);

				QueryWrapper<SpareEntity> spareEntityQueryWrapper = new QueryWrapper<>();
				spareEntityQueryWrapper.lambda().eq(SpareEntity::getFloorGuid, e.getFloorGuid());
				spareEntityQueryWrapper.lambda().eq(SpareEntity::getIsDeleted, 0);
				List<SpareEntity> spareEntities = spareMapper.selectList(spareEntityQueryWrapper);
				if (uavEntity.size() > 0 && spareEntities.size() > 0) {
					e.setFloorTypeZh("无人机/配件");
				} else if (uavEntity.size() > 0 && spareEntities.size() == 0) {
					e.setFloorTypeZh("无人机");
				} else if (uavEntity.size() == 0 && spareEntities.size() > 0) {
					e.setFloorTypeZh("配件");
				} else {
					e.setFloorTypeZh("暂无设备");
				}
			}
		});

		OpenCabinetVO openCabinetVO = new OpenCabinetVO();
		equipmentCabinetFloorVOList.sort(Comparator.comparing(EquipmentCabinetFloorVO::getFloorNo));
		openCabinetVO.setEquipmentCabinetFloorVOList(equipmentCabinetFloorVOList);
		//查询个人名下未归还设备
		AllcoreUser user = AuthUtil.getUser();
		if (null != user) {
			List<ReceiveLogEntity> receiveLogEntityList = receiveLogMapper.selectReceiveType(user.getUserId());
			Iterator<ReceiveLogEntity> iterator = receiveLogEntityList.iterator();
			while (iterator.hasNext()) {
				ReceiveLogEntity receiveLogEntity = iterator.next();
				List<ReceiveLogEntity> receiveLogEntities = receiveLogMapper.selectReturnType(user.getUserId(), receiveLogEntity.getReceiveTime(), receiveLogEntity.getDeviceGuid());
				if (receiveLogEntities.size() > 0) {
					iterator.remove();
				}
			}
			List<DeviceChildVO> deviceStatusVOS = new ArrayList<>();
			receiveLogEntityList.forEach(e -> {
				DeviceChildVO deviceChildVO = new DeviceChildVO();
				deviceChildVO.setDeviceType(DictBizCache.getValue(MainBizEnum.DEVICE_TYPE.getCode(), e.getReceiveType()));
				deviceChildVO.setDeviceGuid(e.getDeviceGuid());
				deviceChildVO.setDeviceName(findName(e.getReceiveType(), e.getDeviceGuid()));
				deviceChildVO.setDevicePic(receiveLogMapper.selectPic(e.getReceiveType(), e.getReceiveModel()));
				deviceStatusVOS.add(deviceChildVO);
			});
			openCabinetVO.setDeviceStatusVOS(deviceStatusVOS);
		}

		//添加操作日志
		LogEntity logEntity = new LogEntity();
		logEntity.setLogData(user.getAccount() + "打开了机柜（" + equipmentCabinetEntity.getSn() + ")的柜门");
		logService.add(logEntity);


		//开启redis 异步处理 出入库数据
		if ("1".equals(flag)) {
			EquipmentCabinetService equipmentCabinetService = applicationContext.getBean(EquipmentCabinetService.class);
			equipmentCabinetService.handleRedisInOut(equipmentCabinetEntity.getSn(), AuthUtil.getUserId());
		}
		return R.data(openCabinetVO);
	}

	/**
	 * 点击开启柜门后  异步处理 出入库信息
	 *
	 * @param snCode
	 */
	@Async
	@Override
	public void handleRedisInOut(String snCode ,String userId) {
		try {
			//等待5s
			//Thread.sleep(50000);
			System.out.println("进入出入库信息-监听状态");
			String door = allcoreRedis.get(snCode + BasicConstant.DOOR).toString();
			System.out.println(snCode + "柜门开启状态：" + door);
			//确认柜门开启  才去刷redis的出入库
			while (true) {

				System.out.println("开始监听出入库信息：");
				List<String> listRfidIn = null;
				List<String> listBatteryIn = null;
				//入库信息
				if (null != allcoreRedis.get(snCode + BasicConstant.IN)) {
					listRfidIn = Arrays.asList(allcoreRedis.get(snCode + BasicConstant.IN).toString().split(","));
				}
				if (null != allcoreRedis.get(snCode + BasicConstant.IN_B)) {
					listBatteryIn = Arrays.asList(allcoreRedis.get(snCode + BasicConstant.IN_B).toString().split(","));
				}

				//获取上次的redis的值
				List<String> listCheckIn = allcoreRedis.get(snCode + BasicConstant.IN_CHECK) == null ? new ArrayList<>() : allcoreRedis.get(snCode + BasicConstant.IN_CHECK);
				List<String> checkInNow = new ArrayList<>();
				if (null != listRfidIn) {
					checkInNow.addAll(listRfidIn);
				}
				if (null != listBatteryIn) {
					checkInNow.addAll(listBatteryIn);
				}

				//出库信息
				List<String> listRfidOut = null;
				if (null != allcoreRedis.get(snCode + BasicConstant.OUT)) {
					listRfidOut = Arrays.asList(allcoreRedis.get(snCode + BasicConstant.OUT).toString().split(","));
				}
				List<String> listBatteryOut = null;
				if (null != allcoreRedis.get(snCode + BasicConstant.OUT_B)) {
					listBatteryOut = Arrays.asList(allcoreRedis.get(snCode + BasicConstant.OUT_B).toString().split(","));
				}
				//获取上次的redis的值
				List<String> listCheckOut = allcoreRedis.get(snCode + BasicConstant.OUT_CHECK) == null ? new ArrayList<>() : allcoreRedis.get(snCode + BasicConstant.OUT_CHECK);
				List<String> checkOutNow = new ArrayList<>();
				if (null != listRfidOut) {
					checkOutNow.addAll(listRfidOut);
				}
				if (null != listBatteryOut) {
					checkOutNow.addAll(listBatteryOut);
				}
				//如果数据发送改变了  才会触发
				DeviceStatusVO deviceStatusVO = new DeviceStatusVO();
				//if (!(listCheckOut.containsAll(checkOutNow)) || !(listCheckIn.containsAll(checkInNow))) {

					deviceStatusVO.setReceiveChildVOS(returnResult(listRfidOut, listBatteryOut, 0));
					deviceStatusVO.setReturnChildVOS(returnResult(listRfidIn, listBatteryIn, 1));
					wsService.sendReceiveAndRevertInfo(userId, deviceStatusVO);
					System.out.println("发送ws");
				//}

				allcoreRedis.setEx(snCode + BasicConstant.IN_CHECK, checkInNow,BasicConstant.HOURS_1);
				allcoreRedis.setEx(snCode + BasicConstant.OUT_CHECK, checkOutNow,BasicConstant.HOURS_1);
				//等待2s
				Thread.sleep(1000);
				door = allcoreRedis.get(snCode + BasicConstant.DOOR).toString();
				if(("0").equals(door)){
					break;
				}
			}

		} catch (InterruptedException e) {
			throw new RuntimeException(e);
		}

	}

	private List<DeviceReceiveVO> returnResult(List<String> rfidList, List<String> snList, Integer type) {
		List<DeviceReceiveVO> returnChildVOS = new ArrayList<>();
		if (null != rfidList) {
			rfidList.forEach(e -> {
				QueryWrapper<UavEntity> uavEntityQueryWrapper = new QueryWrapper<>();
				uavEntityQueryWrapper.lambda().eq(UavEntity::getRfidGuid, e);
				uavEntityQueryWrapper.lambda().eq(UavEntity::getIsDeleted, 0);
				UavEntity uavEntity = uavMapper.selectOne(uavEntityQueryWrapper);
				if (null != uavEntity) {
					DeviceReceiveVO deviceReceiveVO = new DeviceReceiveVO();
					deviceReceiveVO.setEquipmentCabinetGuid(uavEntity.getEquipmentCabinetGuid());
					deviceReceiveVO.setOperatorType(type);
					if (1 == type) {
						deviceReceiveVO.setReason("");
					} else {
						deviceReceiveVO.setReason("正常");
					}
					deviceReceiveVO.setDeviceGuid(uavEntity.getUavGuid());
					deviceReceiveVO.setReceiveModel(uavEntity.getUavModel());
					deviceReceiveVO.setReceiveType(uavEntity.getUavType());
					deviceReceiveVO.setDeviceName(uavEntity.getUavName());
					deviceReceiveVO.setDeviceUrl(null == receiveLogMapper.selectPic(uavEntity.getUavType(), uavEntity.getUavModel()) ? "" : receiveLogMapper.selectPic(uavEntity.getUavType(), uavEntity.getUavModel()));
					deviceReceiveVO.setPicUrl("");
					deviceReceiveVO.setRoomName(selectRoom(uavEntity.getEquipmentCabinetGuid()).getStoreRoomName());
					deviceReceiveVO.setStoreRoomGuid(selectRoom(uavEntity.getEquipmentCabinetGuid()).getStoreRoomGuid());
					deviceReceiveVO.setSnCode(uavEntity.getSnCode());
					returnChildVOS.add(deviceReceiveVO);
				}

				QueryWrapper<SpareEntity> spareEntityQueryWrapper = new QueryWrapper<>();
				spareEntityQueryWrapper.lambda().eq(SpareEntity::getRfidGuid, e);
				spareEntityQueryWrapper.lambda().eq(SpareEntity::getIsDeleted, 0);
				SpareEntity spareEntity = spareMapper.selectOne(spareEntityQueryWrapper);
				if (null != spareEntity) {
					DeviceReceiveVO deviceReceiveVO = new DeviceReceiveVO();
					deviceReceiveVO.setEquipmentCabinetGuid(spareEntity.getEquipmentCabinetGuid());
					deviceReceiveVO.setOperatorType(type);
					if (1 == type) {
						deviceReceiveVO.setReason("");
					} else {
						deviceReceiveVO.setReason("正常");
					}
					deviceReceiveVO.setDeviceGuid(spareEntity.getSpareGuid());
					deviceReceiveVO.setReceiveModel(spareEntity.getSpareModel());
					deviceReceiveVO.setReceiveType(spareEntity.getSpareType());
					deviceReceiveVO.setDeviceName(spareEntity.getSpareName());
					deviceReceiveVO.setDeviceUrl(null == receiveLogMapper.selectPic(spareEntity.getSpareType(), spareEntity.getSpareModel()) ? "" : receiveLogMapper.selectPic(spareEntity.getSpareType(), spareEntity.getSpareModel()));
					deviceReceiveVO.setPicUrl("");
					deviceReceiveVO.setRoomName(selectRoom(spareEntity.getEquipmentCabinetGuid()).getStoreRoomName());
					deviceReceiveVO.setStoreRoomGuid(selectRoom(spareEntity.getEquipmentCabinetGuid()).getStoreRoomGuid());
					deviceReceiveVO.setSnCode(spareEntity.getSnCode());
					returnChildVOS.add(deviceReceiveVO);
				}
			});
		}

		if (null != snList) {
			snList.forEach(e -> {
				QueryWrapper<BatteryEntity> batteryEntityQueryWrapper = new QueryWrapper<>();
				batteryEntityQueryWrapper.lambda().eq(BatteryEntity::getSnCode, e);
				batteryEntityQueryWrapper.lambda().eq(BatteryEntity::getIsDeleted, 0);
				BatteryEntity batteryEntity = batteryMapper.selectOne(batteryEntityQueryWrapper);
				if (null != batteryEntity) {
					DeviceReceiveVO deviceReceiveVO = new DeviceReceiveVO();
					deviceReceiveVO.setEquipmentCabinetGuid(batteryEntity.getEquipmentCabinetGuid());
					deviceReceiveVO.setOperatorType(type);
					if (1 == type) {
						deviceReceiveVO.setReason("");
					} else {
						deviceReceiveVO.setReason("正常");
					}
					deviceReceiveVO.setDeviceGuid(batteryEntity.getBatteryGuid());
					deviceReceiveVO.setReceiveModel(batteryEntity.getBatteryModel());
					deviceReceiveVO.setReceiveType(batteryEntity.getBatteryType());
					deviceReceiveVO.setDeviceName(batteryEntity.getBatteryName());
					deviceReceiveVO.setDeviceUrl(null == receiveLogMapper.selectPic(batteryEntity.getBatteryType(), batteryEntity.getBatteryModel()) ? "" : receiveLogMapper.selectPic(batteryEntity.getBatteryType(), batteryEntity.getBatteryModel()));
					deviceReceiveVO.setPicUrl("");
					deviceReceiveVO.setRoomName(selectRoom(batteryEntity.getEquipmentCabinetGuid()).getStoreRoomName());
					deviceReceiveVO.setStoreRoomGuid(selectRoom(batteryEntity.getEquipmentCabinetGuid()).getStoreRoomGuid());
					deviceReceiveVO.setSnCode(batteryEntity.getSnCode());
					returnChildVOS.add(deviceReceiveVO);
				}
			});
		}
		return returnChildVOS;
	}

	private StoreRoomEntity selectRoom(String equipmentCabinetGuid) {
		StoreRoomEntity storeRoomEntity = baseMapper.selectRoom(equipmentCabinetGuid);
		return storeRoomEntity;
	}


	private String findName(String deviceType, String deviceGuid) {
		if ("1".equals(deviceType)) {
			QueryWrapper<UavEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(UavEntity::getUavGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(UavEntity::getIsDeleted, 0);
			UavEntity uavEntity = uavMapper.selectOne(entityQueryWrapper);
			return uavEntity.getUavName();
		} else if ("2".equals(deviceType)) {
			QueryWrapper<BatteryEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(BatteryEntity::getBatteryGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(BatteryEntity::getIsDeleted, 0);
			BatteryEntity batteryEntity = batteryMapper.selectOne(entityQueryWrapper);
			return batteryEntity.getBatteryName();
		} else if ("3".equals(deviceType)) {
			QueryWrapper<SpareEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(SpareEntity::getSpareGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(SpareEntity::getIsDeleted, 0);
			SpareEntity spareEntity = spareMapper.selectOne(entityQueryWrapper);
			return spareEntity.getSpareName();
		}
		return "无该型号设备";
	}

	@Override
	public R openFloor(String floorGuid) {
		QueryWrapper<EquipmentCabinetCellEntity> entityQueryWrapper = new QueryWrapper<>();
		entityQueryWrapper.lambda().eq(EquipmentCabinetCellEntity::getFloorGuid, floorGuid).eq(EquipmentCabinetCellEntity::getIsDeleted, 0);
		entityQueryWrapper.lambda().eq(EquipmentCabinetCellEntity::getIsDeleted, 0);
		entityQueryWrapper.orderByAsc("cell_no+0.0");
		List<EquipmentCabinetCellEntity> equipmentCabinetCellEntities = cellMapper.selectList(entityQueryWrapper);
		List<EquipmentCabinetCellVO> equipmentCabinetCellVOS = BeanUtil.copyToList(equipmentCabinetCellEntities, EquipmentCabinetCellVO.class);
		//如果有槽 则是电池柜
		if (CollectionUtil.isNotEmpty(equipmentCabinetCellEntities)) {
			equipmentCabinetCellVOS.forEach(e -> {
				e.setCellStatusZh(DictBizCache.getValue(MainBizEnum.CELL_STATUS.getCode(), e.getCellStatus()));
				e.setDeviceKind("2");

				QueryWrapper<BatteryEntity> batteryEntityQueryWrapper = new QueryWrapper<>();
				batteryEntityQueryWrapper.lambda().eq(BatteryEntity::getEquipmentCabinetGuid, e.getEquipmentCabinetGuid());
				batteryEntityQueryWrapper.lambda().eq(BatteryEntity::getPosition, e.getPosition());
				batteryEntityQueryWrapper.lambda().eq(BatteryEntity::getIsDeleted, 0);
				BatteryEntity batteryEntity = batteryMapper.selectOne(batteryEntityQueryWrapper);
				if (null != batteryEntity) {
					if (BasicConstant.STRING_ZERO.equals(batteryEntity.getState())) {
						e.setExistDevice(batteryEntity.getState());
					} else {
						e.setExistDevice("1");
					}
					e.setDeviceType(selectModelName(batteryEntity.getBatteryModel()));
					e.setBatteryType(batteryEntity.getBatteryType());
					e.setRfidGuid(batteryEntity.getRfidGuid());
				}
			});
		} else {
			//如果没有槽 则是设备柜
			LambdaQueryWrapper<EquipmentCabinetFloorEntity> floorEntityQueryWrapper = new LambdaQueryWrapper<>();
			floorEntityQueryWrapper.eq(EquipmentCabinetFloorEntity::getFloorGuid, floorGuid)
				.eq(EquipmentCabinetFloorEntity::getIsDeleted, 0);

			EquipmentCabinetFloorEntity e = floorService.getBaseMapper().selectOne(floorEntityQueryWrapper);


			QueryWrapper<UavEntity> uavEntityQueryWrapper = new QueryWrapper<>();
			uavEntityQueryWrapper.lambda().eq(UavEntity::getEquipmentCabinetGuid, e.getEquipmentCabinetGuid());
			uavEntityQueryWrapper.lambda().eq(UavEntity::getPosition, e.getPosition());
			uavEntityQueryWrapper.lambda().eq(UavEntity::getIsDeleted, 0);
			List<UavEntity> uavEntity = uavMapper.selectList(uavEntityQueryWrapper);
			if (CollectionUtil.isNotEmpty(uavEntity)) {
				uavEntity.forEach(item -> {
					EquipmentCabinetCellVO equipmentCabinetCellVO = new EquipmentCabinetCellVO();
					equipmentCabinetCellVO.setDeviceType("1");
					equipmentCabinetCellVO.setPosition(e.getPosition());
					equipmentCabinetCellVO.setFloorGuid(floorGuid);
					equipmentCabinetCellVO.setCellStatus("1");
					equipmentCabinetCellVO.setCellNo(e.getFloorNo());
					equipmentCabinetCellVO.setDeviceKind("1");
					equipmentCabinetCellVO.setExistDevice("1");
					equipmentCabinetCellVO.setCellGuid(item.getUavGuid());
					equipmentCabinetCellVO.setRfidGuid(item.getRfidGuid());
					equipmentCabinetCellVOS.add(equipmentCabinetCellVO);
				});
			}
			QueryWrapper<SpareEntity> spareEntityQueryWrapper = new QueryWrapper<>();
			spareEntityQueryWrapper.lambda().eq(SpareEntity::getEquipmentCabinetGuid, e.getEquipmentCabinetGuid());
			spareEntityQueryWrapper.lambda().eq(SpareEntity::getPosition, e.getPosition());
			spareEntityQueryWrapper.lambda().eq(SpareEntity::getIsDeleted, 0);
			List<SpareEntity> spareEntity = spareMapper.selectList(spareEntityQueryWrapper);
			if (CollectionUtil.isNotEmpty(spareEntity)) {
				spareEntity.forEach(item -> {
					EquipmentCabinetCellVO equipmentCabinetCellVO = new EquipmentCabinetCellVO();
					equipmentCabinetCellVO.setDeviceType("3");
					equipmentCabinetCellVO.setPosition(e.getPosition());
					equipmentCabinetCellVO.setFloorGuid(floorGuid);
					equipmentCabinetCellVO.setCellStatus("1");
					equipmentCabinetCellVO.setCellNo(e.getFloorNo());
					equipmentCabinetCellVO.setDeviceKind("3");
					equipmentCabinetCellVO.setExistDevice("1");
					equipmentCabinetCellVO.setCellGuid(item.getSpareGuid());
					equipmentCabinetCellVO.setRfidGuid(item.getRfidGuid());
					equipmentCabinetCellVOS.add(equipmentCabinetCellVO);
				});
			}

		}


		return R.data(equipmentCabinetCellVOS);
	}

	private String selectModelName(String code) {
		QueryWrapper<ModelEntity> entityQueryWrapper = new QueryWrapper<>();
		entityQueryWrapper.lambda().eq(ModelEntity::getCode, code);
		entityQueryWrapper.lambda().eq(ModelEntity::getIsDeleted, 0);
		List<ModelEntity> modelEntity = modelService.list(entityQueryWrapper);
		if (modelEntity.size() > 0) {
			return modelEntity.get(0).getName();
		}
		return "";
	}

	@Override
	public R openCell(String cellGuid) {
		QueryWrapper<BatteryEntity> entityQueryWrapper = new QueryWrapper<>();
		entityQueryWrapper.lambda().eq(BatteryEntity::getCellGuid, cellGuid);
		BatteryEntity batteryEntity = batteryMapper.selectOne(entityQueryWrapper);
		CabinetBatteryVO cabinetBatteryVO = BeanUtil.copyProperties(batteryEntity, CabinetBatteryVO.class);
		cabinetBatteryVO.setPicUrl(receiveLogMapper.selectPic(cabinetBatteryVO.getBatteryType(), cabinetBatteryVO.getBatteryModel()));
		cabinetBatteryVO.setState(DictBizCache.getValue(MainBizEnum.BATTERTY_STATE.getCode(), batteryEntity.getState()));
		cabinetBatteryVO.setBatteryType(DictBizCache.getValue(MainBizEnum.BATTERTY_TYPE.getCode(), batteryEntity.getBatteryType()));
		cabinetBatteryVO.setBatteryModel(selectModelName(batteryEntity.getBatteryModel()));

		return R.data(cabinetBatteryVO);
	}

	@Override
	public R openUavCell(String guid) {

		QueryWrapper<UavEntity> entityQueryWrapper = new QueryWrapper<>();
		entityQueryWrapper.lambda().eq(UavEntity::getUavGuid, guid);
		entityQueryWrapper.lambda().eq(UavEntity::getIsDeleted, 0);
		UavEntity uavEntity = uavMapper.selectOne(entityQueryWrapper);
		uavEntity.setState(DictBizCache.getValue(MainBizEnum.STATE.getCode(), uavEntity.getState()));

		UavHomeVO uavHomeVO = BeanUtil.copyProperties(uavEntity, UavHomeVO.class);
		uavHomeVO.setPicUrl(receiveLogMapper.selectPic(uavHomeVO.getUavType(), uavHomeVO.getUavModel()));
		uavHomeVO.setDeptCodeZh(sysClient.getDeptNameByDeptCode(uavHomeVO.getDeptCode()).getData());
		uavHomeVO.setUavType(DictBizCache.getValue(MainBizEnum.DEVICE_TYPE.getCode(), uavHomeVO.getUavType()));
		List<ModelVO> factoryList = modelService.getDropDownList("99");
		Map<String, String> factoryMap = factoryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		List<ModelVO> batteryList = modelService.getDropDownList(MainBizEnum.DEVICE_TYPE_2.getCode());
		Map<String, String> modelMap = batteryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		uavHomeVO.setUavModel(selectModelName(uavHomeVO.getUavModel()));
		uavHomeVO.setFactoryCode(factoryMap.get(uavHomeVO.getFactoryCode()));
		return R.data(uavHomeVO);
	}

	@Override
	public R opeSpareCell(String guid) {

		QueryWrapper<SpareEntity> entityQueryWrapper = new QueryWrapper<>();
		entityQueryWrapper.lambda().eq(SpareEntity::getSpareGuid, guid);
		entityQueryWrapper.lambda().eq(SpareEntity::getIsDeleted, 0);
		SpareEntity spareEntities = spareMapper.selectOne(entityQueryWrapper);
		spareEntities.setState(DictBizCache.getValue(MainBizEnum.STATE.getCode(), spareEntities.getState()));

		SpareHomeVO spareHomeVO = BeanUtil.copyProperties(spareEntities, SpareHomeVO.class);
		spareHomeVO.setPicUrl(receiveLogMapper.selectPic(spareHomeVO.getSpareType(), spareHomeVO.getSpareModel()));
		spareHomeVO.setDeptCodeZh(sysClient.getDeptNameByDeptCode(spareHomeVO.getDeptCode()).getData());
		spareHomeVO.setSpareType(DictBizCache.getValue(MainBizEnum.DEVICE_TYPE.getCode(), spareHomeVO.getSpareType()));
		List<ModelEntity> spareModelList = modelService.list(new LambdaQueryWrapper<ModelEntity>()
			.eq(ModelEntity::getType, MainBizEnum.DEVICE_TYPE_3.getCode()));
		for (ModelEntity spare : spareModelList) {
			if (spareHomeVO.getSpareModel().equals(spare.getCode())) {
				spareHomeVO.setSpareModel(spare.getName());
			}
		}
		List<ModelEntity> factoryModelList = modelService.list(new LambdaQueryWrapper<ModelEntity>()
			.eq(ModelEntity::getType, MainBizEnum.DEVICE_TYPE_99.getCode()));
		for (ModelEntity factory : factoryModelList) {
			if (spareHomeVO.getFactoryCode().equals(factory.getCode())) {
				spareHomeVO.setFactoryCode(factory.getName());
			}
		}
		return R.data(spareHomeVO);
	}

	@Override
	public R changeStatus(String position, String equipmentCabinetGuid, String status) {
		boolean bool = storeRoomService.checkOperation(equipmentCabinetGuid, AuthUtil.getUserId());
		if (bool) {
			return R.fail("当前用户没有权限！");
		}
		QueryWrapper<BatteryEntity> entityQueryWrapper = new QueryWrapper<>();
		entityQueryWrapper.lambda().eq(BatteryEntity::getPosition, position);
		entityQueryWrapper.lambda().eq(BatteryEntity::getEquipmentCabinetGuid, equipmentCabinetGuid);
		entityQueryWrapper.lambda().eq(BatteryEntity::getIsDeleted, 0);
		BatteryEntity batteryEntity = batteryMapper.selectOne(entityQueryWrapper);

		QueryWrapper<EquipmentCabinetEntity> equipmentCabinetEntityQueryWrapper = new QueryWrapper<>();
		equipmentCabinetEntityQueryWrapper.lambda().eq(EquipmentCabinetEntity::getEquipmentCabinetGuid, equipmentCabinetGuid);
		equipmentCabinetEntityQueryWrapper.lambda().eq(EquipmentCabinetEntity::getIsDeleted, 0);
		EquipmentCabinetEntity equipmentCabinetEntity = equipmentCabinetMapper.selectOne(equipmentCabinetEntityQueryWrapper);

		NettyChargeDTO dto = new NettyChargeDTO();
		dto.setPosition(position);
		dto.setCabinetSnCode(equipmentCabinetEntity.getSn());
		if ("0".equals(status)) {
			dto.setChargeType("0");
			batteryEntity.setState("1");
		} else if ("1".equals(status)) {
			dto.setChargeType("1");
			batteryEntity.setState("2");
		}
		nettyBuinessClient.chargeOrder(dto.getCabinetSnCode(),dto.getPosition(),dto.getChargeType());
		batteryMapper.update(batteryEntity, entityQueryWrapper);
		return R.success("成功");
	}

	@Override
	public R receive(List<ReceiveLogEntity> receiveLogEntityList) {
		if (receiveLogEntityList.size() > 0) {
			String roomName = receiveLogEntityList.get(0).getRoomName();
			String receiveType = receiveLogEntityList.get(0).getReceiveType();
			String picUrl = receiveLogEntityList.get(0).getPicUrl();
			String storeRoomGuid = receiveLogEntityList.get(0).getStoreRoomGuid();
			String equipmentCabinetGuid = receiveLogEntityList.get(0).getEquipmentCabinetGuid();
			//先按领用归还分组 0领取 1归还
			List<ReceiveLogEntity> receiveList = receiveLogEntityList.stream().filter(e -> 0 == e.getOperatorType()).collect(Collectors.toList());
			//按型号分组
			Map<String, List<ReceiveLogEntity>> listMap = receiveList.stream().collect(Collectors.groupingBy(t -> t.getReceiveModel()));
			//根据key找value
			Set<String> set = listMap.keySet(); //取出所有的key值
			for (String key : set) {
				ReceiveLogEntity receiveLogEntity = new ReceiveLogEntity();
				//领取操作下同一型号
				List<ReceiveLogEntity> receiveLogEntities = listMap.get(key);
				receiveLogEntity.setReceiveOperator(AuthUtil.getUserId());
				String UUID = IdUtil.simpleUUID();
				receiveLogEntity.setReceiveGuid(UUID);
				receiveLogEntity.setRoomName(roomName);
				receiveLogEntity.setReceiveType(receiveType);
				receiveLogEntity.setReceiveModel(key);

				List<String> deviceGuids = new ArrayList<>();
				List<String> snCodes = new ArrayList<>();
				receiveLogEntities.forEach(e -> {
					deviceGuids.add(e.getDeviceGuid());
					snCodes.add(e.getSnCode());

					//待维修设备需在维修表系新增一条记录
					if (MainBizEnum.OUT_REASON_2.getCode().equals(e.getReason())) {
						MaintanceLogEntity maintanceLogEntity = new MaintanceLogEntity();
						maintanceLogEntity.setMaintanceGuid(IdUtil.simpleUUID());
						maintanceLogEntity.setDeviceType(e.getReceiveType());
						maintanceLogEntity.setDeviceModel(e.getReceiveModel());
						maintanceLogEntity.setDeviceGuid(e.getDeviceGuid());
						maintanceLogEntity.setSnCode(e.getSnCode());
						maintanceLogEntity.setRfidGuid(findRFID(e.getReceiveType(), e.getDeviceGuid()));
						maintanceLogEntity.setFactoryCode(findFactory(e.getReceiveType(), e.getDeviceGuid()));
						maintanceLogEntity.setStatus(MainBizEnum.MAINTANCE_STATUS_0.getCode());
						maintanceLogEntity.setReceiveGuid(UUID);
						maintanceLogEntity.setStoreRoomGuid(e.getStoreRoomGuid());
						maintanceLogMapper.insert(maintanceLogEntity);
						if (MainBizEnum.DEVICE_TYPE_1.getCode().equals(e.getReceiveType())) {
							UavEntity uav = uavMapper.selectOne(new LambdaQueryWrapper<UavEntity>()
								.eq(UavEntity::getUavGuid, e.getDeviceGuid()));
							int maintainCount = uav.getMaintainCount().intValue() + 1;
							uav.setMaintainCount(maintainCount);
							uav.setStatus(MainBizEnum.DEVICE_STATUS_1.getCode());
							uav.setState(MainBizEnum.STATE_0.getCode());
							QueryWrapper<RfidBindEntity> rfidBindEntityQueryWrapper = new QueryWrapper<>();
							rfidBindEntityQueryWrapper.lambda().eq(RfidBindEntity::getDeviceGuid,e.getDeviceGuid());
							RfidBindEntity bindEntity = rfidBindService.getOne(rfidBindEntityQueryWrapper);
							uav.setPosition(bindEntity.getPosition());
							uavMapper.updateById(uav);
							log.info(uav.getUavName() + "已出库");
							String redisKey = BasicConstant.WARN_PREFIX + uav.getUavType() + BasicConstant.HG + uav.getUavModel();
							String count = allcoreRedis.get(redisKey);
							if (StringUtil.hasText(count)) {
								JSONObject json = JSON.parseObject(count);
								if (maintainCount > json.getInteger(BasicConstant.MAINTANCE)) {
									WarnDeviceDTO dto = new WarnDeviceDTO();
									dto.setDeviceModel(uav.getUavModel());
									dto.setDeviceType(uav.getUavType());
									dto.setPosition("出库");
									dto.setStoreRoomGuid(storeRoomGuid);
									dto.setDeviceNo(uav.getUavCode());
									dto.setWarnDeviceType(MainBizEnum.WARN_STATE_2.getCode());
									dto.setMaintanceCount(maintainCount);
									warnDeviceService.add(dto);
								}
							}
						} else if (MainBizEnum.DEVICE_TYPE_2.getCode().equals(e.getReceiveType())) {
							BatteryEntity battery = batteryMapper.selectOne(new LambdaQueryWrapper<BatteryEntity>()
								.eq(BatteryEntity::getBatteryGuid, e.getDeviceGuid()));
							int maintainCount = battery.getMaintainCount().intValue() + 1;
							battery.setMaintainCount(maintainCount);
							battery.setStatus(MainBizEnum.DEVICE_STATUS_1.getCode());
							battery.setState(MainBizEnum.STATE_0.getCode());
							batteryMapper.updateById(battery);
							log.info(battery.getBatteryName() + "已出库");
							String redisKey = BasicConstant.WARN_PREFIX + battery.getBatteryType() + BasicConstant.HG + battery.getBatteryModel();
							String count = allcoreRedis.get(redisKey);
							if (StringUtil.hasText(count)) {
								JSONObject json = JSON.parseObject(count);
								if (maintainCount > json.getInteger(BasicConstant.MAINTANCE)) {
									WarnDeviceDTO dto = new WarnDeviceDTO();
									dto.setDeviceModel(battery.getBatteryModel());
									dto.setDeviceType(battery.getBatteryType());
									dto.setPosition("出库");
									dto.setStoreRoomGuid(storeRoomGuid);
									dto.setDeviceNo(battery.getBatteryCode());
									dto.setWarnDeviceType(MainBizEnum.WARN_STATE_2.getCode());
									dto.setMaintanceCount(maintainCount);
									warnDeviceService.add(dto);
								}
							}
						} else if (MainBizEnum.DEVICE_TYPE_3.getCode().equals(e.getReceiveType())) {
							SpareEntity spare = spareMapper.selectOne(new LambdaQueryWrapper<SpareEntity>()
								.eq(SpareEntity::getSpareGuid, e.getDeviceGuid()));
							int maintainCount = spare.getMaintainCount().intValue() + 1;
							spare.setMaintainCount(maintainCount);
							spare.setStatus(MainBizEnum.DEVICE_STATUS_1.getCode());
							spare.setState(MainBizEnum.STATE_0.getCode());
							QueryWrapper<RfidBindEntity> rfidBindEntityQueryWrapper = new QueryWrapper<>();
							rfidBindEntityQueryWrapper.lambda().eq(RfidBindEntity::getDeviceGuid,e.getDeviceGuid());
							RfidBindEntity bindEntity = rfidBindService.getOne(rfidBindEntityQueryWrapper);
							spare.setPosition(bindEntity.getPosition());
							spareMapper.updateById(spare);
							log.info(spare.getSpareName() + "已出库");
							String redisKey = BasicConstant.WARN_PREFIX + spare.getSpareType() + BasicConstant.HG + spare.getSpareModel();
							String count = allcoreRedis.get(redisKey);
							if (StringUtil.hasText(count)) {
								JSONObject json = JSON.parseObject(count);
								if (maintainCount > json.getInteger(BasicConstant.MAINTANCE)) {
									WarnDeviceDTO dto = new WarnDeviceDTO();
									dto.setDeviceModel(spare.getSpareModel());
									dto.setDeviceType(spare.getSpareType());
									dto.setPosition("出库");
									dto.setStoreRoomGuid(storeRoomGuid);
									dto.setDeviceNo(spare.getSpareCode());
									dto.setWarnDeviceType(MainBizEnum.WARN_STATE_2.getCode());
									dto.setMaintanceCount(maintainCount);
									warnDeviceService.add(dto);
								}
							}
						}
					} else if (MainBizEnum.OUT_REASON_3.getCode().equals(e.getReason())) {
						if (MainBizEnum.DEVICE_TYPE_1.getCode().equals(e.getReceiveType())) {
							UavEntity uav = uavMapper.selectOne(new LambdaQueryWrapper<UavEntity>()
								.eq(UavEntity::getUavGuid, e.getDeviceGuid()));
							uav.setStatus(MainBizEnum.DEVICE_STATUS_2.getCode());
							uav.setState(MainBizEnum.STATE_0.getCode());
							uavMapper.updateById(uav);
						} else if (MainBizEnum.DEVICE_TYPE_2.getCode().equals(e.getReceiveType())) {
							BatteryEntity battery = batteryMapper.selectOne(new LambdaQueryWrapper<BatteryEntity>()
								.eq(BatteryEntity::getBatteryGuid, e.getDeviceGuid()));
							battery.setStatus(MainBizEnum.DEVICE_STATUS_2.getCode());
							battery.setState(MainBizEnum.STATE_0.getCode());
							batteryMapper.updateById(battery);
						} else if (MainBizEnum.DEVICE_TYPE_3.getCode().equals(e.getReceiveType())) {
							SpareEntity spare = spareMapper.selectOne(new LambdaQueryWrapper<SpareEntity>()
								.eq(SpareEntity::getSpareGuid, e.getDeviceGuid()));
							spare.setStatus(MainBizEnum.DEVICE_STATUS_2.getCode());
							spare.setState(MainBizEnum.STATE_0.getCode());
							spareMapper.updateById(spare);
						}
					}
				});

				receiveLogEntity.setDeviceGuid(String.join(",", deviceGuids));
				receiveLogEntity.setNumber(receiveLogEntities.size());
				receiveLogEntity.setReceiveTime(new Date());
				receiveLogEntity.setPicUrl(picUrl);
				receiveLogEntity.setOperatorType(0);
				receiveLogEntity.setStoreRoomGuid(storeRoomGuid);
				receiveLogEntity.setReason("正常");
				receiveLogEntity.setEquipmentCabinetGuid(equipmentCabinetGuid);
				receiveLogEntity.setSnCode(String.join(",", snCodes));
				receiveLogEntity.setIsDeleted(0);
				receiveLogMapper.insertSelective(receiveLogEntity);
			}

			List<ReceiveLogEntity> returnList = receiveLogEntityList.stream().filter(e -> 1 == e.getOperatorType()).collect(Collectors.toList());
			Map<String, List<ReceiveLogEntity>> returnMap = returnList.stream().collect(Collectors.groupingBy(t -> t.getReceiveModel()));
			Set<String> strings = returnMap.keySet(); //取出所有的key值
			for (String key : strings) {
				ReceiveLogEntity receiveLogEntity = new ReceiveLogEntity();
				//归还操作下同一型号
				List<ReceiveLogEntity> receiveLogEntities = returnMap.get(key);
				receiveLogEntity.setReceiveOperator(AuthUtil.getUserId());
				receiveLogEntity.setReceiveGuid(IdUtil.simpleUUID());
				receiveLogEntity.setRoomName(roomName);
				receiveLogEntity.setReceiveType(receiveType);
				receiveLogEntity.setReceiveModel(key);

				List<String> deviceGuids = new ArrayList<>();
				List<String> snCodes = new ArrayList<>();
				receiveLogEntities.forEach(e -> {
					deviceGuids.add(e.getDeviceGuid());
					snCodes.add(e.getSnCode());
					if (MainBizEnum.DEVICE_TYPE_1.getCode().equals(e.getReceiveType())) {
						UavEntity uav = uavMapper.selectOne(new LambdaQueryWrapper<UavEntity>()
							.eq(UavEntity::getUavGuid, e.getDeviceGuid()));
						uav.setStatus(MainBizEnum.DEVICE_STATUS_0.getCode());
						uav.setState(MainBizEnum.STATE_1.getCode());
						uavMapper.updateById(uav);
						log.info(uav.getUavName() + "已入库");
					} else if (MainBizEnum.DEVICE_TYPE_2.getCode().equals(e.getReceiveType())) {
						BatteryEntity battery = batteryMapper.selectOne(new LambdaQueryWrapper<BatteryEntity>()
							.eq(BatteryEntity::getBatteryGuid, e.getDeviceGuid()));
						battery.setStatus(MainBizEnum.DEVICE_STATUS_0.getCode());
						battery.setState(MainBizEnum.STATE_1.getCode());
						batteryMapper.updateById(battery);
						log.info(battery.getBatteryName() + "已入库");
					} else if (MainBizEnum.DEVICE_TYPE_3.getCode().equals(e.getReceiveType())) {
						SpareEntity spare = spareMapper.selectOne(new LambdaQueryWrapper<SpareEntity>()
							.eq(SpareEntity::getSpareGuid, e.getDeviceGuid()));
						spare.setStatus(MainBizEnum.DEVICE_STATUS_0.getCode());
						spare.setState(MainBizEnum.STATE_1.getCode());
						spareMapper.updateById(spare);
						log.info(spare.getSpareName() + "已入库");
					}
				});

				receiveLogEntity.setDeviceGuid(String.join(",", deviceGuids));
				receiveLogEntity.setNumber(receiveLogEntities.size());
				receiveLogEntity.setReceiveTime(new Date());
				receiveLogEntity.setPicUrl(picUrl);
				receiveLogEntity.setOperatorType(1);
				receiveLogEntity.setStoreRoomGuid(storeRoomGuid);
				receiveLogEntity.setReason("");
				receiveLogEntity.setEquipmentCabinetGuid(equipmentCabinetGuid);
				receiveLogEntity.setSnCode(String.join(",", snCodes));
				receiveLogEntity.setIsDeleted(0);
				receiveLogMapper.insertSelective(receiveLogEntity);
			}
		}
		return R.success("操作完成");
	}

	@Override
	public R storeList() {
		AllcoreUser user = AuthUtil.getUser();
		List<String> strings = baseMapper.selectDepts(user.getDeptId());
		strings.add(baseMapper.selectUserDept(user.getDeptId()));
 		return R.data(baseMapper.storeList(strings));
	}

	@Override
	public R receive(String equipmentCabinetGuid) {
		DeviceStatusVO deviceStatusVOS = new DeviceStatusVO();
		List<DeviceReceiveVO> deviceChildVOList;

		DeviceReceiveVO deviceChildVO = new DeviceReceiveVO();
		deviceChildVO.setEquipmentCabinetGuid("28456a4f0cfb4f0699d727dfb2c13d10");
		deviceChildVO.setOperatorType(0);
		deviceChildVO.setReason("领用原因");
		deviceChildVO.setReceiveModel("0");
		deviceChildVO.setReceiveType("1");
		deviceChildVO.setDeviceUrl("123");
		deviceChildVO.setPicUrl("123");
		deviceChildVO.setRoomName("库房名称");
		deviceChildVO.setStoreRoomGuid("1234");

		deviceChildVOList = new ArrayList<>();
		deviceChildVOList.add(deviceChildVO);

		deviceStatusVOS.setReceiveChildVOS(deviceChildVOList);

		deviceChildVO = new DeviceReceiveVO();
		deviceChildVO.setEquipmentCabinetGuid("28456a4f0cfb4f0699d727dfb2c13d10");
		deviceChildVO.setOperatorType(1);
		deviceChildVO.setReceiveModel("0");
		deviceChildVO.setReceiveType("1");
		deviceChildVO.setDeviceUrl("123");
		deviceChildVO.setPicUrl("123");
		deviceChildVO.setRoomName("库房名称");
		deviceChildVO.setStoreRoomGuid("1234");

		deviceChildVOList = new ArrayList<>();
		deviceChildVOList.add(deviceChildVO);

		deviceStatusVOS.setReturnChildVOS(deviceChildVOList);

		wsService.sendReceiveAndRevertInfo(AuthUtil.getUserId(), deviceStatusVOS);

		return R.data(deviceStatusVOS);
	}

	@Override
	public R personDevice(String personId) {
		List<ReceiveLogEntity> receiveLogEntityList = receiveLogMapper.selectReceiveType(personId);
		Iterator<ReceiveLogEntity> iterator = receiveLogEntityList.iterator();
		while (iterator.hasNext()) {
			ReceiveLogEntity receiveLogEntity = iterator.next();
			List<ReceiveLogEntity> receiveLogEntities = receiveLogMapper.selectReturnType(personId, receiveLogEntity.getReceiveTime(), receiveLogEntity.getDeviceGuid());
			if (receiveLogEntities.size() > 0) {
				iterator.remove();
			}
		}
		List<DeviceChildVO> deviceStatusVOS = new ArrayList<>();
		receiveLogEntityList.forEach(e -> {
			DeviceChildVO deviceChildVO = new DeviceChildVO();
			deviceChildVO.setDeviceType(DictBizCache.getValue(MainBizEnum.DEVICE_TYPE.getCode(), e.getReceiveType()));
			deviceChildVO.setDeviceGuid(e.getDeviceGuid());
			deviceChildVO.setDeviceName(findName(e.getReceiveType(), e.getDeviceGuid()));
			deviceChildVO.setDevicePic(receiveLogMapper.selectPic(e.getReceiveType(), e.getReceiveModel()));
			deviceChildVO.setReason(e.getReason());
			deviceChildVO.setRfid(findRFID(e.getReceiveType(), e.getDeviceGuid()));
			deviceStatusVOS.add(deviceChildVO);
		});

		return R.data(deviceStatusVOS);
	}

	@Override
	public R userFinger() {


//		List<UserFinger> userFingers = baseMapper.selectUserFinger();
//		List<UserFinger> result = userFingers.stream().filter(item -> StrUtil.isNotEmpty(item.getFingerPrintOne()) || StrUtil.isNotEmpty(item.getFingerPrintTwo())).collect(Collectors.toList());

		String t1 = "Si9TUzIxAAADbG8ECAUHCc7QAAAvbZEBAAAAg5EbhmzZAOQPlAAFAGNihACuAF8PmQCybFQPXAClACcPjWwoAUYNjgD/AUtiRgB4AN0NnQBWbF8LnADSANQPaGy9AOQPrwAHABhhRgDhANEPbQAQbTIETwAXAQoPe2w8AcwOUgCEAc1imwBOAHMOvgDDbOUPgAD+ACUOnWwIASkBmQDWATFtwwCpABoNXwA1bT4NYgB3AC0KnWxUAAAOxACaAb9kkNZNA3WHlFHGbqB+dX1FAkt87xtEcT5x4I8MA71ufABdCQ0W6Qv6aLR+ofd2ghb9WRV3gh/vIbEgLlaWqAA1kUUHDA7q00MAk+2DfS4G7mDMALLt8gOqApyesIaaiK6NawZxb66LOHaLF7KPKmxwhZ2Gsrh7sGWSrft5+OZI5QtObKDuwgDXvQIKt5fk+bp1GQ+LjdxkLY7vGwOnon9cG2Z7cNTEBuU0Am4HGoIGAJz6eoitCAB4QnfEOMLAEAsAsUaAgAXBk2EBmE93d8MFWsEJBACZVnfCuQQDzVYGwv0HAJBYYxRwCQDRZYanlsJ/AddwjMFxB8P8rYXBwf90CcVdeQ7Ae2kFAES+XMKswRQA3nyMOoOPFVhqVRUA6EuXbOyIi3N2wAzF45f2wv6dwovD0ADi95uLksJ8/1bAVawOAN6gl4RUg8GvTwcAvaeMB5LBaQHFqRP+QsAAxsIb/zgKAIR1YsGsf8FaFwDtd5zBMJrBiHbBwAVdwWgBXLNXcwzFa7qy/v7//P/+O8AwYAF9veT//Tv7NlsKAGnBU8AFdFCsCgCSw2SJBcH9rl0FALHDDzk2B2yoxICsBAB0yBlCBACc0wn95gUTvQYwRwYARiBQw6xWEwDq7qkFdcH5g3jBSw0ARP7Vkf/3/f39wTr/bn4Bx+mpw8EDxcGuw3Nt/lYF1ZoLCpYIEKkKKfBPC3yhDDTARzvCEJl4NcBG/wcQbBUzk03ABBDqHHhcFmzf6avBwZsBwcetwsDAXXD+wBBNcU7BTggQmfW30KrDVQUQmDiF/1BpEZ04Ov87yhB1V8f+/8D8/Tn1/JPBwD4FEIv5SUlpEZE8QP490hDdLbBlwcDBwwTFxK7FwMHAwMEF/sOTBRB3Q0xA0hBaBN3DcsBA/Tv5+ZE/W/zB";

		String t2 = "SsxTUzIxAAADj48ECAUHCc7QAAAvjpEBAAAAg7IbdY/MAO0PbgB0AO+AhgDvACQNlAChj10PNADFAB8PcY8fAUsMfQDpAUGC1ACiAJoP9gAojtMMiwDMANAPXI+qAOsPowB/AByAVACVAOcPSgAPjjYLNgADAREPwI8EAbEPWADoAdCBnAA0AAQMQwC0j2YPegCjAKIPao/yAOUNegDOATGFtwCmAB8PtAAvjk8NYgBoACsPS49iAOQOgADoAPqBkNlti0kD4AByCqCGkuzaQGv08vs8cDpwhoNW8YB4ePxZCQUZBffmdv8QYnxXfpo3sY5zArOH6SnkslF3pAAtkEUGhAPmNMsM5YzJ/nv+zooDFLbt8gZm8dCNSn1fj0+DaIaZB6+RBgzjGy4jUtnEAqV+dX2AAo7wwLeWwGr6ZOlScvRPwQNehBK5M4D0BLF+GQ8aCNwYpPvT/duLV/TcgOoXYNjFAuUwAo0oHzsLAJfnepPtaAYAbylxucIMj8cwhnn//1ZxWooBnjX9NRLF5FIVxf7Cb4TCsv5ugQHrXJBSwQTBgE7F/g4AZGcu//xw/P3B////OlkXj/FqlsB0wQfAwAlYZMIWAPW9mmn3wJPDVnvBng8D2JDiM/8u/zr//dsRAFmV5v/2wCFw//7BTgkAl5lnDFLBDABWoCE7N3D+NhcA9qBsw07zjYTBeMBk0gD9L6qLwImGfgbAw/FlBwCxo5AHw3qLAU+kXngNxXmm5nZ2ckoXAHymFHExwcDZyP4EwcFNwcHCwsLCSAkD3qlX/2f/fcwAuyUf//8+wf7SAPQjpcB4gIjBBId9T2UKAF2wXK/AcE8OAHCw6/46/P68Nv9kDACGf23ATmtiwf8LAGa9Gab+VVYKAHcJ4v50+zNEDACMCwn4psA2bAkAehXt/Hf8/v7/wRjF79Ik/pZvlsPAU1hzcAsActRedAXCPYUBjO8i/TiT/wyPavPa/f345FPCcMH8FhA6AhZVSHPA/P0u/v8F//zYBhCACzdU9gUTSQo3SQcQestaw01kBxCQDjcFS/yVEeIotF1qBMOYTsTBwnTAwLfBDJ9WK9NP/vg9/v3FMQwQXSvXBf/+evfBwP7/wMAQbaNOwf7/BBC2LkC1BBB6LkNBwBB8oTz//v4DEJIzUE8WEDdC2sKg/1Ry/fv8/f/+BcDDTzwUED1J4FFU/nD9+fv+//8FwFc=";
		String t3 = "SxBTUzIxAAACU1MECAUHCc7QAAAuUpEBAAAAgv4QuFPEAPEJygAWAHFR0QCcAHQOSwCjU+QO0QAAAYILxlM3APgKwAALAAFQ0ADRAC8CJwDLUy0ImwDmAJAMllPyANUMugBaAPRd2QCoABUPAADoU0sFiwCWAKcOBVKTABYPuPNogaeUAUagc62F8GCL0WKEUIea837256321PsYx4cKB93IJReQk4XUAb7bf0AFxf3Fuxcpg9MzDxZXgYBijZGFhIaa4M4G0KD4c6PpAdphBswfViwmbENz2wEipsW8sx4gKAEBthOPWwH9IYDBwAZbB1ICKoB0BwHSN4vXZQQAvzh0vQ0DTEeTg8RDwqEOA3BQk8LAwcOiwMOTbg4BKF6TBYDAk4R7DwEoZ1X/idrBwmXBDwHvcpE5isNqwGoPxCZ4xHaEh8LAYNUBJtKXd3XCacAFwMBZAbmD8P7/9MA6VwGyiG2LCcW6iqcyN/0RASJPl3WSwMKLeVoIxbOJMf7B/8JtA8WJmjPBCQC8nfA4Kv2uwQkAtaRkqlLDVgHbpgz+Ks4A0ft1w35vRgbF265D//02EQEdaqBrwcOWZnMSAdi9q5D+w8CXw3exWwVTvcTp/fj/5gMSrAdJwAoA5Awp/BP+/jwJANcVJv94RxIBFtKrBHnAlsDCi8HAcM0AyoBsw8RV/wnF1NZ+KcA7CwCeJNpITvwqBAEC7PEpBVPK5T0rQQbFw+QFd/8GALrnllPDVQHF7EY+/sAQ0lBNwTMFENXGRsJrEhCIGNx2+P3/qx81RQ==";
		String t4 = "SiBTUzIxAAADY2IECAUHCc7QAAAvYpEBAAAAg44ZfWPAAGQPcgAuAORtgQD/AEwJQQAKYkALygDlAOAPgmNiAH8PegCaAAFsUwBdAO4PQAAyY/IOiQDIAMwPeWPvAP0JrwBjABNsOwDIAFcPVgAkYkAPIgCgACcN4WOhABEKNADhAc5rZADHAOsPXwDgYyINQADUAB4PYmMOAU8PTgDbAcxszwAMAS4PHAAFYikPQQBTACwLNzyUe2p/qO4EnsXKZQ/pUd1mNffyJG8Biv2a+hoEmx2KkMeNlIbHFkhzKAWW7ptwHvDQFvIHEKei6D8hE3m0sbW/1wmqAdzNWX8qb9d0KwHtlNL2y4zLC3L77GK+798LqQNmgXiUjIua5coGzxzRSr/aVIea8n72luEfBS4RlYLsB6DwPQsOCsPvAf8JZu4Hrf2j62ZvEkWBAyAxAQLQHh5mAX0fccFfyQCZQXyDWMN8B8WPKhJjfQQAti1MlwhjxTmMg29w1QDJIYdmwsDCdKpVCGPYUZPDWYMHwAdjP1ZibxIAJF6POcN8w0KLVM8AeDz8/8H9wEwFCQPjYnTAw2dV1wDkCo1KicDEwAXAwhXAFADsf5AFwMIKwsDCwH5brhQDkoiPwGLBgAd0cBEUAPeUkGUHaYihesB0wgMAIaETnRcA86GQwZ93g6D/fv/D/4fTAPvBoZR3dcHBBv/DP3EHALGnD4RbGmP7tprC/oOyjMGjbcFSwMDCyQB4o2iMwH7AUMkAgKP2/f3///4FU8NyAfrAnMFcTMCSG8MMAH3FZgTDZJzC/v/BDwCix+WdKylK/sHCwwA6qlZzwQoAiw4DJExIFwD6y5qnwcKgZsLCwcLBOsHAncNqBwCd5dsrNmYBzeYiwFTcAPiFoWTBwcL/XsHB88D/wmt2C8V16r39+v77wP46/sJqAX3q6/n6O0D8agGU7yD9OAU1CmN38lfCwcAFwSlpAYz2esjDBW0vdAH/9qDAwK91gaHDh35sDABR9yqeUjD/IQsAS/0qUsP9//79/c0QgmJSw/9k/gTV5h9IXAcQgg1GBVv+axGHDTo2LsMQY3FNWv0UEFDez1tQ/vz7/v/+OMHDoTUGEJEqQzrBIWURlio6M/7QENFKsf+BgsKwsVFuZxHLcHd4";
		String test = "fali";

		if (JavaToBiokey.NativeToProcess(t1, t2)) {
			test = "success";
		}

		System.out.printf("verify 10.0, t1-t2 result=" + test + "\n");

		test = "fali";

		if (JavaToBiokey.NativeToProcess(t2, t3)) {
			test = "success";
		}

		System.out.printf("verify 10.0, t2-t3result=" + test + "\n");

		test = "fali";

		if (JavaToBiokey.NativeToProcess(t4, t3)) {
			test = "success";
		}

		System.out.printf("verify 10.0, t4-t3result=" + test + "\n");


		return R.status(true);
	}


	private String findRFID(String deviceType, String deviceGuid) {
		if ("1".equals(deviceType)) {
			QueryWrapper<UavEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(UavEntity::getUavGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(UavEntity::getIsDeleted, 0);
			UavEntity uavEntity = uavMapper.selectOne(entityQueryWrapper);
			return uavEntity.getRfidGuid();
		} else if ("2".equals(deviceType)) {
			QueryWrapper<BatteryEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(BatteryEntity::getBatteryGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(BatteryEntity::getIsDeleted, 0);
			BatteryEntity batteryEntity = batteryMapper.selectOne(entityQueryWrapper);
			return batteryEntity.getRfidGuid();
		} else if ("3".equals(deviceType)) {
			QueryWrapper<SpareEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(SpareEntity::getSpareGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(SpareEntity::getIsDeleted, 0);
			SpareEntity spareEntity = spareMapper.selectOne(entityQueryWrapper);
			return spareEntity.getRfidGuid();
		}
		return "无该型号rfid";
	}

	private String findFactory(String deviceType, String deviceGuid) {
		if ("1".equals(deviceType)) {
			QueryWrapper<UavEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(UavEntity::getUavGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(UavEntity::getIsDeleted, 0);
			UavEntity uavEntity = uavMapper.selectOne(entityQueryWrapper);
			return uavEntity.getFactoryCode();
		} else if ("2".equals(deviceType)) {
			QueryWrapper<BatteryEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(BatteryEntity::getBatteryGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(BatteryEntity::getIsDeleted, 0);
			BatteryEntity batteryEntity = batteryMapper.selectOne(entityQueryWrapper);
			return batteryEntity.getFactoryCode();
		} else if ("3".equals(deviceType)) {
			QueryWrapper<SpareEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(SpareEntity::getSpareGuid, deviceGuid);
			entityQueryWrapper.lambda().eq(SpareEntity::getIsDeleted, 0);
			SpareEntity spareEntity = spareMapper.selectOne(entityQueryWrapper);
			return spareEntity.getFactoryCode();
		}
		return "无厂家";
	}

	@Override
	public R saveFinger(String guid, String userId) {
		User user = baseMapper.selectUserById(userId);
		if (StrUtil.isBlank(user.getFingerPrintOne())) {
			baseMapper.updateSaveFinger(guid, userId);
		} else {
			baseMapper.updateTwoFinger(guid, userId);
		}
		return R.success("绑定成功");
	}


	@Override
	public R loginFinger(String fingerGuid) {
		QueryWrapper<DeviceAuthInfoEntity> deviceAuthInfoEntityQueryWrapper = new QueryWrapper<>();
		deviceAuthInfoEntityQueryWrapper.lambda().eq(DeviceAuthInfoEntity::getIsDeleted, 0);
		List<DeviceAuthInfoEntity> deviceAuthInfoEntities = deviceAuthInfoMapper.selectList(deviceAuthInfoEntityQueryWrapper);

		User user = null;
		NacosConfig config = SpringContextUtil.getBean(NacosConfig.class);
		String url = config.getUrl() + "/finger/judge";
		HashMap<String, String> hashMap = Maps.newHashMap();
		for (DeviceAuthInfoEntity e : deviceAuthInfoEntities) {
			if (!StrUtil.isBlank(e.getFingerPrintOne())) {
				String finger1 = e.getFingerPrintOne();
				hashMap.put("fingerGuidIn", finger1);
				hashMap.put("fingerGuid", fingerGuid);
				String param = JSON.toJSONString(hashMap);
				String result = HttpUrlConnectionToInterface.doPost(url, param);
				if ("true".equals(result)) {
					user = baseMapper.selectUserById(e.getUserId());
					break;
				}
			}
			if (!StrUtil.isBlank(e.getFingerPrintTwo())) {
				String finger2 = e.getFingerPrintTwo();
				hashMap.put("fingerGuidIn", finger2);
				hashMap.put("fingerGuid", fingerGuid);
				String param = JSON.toJSONString(hashMap);
				String result = HttpUrlConnectionToInterface.doPost(url, param);
				if ("true".equals(result)) {
					user = baseMapper.selectUserById(e.getUserId());
					break;
				}
			}
		}
		String ticket = "jk";
		//body
		MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
		/*登录账号*/
		if (null == user) {
			return R.fail("无该指纹用户信息");
		}
		String account = user.getAccount();
		body.add("username", account);
		/*密码需要MD5加密传输  Jsepc01!  32位 小写  对应 95c75b126b7382bdb0cedeafab114c1a*/
		//ticket方式密码随便填写但是不能为空

		body.add("password", "95c75b126b7382bdb0cedeafab114c1a");
		body.add("grant_type", "password");
		body.add("scope", "all");

		MultiValueMap<String, String> headers = new LinkedMultiValueMap<>();
		headers.add("Authorization", "Basic " + "c2FiZXI6c2FiZXJfc2VjcmV0");
		//默认值947313
		headers.add("Tenant-Id", "000000");
		headers.add("Content-Type", "x-www-form-urlencoded");
		if (StringUtils.isNotBlank(ticket)) {
			headers.add("ticket", ticket);
			body.add("ticket", ticket);
		}
		//调用auth服务
		Object token = authClient.postAccessToken(body, headers);
		if (token instanceof String && token.toString().contentEquals("获取token失败")) {
			return R.fail("登陆失败");
		}
		LinkedHashMap<String, Object> map = (LinkedHashMap<String, Object>) token;
		if (!"administrator".equals(String.valueOf(map.get("role_name")))){
			if (!visitInfoService.checkVisit(String.valueOf(map.get("dept_code")))){
				return R.fail("该用户没有访问权限");
			}
		}
		return R.data(map);
	}


	@Override
	public R getDoor(String equipmentCabinetGuid) {
		QueryWrapper<EquipmentCabinetEntity> equipmentCabinetEntityQueryWrapper = new QueryWrapper<>();
		equipmentCabinetEntityQueryWrapper.lambda().eq(EquipmentCabinetEntity::getEquipmentCabinetGuid, equipmentCabinetGuid);
		equipmentCabinetEntityQueryWrapper.lambda().eq(EquipmentCabinetEntity::getIsDeleted, 0);
		EquipmentCabinetEntity equipmentCabinetEntity = equipmentCabinetMapper.selectOne(equipmentCabinetEntityQueryWrapper);
		String state = allcoreRedis.get(equipmentCabinetEntity.getSn() + "_door_state").toString();
		if ("1".equals(state)) {
			return R.data("柜门未关闭");
		} else if ("0".equals(state)) {
			return R.data("已关闭");
		}
		return R.data("未查询到柜门状态");
	}

}
