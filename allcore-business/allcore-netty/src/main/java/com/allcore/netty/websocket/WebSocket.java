package com.allcore.netty.websocket;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

import javax.websocket.OnClose;
import javax.websocket.OnMessage;
import javax.websocket.OnOpen;
import javax.websocket.Session;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;

import com.allcore.netty.enums.MessageTypeEnum;
import com.allcore.netty.utils.WebSocketCache;

import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;

import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @Date 2019/11/29 9:41
 * @Description: 此注解相当于设置访问URL
 */
@Component
@Slf4j
@ServerEndpoint("/websocket/{userId}") //此注解相当于设置访问URL
public class WebSocket {

    private String userId;
    public Session session;

    //    private static CopyOnWriteArraySet<WebSocket> webSockets = new CopyOnWriteArraySet<>();
    private static Map<String, Session> sessionPool = new HashMap<String, Session>();

    @OnOpen
    public void onOpen(Session session, @PathParam(value = "userId") String userId) {
        try {
            this.userId = userId;
            this.session = session;
            sessionPool.put(userId, session);
            WebSocketCache.add(this);
            log.info("【websocket消息】有新的连接，总数为:" + WebSocketCache.getSize());
        } catch (Exception e) {
            log.error("websocket有连接出错->", e);
        }
    }

    @OnClose
    public void onClose() {
        try {
            WebSocketCache.remove(this);
            log.info("【websocket消息】连接断开，总数为:" + WebSocketCache.getSize());
        } catch (Exception e) {
            log.error("websocket关闭连接出错->", e);
        }
    }

    @OnMessage
    public void onMessage(String message) {
//    	log.info("【websocket消息】收到客户端心跳消息:"+message);
        Map<String, Object> result = new HashMap<String, Object>();
        result.put("type", MessageTypeEnum.MESSAGETYPE_HEART.getType());
        result.put("list", "");
        try {
            synchronized (session) {
                if (Objects.nonNull(session)) {
                    session.getBasicRemote().sendText(JSONObject.toJSONString(result));
                }
            }
        } catch (IOException e) {
            log.info("onMessage发消息时出错->{}", e);
        }
    }

    // 此为广播消息
    public void sendAllMessage(String message) {
        WebSocketCache.sendAllMessage(message);
    }

    // 此为单点消息
    public void sendOneMessage(String userId, String message) {
        Session session = sessionPool.get(userId);
        if (Objects.nonNull(session) && session.isOpen()) {
            try {
                log.info("【websocket消息】 单点消息:" + message);
                synchronized (session) {
                    session.getBasicRemote().sendText(message);
                }
            } catch (Exception e) {
                log.error("sendOneMessage此为单点消息异常->{}", e);
            }
        }
    }

    // 此为单点消息(多人)
    public void sendMoreMessage(String[] userIds, String message) {
        for (String userId : userIds) {
            Session session = sessionPool.get(userId);
            if (session != null && session.isOpen()) {
                try {
                    log.info("【websocket消息】 单点消息:" + message);
                    synchronized (session) {
                        session.getBasicRemote().sendText(message);
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                }
            }
        }
    }

}
