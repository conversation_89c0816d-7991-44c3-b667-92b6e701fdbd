package com.allcore.netty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.IoUtil;
import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.enums.BatteryModelEnum;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.common.utils.AllCoreAuthUtil;
import com.allcore.common.utils.RandomCodeUtils;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.netty.dto.BatteryDTO;
import com.allcore.netty.dto.InfoStatisticalDTO;
import com.allcore.netty.dto.ModelForSaveDTO;
import com.allcore.netty.entity.*;
import com.allcore.netty.mapper.BatteryMapper;
import com.allcore.netty.mapper.EquipmentCabinetFloorMapper;
import com.allcore.netty.mapper.EquipmentCabinetMapper;
import com.allcore.netty.mapper.ModelMapper;
import com.allcore.netty.service.BatteryService;
import com.allcore.netty.service.EquipmentCabinetCellService;
import com.allcore.netty.service.ModelService;
import com.allcore.netty.service.StoreRoomService;
import com.allcore.netty.vo.*;
import com.allcore.netty.wrapper.BatteryWrapper;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Font;
import org.apache.poi.ss.usermodel.IndexedColors;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;


@Service
public class BatteryServiceImpl extends ZxhcServiceImpl<BatteryMapper, BatteryEntity> implements BatteryService {

	@Resource
	private EquipmentCabinetMapper equipmentCabinetMapper;

	@Resource
	private ISysClient sysClient;

	@Resource
	private ModelService modelService;

	@Resource
	private EquipmentCabinetCellService cellService;

	@Resource
	private EquipmentCabinetFloorMapper equipmentCabinetFloorMapper;

	@Resource
	private ModelMapper modelMapper;

	@Resource
	@Lazy
	private StoreRoomService storeRoomService;

	@Override
	public BatteryStatisticalVO statistical(Integer isWarehouse, String deptCode, String storeRoomGUID) {
		//isWarehouse = 1 为在库的
		List<EquipmentCabinetEntity> entityList = null;
		if (!Objects.isNull(storeRoomGUID) && !"".equals(storeRoomGUID)) {
			entityList = equipmentCabinetMapper.selectList(new LambdaQueryWrapper<EquipmentCabinetEntity>().eq(EquipmentCabinetEntity::getStoreRoomGuid, storeRoomGUID));
		}
		BatteryStatisticalVO statisticalVo = new BatteryStatisticalVO();
		List<Map<String, Object>> list = this.baseMapper.statistical(isWarehouse, deptCode, entityList);
		int greaterOneHundred = 0, withinFifty = 0, withinOneHundred = 0;
		for (Map<String, Object> map : list) {
			if ("greaterOneHundred".equals(map.get("statistics"))) {
				greaterOneHundred = Integer.valueOf(map.get("num").toString());
			}
			if ("withinFifty".equals(map.get("statistics"))) {
				withinFifty = Integer.valueOf(map.get("num").toString());
			}
			if ("withinOneHundred".equals(map.get("statistics"))) {
				withinOneHundred = Integer.valueOf(map.get("num").toString());
			}
		}
		statisticalVo.setGreaterOneHundred(greaterOneHundred);
		statisticalVo.setWithinFifty(withinFifty);
		statisticalVo.setWithinOneHundred(withinOneHundred);
		return statisticalVo;
	}


	/**
	 * 电池信息统计
	 *
	 * @param roomList
	 * @return
	 */
	@Override
	public R infoStatistical(List<String> roomList) {
		if (CollectionUtil.isEmpty(roomList)) {
			throw new ServiceException("库房数据不能为空！");
		}
		QueryWrapper queryWrapper = new QueryWrapper();
		queryWrapper.in("netty_equipment_cabinet.store_room_guid", roomList);
		queryWrapper.eq("netty_equipment_cabinet.is_deleted", StringPool.ZERO);
		queryWrapper.eq("netty_battery.is_deleted", StringPool.ZERO);
		List<InfoStatisticalDTO> infoStatisticalTDOList = getBaseMapper().infoStatistical(queryWrapper);
		LineStatisticalVO vo = new LineStatisticalVO();
		Set<String> xAxisData = new HashSet<>();
		for (InfoStatisticalDTO infoStatisticalTDO : infoStatisticalTDOList) {
			xAxisData.add(infoStatisticalTDO.getBatteryModel());
		}
		List<ModelVO> batteryList = modelService.getDropDownList(MainBizEnum.DEVICE_TYPE_2.getCode());
		Map<String, String> modelMap = batteryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));

		vo.setXAxisData(xAxisData.stream().map(model -> modelMap.get(model)).collect(Collectors.toList()));
		vo.setLegendData(Arrays.asList("在库", "出库"));
		//在库的数据
		List<String> inList = new ArrayList<>();
		List<String> outList = new ArrayList<>();
		int i = 0;
		for (String model : xAxisData) {
			inList.add(i, StringPool.ZERO);
			outList.add(i, StringPool.ZERO);
			for (InfoStatisticalDTO infoStatisticalTDO : infoStatisticalTDOList) {
				if (infoStatisticalTDO.getBatteryModel().equals(model) && infoStatisticalTDO.getState().equals(StringPool.ONE)) {
					inList.set(i, infoStatisticalTDO.getNum() + "");
				} else if (infoStatisticalTDO.getBatteryModel().equals(model) && infoStatisticalTDO.getState().equals(StringPool.ZERO)) {
					outList.set(i, infoStatisticalTDO.getNum() + "");
				}
			}
			i++;
		}
		SeriesVO outSeriesVO = new SeriesVO();
		outSeriesVO.setName("出库");
		outSeriesVO.setData(outList);

		SeriesVO inSeriesVO = new SeriesVO();
		inSeriesVO.setName("在库");
		inSeriesVO.setData(inList);

		vo.setSeries(Arrays.asList(outSeriesVO, inSeriesVO));
		return R.data(vo);
	}

	/**
	 * 新增电池信息
	 *
	 * @param batteryDTO
	 * @return
	 */
	@Override
	public BatteryVO add(BatteryDTO batteryDTO) {
		batteryDTO.setBatteryGuid(IdUtil.simpleUUID());
		BatteryEntity batteryEntity = BeanUtil.copyProperties(batteryDTO, BatteryEntity.class);
		batteryEntity.setMaintainCount(0);
		batteryEntity.setCircleCount(0);
		QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
		//判断SN是否重复
		if (StrUtil.isNotEmpty(batteryDTO.getSnCode())) {
			queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(BatteryEntity::getSnCode, batteryDTO.getSnCode());
			Long num = getBaseMapper().selectCount(queryWrapper);
			if (num > 0) {
				throw new ServiceException("SN码已存在");
			}
		}
		//判断rfid是否重复
		if (StrUtil.isNotEmpty(batteryDTO.getRfidGuid())) {
			queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(BatteryEntity::getRfidGuid, batteryDTO.getRfidGuid());
			Long num = getBaseMapper().selectCount(queryWrapper);
			if (num > 0) {
				throw new ServiceException("rfid已存在");
			}
		}
		if (StrUtil.isEmpty(batteryDTO.getBatteryName())) {
			throw new ServiceException("电池名称不能为空");
		}
		//判断电池名称是否重复
		queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(BatteryEntity::getBatteryName, batteryDTO.getBatteryName());
		Long num = getBaseMapper().selectCount(queryWrapper);
		if (num > 0) {
			throw new ServiceException("电池名称已存在");
		}

		save(batteryEntity);
		return detail(batteryEntity.getBatteryGuid());
	}

	/**
	 * 更新电池信息
	 *
	 * @param batteryDTO
	 * @return
	 */
	@Override
	public BatteryVO update(BatteryDTO batteryDTO) {
		BatteryEntity batteryEntity = BeanUtil.copyProperties(batteryDTO, BatteryEntity.class);
		QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
		//判断SN是否重复
		if (StrUtil.isNotEmpty(batteryDTO.getSnCode())) {
			queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(BatteryEntity::getSnCode, batteryDTO.getSnCode());
			queryWrapper.lambda().ne(BatteryEntity::getBatteryGuid, batteryDTO.getBatteryGuid());
			Long num = getBaseMapper().selectCount(queryWrapper);
			if (num > 0) {
				throw new ServiceException("SN码已存在");
			}
		}
		//判断rfid是否重复
		if (StrUtil.isNotEmpty(batteryDTO.getRfidGuid())) {
			queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(BatteryEntity::getRfidGuid, batteryDTO.getRfidGuid());
			queryWrapper.lambda().ne(BatteryEntity::getBatteryGuid, batteryDTO.getBatteryGuid());
			Long num = getBaseMapper().selectCount(queryWrapper);
			if (num > 0) {
				throw new ServiceException("rfid已存在");
			}
		}
		//判断电池名称是否重复
		if (StrUtil.isNotEmpty(batteryDTO.getBatteryName())) {
			queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(BatteryEntity::getBatteryName, batteryDTO.getBatteryName());
			queryWrapper.lambda().ne(BatteryEntity::getBatteryGuid, batteryDTO.getBatteryGuid());
			Long num = getBaseMapper().selectCount(queryWrapper);
			if (num > 0) {
				throw new ServiceException("电池名称已存在");
			}
		}
		queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(BatteryEntity::getBatteryGuid, batteryEntity.getBatteryGuid());

		BatteryEntity entity = getBaseMapper().selectOne(queryWrapper);
		if (storeRoomService.checkOperation(entity.getEquipmentCabinetGuid(), AllCoreAuthUtil.getUserId())) {
			throw new ServiceException("您没有操作权限");
		}

		update(batteryEntity, queryWrapper);
		return detail(batteryEntity.getBatteryGuid());
	}

	/**
	 * 根据SN码更新电池信息
	 *
	 * @param batteryDTO
	 * @return
	 */
	@Override
	public BatteryVO updateBySn(BatteryDTO batteryDTO) {
		if (StrUtil.isEmpty(batteryDTO.getSnCode())) {
			throw new ServiceException("SN码不能为空");
		}
		if (StrUtil.isEmpty(batteryDTO.getPosition())) {
			throw new ServiceException("电池位置不能为空");
		}
		QueryWrapper<EquipmentCabinetCellEntity> cellWrapper = new QueryWrapper();
		cellWrapper.lambda().eq(EquipmentCabinetCellEntity::getPosition, batteryDTO.getPosition());
		EquipmentCabinetCellEntity equipmentCabinetCellEntity = cellService.getBaseMapper().selectOne(cellWrapper);
		if (equipmentCabinetCellEntity == null) {
			throw new ServiceException("未找到位置信息");
		}

		QueryWrapper<EquipmentCabinetEntity> cabinetEntityQueryWrapper = new QueryWrapper<>();
		cabinetEntityQueryWrapper.lambda().eq(EquipmentCabinetEntity::getEquipmentCabinetGuid, equipmentCabinetCellEntity.getEquipmentCabinetGuid());
		EquipmentCabinetEntity equipmentCabinetEntity = equipmentCabinetMapper.selectOne(cabinetEntityQueryWrapper);
		if (equipmentCabinetEntity == null) {
			throw new ServiceException("未找到机柜信息");
		}
		//如果是出库  则清空电池位置信息
		if(("0").equals(batteryDTO.getState())){
			batteryDTO.setPosition(null);
		}

		batteryDTO.setEquipmentCabinetGuid(equipmentCabinetCellEntity.getEquipmentCabinetGuid());
		batteryDTO.setCellGuid(equipmentCabinetCellEntity.getCellGuid());

		BatteryVO batteryVO = new BatteryVO();
		batteryVO.setStoreRoomGuid(equipmentCabinetEntity.getStoreRoomGuid());

		QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(BatteryEntity::getSnCode, batteryDTO.getSnCode());
		BatteryEntity entity = getBaseMapper().selectOne(queryWrapper);
		if (entity == null) {
			//电池为新电池,自动注册进系统
			batteryDTO.setBatteryType("2");

			String position = batteryDTO.getPosition().substring(0,3);
			QueryWrapper<EquipmentCabinetFloorEntity> entityQueryWrapper = new QueryWrapper<>();
			entityQueryWrapper.lambda().eq(EquipmentCabinetFloorEntity::getPosition, position);
			EquipmentCabinetFloorEntity equipmentCabinetFloorEntity = equipmentCabinetFloorMapper.selectOne(entityQueryWrapper);
			String modelName = new String();
			String floorType = equipmentCabinetFloorEntity.getFloorType();
			if("2".equals(floorType)){
				String fullCap = batteryDTO.getFullCapacity();
				List<ModelEntity> modelEntities = modelMapper.selectList(new LambdaQueryWrapper<ModelEntity>()
					.eq(ModelEntity::getBatteryCapacity,fullCap).orderByDesc(ModelEntity::getSort));
				modelName = modelEntities.get(0).getName();
			}else {
				modelName = BatteryModelEnum.getByCode(floorType).getValue();
			}
			List<ModelEntity> modelEntityList = modelMapper.selectList(new LambdaQueryWrapper<ModelEntity>()
				.eq(ModelEntity::getName,modelName).orderByDesc(ModelEntity::getSort));


			batteryDTO.setBatteryModel(modelEntityList.get(0).getCode());
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-ddHH:mm:ss");
			String dateString = formatter.format(new Date());
			batteryDTO.setBatteryName(modelName +"_" +dateString + RandomCodeUtils.randomCode(4));
			batteryDTO.setDeptCode(AuthUtil.getDeptCode());
			//batteryDTO.setFactoryCode();
			//batteryDTO.setBatteryLife();
			//batteryDTO.setBatteryLifePer();
			batteryDTO.setStatus("0");
			batteryVO = add(batteryDTO);
			return batteryVO;
		}
		batteryDTO.setBatteryGuid(entity.getBatteryGuid());
		batteryVO = update(batteryDTO);


		return batteryVO;
	}

	/**
	 * 电池信息详情
	 *
	 * @param guid
	 * @return
	 */
	@Override
	public BatteryVO detail(String guid) {
		QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(BatteryEntity::getBatteryGuid, guid);
		BatteryEntity batteryEntity = getBaseMapper().selectOne(queryWrapper);
		if (batteryEntity == null) {
			return null;
		}
		List<ModelVO> factoryList = modelService.getDropDownList("99");
		Map<String, String> factoryMap = factoryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		List<ModelVO> batteryList = modelService.getDropDownList(MainBizEnum.DEVICE_TYPE_2.getCode());
		Map<String, String> modelMap = batteryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));

		BatteryVO batteryVO = BatteryWrapper.build(factoryMap, modelMap).entityVO(batteryEntity);
		return batteryVO;
	}

	/**
	 * 删除电池信息
	 *
	 * @param guids
	 */
	@Override
	public void remove(String guids) {
		List<String> split = StrUtil.split(guids, ",");
		for (String guid : split) {
			QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().eq(BatteryEntity::getBatteryGuid, guid);
			BatteryEntity entity = getBaseMapper().selectOne(queryWrapper);
			if (StrUtil.isNotEmpty(entity.getEquipmentCabinetGuid())) {
				if (storeRoomService.checkOperation(entity.getEquipmentCabinetGuid(), AuthUtil.getUserId())) {
					throw new ServiceException("您没有操作权限");
				}
			}
		}
		QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(BatteryEntity::getBatteryGuid, split);
		getBaseMapper().delete(queryWrapper);
	}

	/**
	 * 分页列表
	 *
	 * @param page
	 * @param batteryDTO
	 * @return
	 */
	@Override
	public IPage<BatteryVO> pageList(IPage<BatteryEntity> page, BatteryDTO batteryDTO) {
		QueryWrapper<BatteryEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().like(BatteryEntity::getDeptCode, AuthUtil.getDeptCode());
		queryWrapper.lambda().like(StrUtil.isNotEmpty(batteryDTO.getBatteryName()), BatteryEntity::getBatteryName, batteryDTO.getBatteryName());
		queryWrapper.lambda().like(StrUtil.isNotEmpty(batteryDTO.getSnCode()), BatteryEntity::getSnCode, batteryDTO.getSnCode());
		queryWrapper.lambda().eq(StrUtil.isNotEmpty(batteryDTO.getState()), BatteryEntity::getState, batteryDTO.getState());
		queryWrapper.lambda().like(StrUtil.isNotEmpty(batteryDTO.getDeptCode()), BatteryEntity::getDeptCode, batteryDTO.getDeptCode());
		queryWrapper.lambda().ge(batteryDTO.getMinCircleCount() != null, BatteryEntity::getCircleCount, batteryDTO.getMinCircleCount());
		queryWrapper.lambda().le(batteryDTO.getMaxCircleCount() != null, BatteryEntity::getCircleCount, batteryDTO.getMaxCircleCount());
		queryWrapper.lambda().in(CollectionUtil.isNotEmpty(batteryDTO.getGuids()), BatteryEntity::getBatteryGuid, batteryDTO.getGuids());
		IPage<BatteryEntity> batteryEntityIPage = getBaseMapper().selectPage(page, queryWrapper);
		List<ModelVO> factoryList = modelService.getDropDownList("99");
		Map<String, String> factoryMap = factoryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		List<ModelVO> batteryList = modelService.getDropDownList(MainBizEnum.DEVICE_TYPE_2.getCode());
		Map<String, String> modelMap = batteryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));
		IPage<BatteryVO> batteryVOIPage = BatteryWrapper.build(factoryMap, modelMap).pageVO(batteryEntityIPage);
		return batteryVOIPage;
	}


	/**
	 * 批量导入
	 *
	 * @param file
	 */
	@Override
	public void importExcel(MultipartFile file, HttpServletResponse response) {
		ExcelReader reader;
		try {
			reader = ExcelUtil.getReader(file.getInputStream());
		} catch (Exception e) {
			e.printStackTrace();
			throw new ServiceException("文件读取失败");
		}

		List<List<Object>> read = reader.read(0);
		if (CollectionUtil.isEmpty(read) || read.size() <= 1) {
			throw new ServiceException("文件不能为空");
		}
		//获取到表头
		List<String> excelHeaderList = Arrays.asList("设备类型", "设备型号", "设备编号", "设备名称", "SN码", "所属部门", "厂家");
		List<Object> headerList = read.get(0);
		for (int i = 0; i < headerList.size(); i++) {
			if (!excelHeaderList.get(i).equals(getCellValue(headerList.get(i)))) {
				throw new ServiceException("模版不正确,导入失败!");
			}
		}
		read.remove(0);
		List<BatteryDTO> batteryDTOList = new ArrayList<>();
		Set<String> deptNameSet = new HashSet<>();
		for (List<Object> objects : read) {
			deptNameSet.add(getCellValue(objects.get(5)));
			BatteryDTO batteryDTO = new BatteryDTO();
			batteryDTO.setBatteryGuid(UUID.fastUUID().toString());
			batteryDTO.setBatteryType(getCellValue(objects.get(0)));
			batteryDTO.setBatteryModel(getCellValue(objects.get(1)));
			batteryDTO.setBatteryCode(getCellValue(objects.get(2)));
			batteryDTO.setBatteryName(getCellValue(objects.get(3)));
			batteryDTO.setSnCode(getCellValue(objects.get(4)));
//			batteryDTO.setRfidGuid(getCellValue(objects.get(5)));
			batteryDTO.setDeptCode(getCellValue(objects.get(5)));
			batteryDTO.setFactoryCode(getCellValue(objects.get(6)));
			batteryDTOList.add(batteryDTO);
		}
		List<ModelVO> factoryList = modelService.getDropDownList("99");
		List<ModelVO> batteryModelList = modelService.getDropDownList(MainBizEnum.DEVICE_TYPE_2.getCode());

		//电池型号
		HashMap<String, String> batteryModelMap = new HashMap<>();
		for (ModelVO modelVO : batteryModelList) {
			batteryModelMap.put(modelVO.getName(), modelVO.getCode());
		}
		//厂家
		HashMap<String, String> factoryMap = new HashMap<>();
		for (ModelVO modelVO : factoryList) {
			factoryMap.put(modelVO.getName(), modelVO.getCode());
		}

		//单位信息
		String join = CollectionUtil.join(deptNameSet, ",");
		R<List<Dept>> deptInfoResult = sysClient.getDeptInfos(AuthUtil.getTenantId(), join);
		if (!deptInfoResult.isSuccess()) {
			throw new ServiceException("获取单位信息失败，请稍后重试！");
		}
		HashMap<String, String> deptMap = new HashMap<>();
		for (Dept dept : deptInfoResult.getData()) {
			deptMap.put(dept.getDeptName(), dept.getDeptCode());
		}
		String tempPath = System.getProperty("user.dir") + File.separator + System.currentTimeMillis();
		File tempFile = new File(tempPath);
		try {
			file.transferTo(tempFile);
		} catch (IOException e) {
			throw new ServiceException("转临时文件失败");
		}
		ExcelWriter writer = ExcelUtil.getWriter(tempFile);
		//新增一列导入结果
		writer.setSheet(0);
		writer.writeCellValue(excelHeaderList.size(), 0, "导入结果");
		//设置导入成功的样式
		CellStyle errorCellStyle = writer.createCellStyle();
		Font errorFont = writer.createFont();
		errorFont.setColor(Font.COLOR_RED);
		errorCellStyle.setFont(errorFont);
		//设置导入失败的样式
		CellStyle successCellStyle = writer.createCellStyle();
		Font successFont = writer.createFont();
		successFont.setColor(IndexedColors.GREEN.index);
		successCellStyle.setFont(successFont);

		for (int i = 0; i < batteryDTOList.size(); i++) {
			BatteryDTO batteryDTO = batteryDTOList.get(i);
			try {
				if (StrUtil.isEmpty(batteryDTO.getBatteryModel())) {
					throw new ServiceException("电池型号不能为空");
				}
				String model = batteryModelMap.get(batteryDTO.getBatteryModel());
				if (StrUtil.isEmpty(model)) {
					throw new ServiceException("电池型号不存在");
				}
				batteryDTO.setBatteryModel(model);
				batteryDTO.setDeptCode(deptMap.get(batteryDTO.getDeptCode()));
				String result = checkImport(batteryDTO, factoryMap);
				if (StrUtil.isNotEmpty(result)) {
					throw new ServiceException(result);
				}
				add(batteryDTO);
				writer.writeCellValue(excelHeaderList.size(), i + 1, "导入成功");
				writer.setStyle(successCellStyle, excelHeaderList.size(), i + 1);
			} catch (Exception e) {
				writer.writeCellValue(excelHeaderList.size(), i + 1, e.getMessage());
				writer.setStyle(errorCellStyle, excelHeaderList.size(), i + 1);
			}
		}
		response.setContentType("application/vnd.ms-excel;charset=utf-8");
		String fileName = file.getOriginalFilename() + ".xls";
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
		ServletOutputStream outputStream = null;
		try {
			outputStream = response.getOutputStream();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		writer.flush(outputStream, true);
		// 关闭writer，释放内存
		writer.close();
		reader.close();
		//此处记得关闭输出Servlet流
		IoUtil.close(outputStream);
		tempFile.deleteOnExit();
	}

	public String checkImport(BatteryDTO batteryDTO, HashMap<String, String> factoryMap) {
		if (!(StrUtil.isNotEmpty(batteryDTO.getBatteryType()) && batteryDTO.getBatteryType().equals("电池"))) {
			return "设备类型不能为空，且必须为电池";
		}
		batteryDTO.setBatteryType("2");
		if (StrUtil.isEmpty(batteryDTO.getBatteryModel())) {
			return "电池型号不能为空";
		}
		if (StrUtil.isEmpty(batteryDTO.getSnCode())) {
			return "SN码不能为空";
		}
		if (!AuthUtil.getDeptCode().equals(batteryDTO.getDeptCode())) {
			return "所属部门仅可为当前账户部门";
		}
		if (StrUtil.isEmpty(batteryDTO.getFactoryCode())) {
			return "厂家不能为空";
		}
		//厂家不存在则新增厂家
		if (StrUtil.isEmpty(factoryMap.get(batteryDTO.getFactoryCode()))) {
			R<ModelVO> result = modelService.saveInfo(new ModelForSaveDTO() {{
				setName(batteryDTO.getFactoryCode());
				setType(MainBizEnum.DEVICE_TYPE_99.getCode());
			}});
			if (result.isSuccess()) {
				ModelVO modelVO = result.getData();
				batteryDTO.setFactoryCode(modelVO.getCode());
				factoryMap.put(modelVO.getName(), modelVO.getCode());
			} else {
				return "厂家新增失败";
			}
		} else {
			batteryDTO.setFactoryCode(factoryMap.get(batteryDTO.getFactoryCode()));
		}
		return null;
	}

	/**
	 * 批量导出
	 *
	 * @param batteryDTO
	 * @return
	 */
	@Override
	public void exportExcel(BatteryDTO batteryDTO, HttpServletResponse response) {
		IPage<BatteryEntity> page = Condition.getPage(new Query());
		page.setSize(-1);
		List<BatteryVO> recordList = pageList(page, batteryDTO).getRecords();
		//表格头部
		List<String> headerList = Arrays.asList("电池型号", "电池名称", "SN码", "RFID码", "所属部门", "厂家", "状态", "循环次数", "维修次数");
		List<List<String>> rows = getRows(recordList, headerList);
		ExcelWriter writer = ExcelUtil.getWriter();
		writer.renameSheet("电池信息导出");
		writer.merge(headerList.size() - 1, "电池信息");
		writer.write(rows, true);

		response.setContentType("application/vnd.ms-excel;charset=utf-8");
		String fileName = DateUtil.now().replace(" ", "") + ".xls";
		response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
		ServletOutputStream outputStream = null;
		try {
			outputStream = response.getOutputStream();
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
		writer.flush(outputStream, true);
		// 关闭writer，释放内存
		writer.close();
		//此处记得关闭输出Servlet流
		IoUtil.close(outputStream);
	}


	/**
	 * 封装需要导出的行数据
	 *
	 * @param recordList 从数据库查询出来的数据
	 * @param headerList 头部信息
	 * @return
	 */
	@NotNull
	private static List<List<String>> getRows(List<BatteryVO> recordList, List<String> headerList) {
		List<List<String>> rows = new ArrayList<>();
		rows.add(headerList);
		for (BatteryVO batteryVO : recordList) {
			List<String> row = new ArrayList<>();
			row.add(batteryVO.getBatteryModelZh());
			row.add(batteryVO.getBatteryName());
			row.add(batteryVO.getSnCode());
			row.add(batteryVO.getRfidGuid());
			row.add(batteryVO.getDeptName());
			row.add(batteryVO.getFactoryCodeZh());
			row.add(batteryVO.getStateZh());
			if (batteryVO.getCircleCount() == null) {
				row.add("");
			} else {
				row.add(batteryVO.getCircleCount() + "");
			}
			if (batteryVO.getMaintainCount() == null) {
				row.add("");
			} else {
				row.add(batteryVO.getMaintainCount() + "");
			}
			rows.add(row);
		}
		return rows;
	}


	/**
	 * 获取表格内容
	 *
	 * @param o
	 * @return
	 */
	public String getCellValue(Object o) {
		if (o == null) {
			return null;
		}
		return StrUtil.trim(o.toString());
	}

}
