package com.allcore.netty.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.constant.BasicConstant;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.dict.cache.DictBizCache;
import com.allcore.netty.dto.WarnDeviceDTO;
import com.allcore.netty.dto.WarnDeviceSearchDTO;
import com.allcore.netty.dto.WarningDTO;
import com.allcore.netty.entity.StoreRoomEntity;
import com.allcore.netty.entity.WarnDeviceEntity;
import com.allcore.netty.mapper.StoreRoomMapper;
import com.allcore.netty.mapper.WarnDeviceMapper;
import com.allcore.netty.service.WarnDeviceService;
import com.allcore.netty.service.WarningService;
import com.allcore.netty.vo.StoreRoomVO;
import com.allcore.netty.vo.WarnDeviceVO;
import com.allcore.system.feign.ISysClient;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.StringPool;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: Jiy
 * @Date: 2022-11-29 15:57
 */
@Slf4j
@Service
public class WarnDeviceServiceImpl extends ZxhcServiceImpl<WarnDeviceMapper, WarnDeviceEntity> implements WarnDeviceService {
	@Autowired
	private StoreRoomMapper storeRoomMapper;
	@Autowired
	private WarningService warningService;
	@Autowired
	private IUserClient userClient;
	@Resource
	private ISysClient sysClient;

	@Override
	public IPage<WarnDeviceVO> pageList(WarnDeviceSearchDTO warnDeviceSearchDTO, Query query) {
		List<String> storeRoomGuids = new ArrayList<>();
		IPage<WarnDeviceVO> page = Condition.getPage(query);
		if(StrUtil.isEmpty(warnDeviceSearchDTO.getStoreRoomGuid())) {
			List<StoreRoomVO> storeRoomVOList = this.homeStoreRoom(AuthUtil.getDeptCode());
			if(CollectionUtil.isEmpty(storeRoomVOList)) {
				return null;
			}
			storeRoomGuids = storeRoomVOList.stream().map(StoreRoomVO::getStoreRoomGUID).collect(Collectors.toList());
		} else  {
			storeRoomGuids.add(warnDeviceSearchDTO.getStoreRoomGuid());
		}
		// 一个库房都看不到则直接返回无数据
		if(CollectionUtil.isEmpty(storeRoomGuids)) {
			return page;
		}
		List<WarnDeviceVO> voList = getBaseMapper().warnDevicePageList(storeRoomGuids, warnDeviceSearchDTO.getWarnDeviceType(), AuthUtil.getUserId(), page);

		if(CollectionUtil.isNotEmpty(voList)) {
			voList.forEach(vo -> {
				vo.setDeviceTypeName(DictBizCache.getValue(MainBizEnum.DEVICE_TYPE.getCode(), vo.getDeviceType()));
			});
		}
		return page.setRecords(voList);
	}

	// 防止循环依赖注入，复写了StoreRoomServiceImpl同名方法
	private List<StoreRoomVO> homeStoreRoom(String deptCode) {
		R<List<TreeNode>> r = sysClient.getDepTree("000000", StringPool.YES, "");
		List<StoreRoomVO> storeRoomVOS = new ArrayList<>();
		List<TreeNode> data = r.getData();
		for (TreeNode treeNode : data) {
			if (CommonUtil.checkDept(deptCode,treeNode.getKey())) {
				//部门下得库房
				List<StoreRoomEntity> storeRoomEntities = storeRoomMapper.selectList(new LambdaQueryWrapper<StoreRoomEntity>().eq(StoreRoomEntity::getDeptCode, treeNode.getKey()));
				for (StoreRoomEntity storeRoomEntity : storeRoomEntities) {
					StoreRoomVO storeRoomVo = new StoreRoomVO();
					storeRoomVo.setStoreRoomGUID(storeRoomEntity.getStoreRoomGuid());
					storeRoomVo.setStoreRoomName(storeRoomEntity.getStoreRoomName());
					storeRoomVOS.add(storeRoomVo);
				}
			}
		}
		return storeRoomVOS;
	}

	@Override
	public WarnDeviceVO detail(String guid) {
		WarnDeviceVO vo = getBaseMapper().warnDeviceDetail(guid);
		vo.setDeviceTypeName(DictBizCache.getValue(MainBizEnum.DEVICE_TYPE.getCode(), vo.getDeviceType()));
		return vo;
	}

	@Override
	@Transactional
	public WarnDeviceVO add(WarnDeviceDTO warnDeviceDTO) {
		warnDeviceDTO.setWarnDeviceGuid(IdUtil.simpleUUID());
		WarnDeviceEntity entity = BeanUtil.copyProperties(warnDeviceDTO, WarnDeviceEntity.class);
		save(entity);
		WarnDeviceVO vo = detail(entity.getWarnDeviceGuid());

		// 获取库房部门信息
		List<String> list = warningService.getUserListByStoreRoomGuid(vo.getStoreRoomGuid());
		// 发送告警到多人,系统消息默认admin发送
		sendWarningData(vo, list);
		return vo;
	}

	/**
	 * 发送告警消息到用户
	 **/
	private void sendWarningData(WarnDeviceVO vo, List<String> list) {
		if(CollectionUtil.isEmpty(list)) {
			return;
		}
		list.stream().distinct().forEach(userId -> {
			WarningDTO dto = new WarningDTO();
			dto.setWarnGuid(IdUtil.simpleUUID());
			String text = "";
			// TODO 后续看实际需要的报警内容进行调整
			if(vo.getDeviceType().equals(MainBizEnum.DEVICE_TYPE_2.getCode())) {
				text = String.format("设备类型：%s，设备型号：%s，设备编号：%s，位置：%s，维修告警次数：%s次，循环告警次数：%s次",vo.getDeviceTypeName(), vo.getDeviceModelName(), vo.getDeviceNo(), vo.getPosition(),  vo.getMaintanceCount(), vo.getCirCount());
			} else {
				text = String.format("设备类型：%s，设备型号：%s，设备编号：%s，位置：%s，维修告警次数：%s次",vo.getDeviceTypeName(), vo.getDeviceModelName(), vo.getDeviceNo(), vo.getPosition(), vo.getMaintanceCount());
			}
			dto.setWarnText(text);
			dto.setWarnStatus(MainBizEnum.READ_STATE_0.getCode());
			dto.setWarnType(MainBizEnum.WARN_STATE_2.getCode());
			dto.setReceiveUser(userId);
			dto.setWarnDeviceGuid(vo.getWarnDeviceGuid());
			// 默认admin发送
			User user = userClient.userByAccount(BasicConstant.TENANTID, BasicConstant.ADMIN).getData();
			dto.setCreateUser(user.getId());
			dto.setCreateTime(DateUtil.now());
			dto.setUpdateUser(user.getId());
			dto.setUpdateTime(DateUtil.now());
			dto.setCreateDept(user.getDeptId());
			warningService.add(dto);
		});
	}
}
