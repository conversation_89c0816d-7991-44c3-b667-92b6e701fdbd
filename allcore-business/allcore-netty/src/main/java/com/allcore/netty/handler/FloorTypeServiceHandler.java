package com.allcore.netty.handler;

import cn.hutool.core.util.ByteUtil;
import com.allcore.netty.components.ServiceFactory;
import com.allcore.netty.consts.Const;
import com.allcore.netty.dto.NettyFloorCellDTO;
import com.allcore.netty.dto.NettyFloorDTO;
import com.allcore.netty.feign.ICabinetFloorClient;
import com.allcore.netty.protocol.Command;
import com.allcore.netty.protocol.FloorRequestPacket;
import com.allcore.netty.protocol.Packet;
import com.allcore.netty.protocol.PacketCodeC;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import io.netty.channel.ChannelFuture;
import io.netty.channel.ChannelFutureListener;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
    * @ClassName: FloorTypeServiceHandler
    * @Description: 动态获取层级类型
    * <AUTHOR>
    * @date 2020年6月8日
    *
 */
@Component
@Slf4j
public class FloorTypeServiceHandler extends SeviceHandlerAbstract {

	@Autowired
	private ICabinetFloorClient iCabinetFloorClient;

	@PostConstruct
	@Override
	public void init(){
		ServiceFactory.seviceMap.put(Command.FLOOR_TYPE, this);
	}

	@Override
	public void handler(Channel ch, Packet packet) {
		FloorRequestPacket floorRequestPacket = decode(packet);
			log.info("总层数："+floorRequestPacket.getFloorNum());
			NettyFloorCellDTO dto = new NettyFloorCellDTO();
			dto.setFloorNum(floorRequestPacket.getFloorNum());
			dto.setDto(floorRequestPacket.getDto());
			dto.setCabinetSnCode(floorRequestPacket.getDeviceSn());
			iCabinetFloorClient.saveOrUpdate(dto);
	}
	/**
	    * @Title: decode
	    * @Description: 解析层级类型
	    * @param @param packet
	    * @param @return    参数
	    * @return TemHumRequestPacket    返回类型
	    * @throws
	 */
	public static 	FloorRequestPacket decode(Packet packet){
		FloorRequestPacket floorRequestPacket = (FloorRequestPacket)packet;
		try {
			byte[] data = packet.getData();
			if (data == null) {
				return floorRequestPacket;
			}
			//层数
			byte[] floorNumByte = Arrays.copyOfRange(data, 0, 1);
			int floorNum = ByteUtil.bytesToInt(floorNumByte);
			List<NettyFloorDTO> list = new ArrayList<>();
			int j = 0;
			for (int i = 0; i < floorNum; i++) {
				NettyFloorDTO dto = new NettyFloorDTO();
				byte[] type = Arrays.copyOfRange(data, j+1, j+2);
				byte[] cellNum = Arrays.copyOfRange(data, j+2, j+3);
				dto.setFloorType(String.valueOf(ByteUtil.bytesToInt(type)));
				dto.setCellNum(String.valueOf(ByteUtil.bytesToInt(cellNum)));
				list.add(dto);
				j +=2;
				log.info("机柜SN码："+floorRequestPacket.getDeviceSn()+"层数："+(i+1)+"层级类型:"+ByteUtil.bytesToInt(type)+"卡槽数量:"+ByteUtil.bytesToInt(cellNum));
			}
			floorRequestPacket.setFloorNum(String.valueOf(floorNum));
			floorRequestPacket.setDto(list);
		} catch (Exception e) {
			log.error("解析异常",e);
		}
		return floorRequestPacket;
	}

	@Override
	public void send(Channel ch, Packet packet) {
		//发解锁指令给嵌入式
		packet.setReturnType(Const.RETURN_TYPE_NEED);//需要返回应答包
		ByteBuf encode = PacketCodeC.INSTANCE.encode(ch.alloc(), packet);
		final ChannelFuture f = ch.writeAndFlush(encode);
		f.addListener(new ChannelFutureListener() {
			@Override
			public void operationComplete(ChannelFuture future) {

				log.info("指令已发送");
			}
		});
	}

}


