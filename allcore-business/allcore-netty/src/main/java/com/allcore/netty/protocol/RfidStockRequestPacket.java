package com.allcore.netty.protocol;

import lombok.Data;

import java.util.List;

/**
    * @ClassName: TemHumRequestPacket
    * @Description: 机柜传过来的温湿度实体
    * <AUTHOR>
    * @date 2020年6月18日
    *
 */
@Data
public class RfidStockRequestPacket extends Packet {

	/**
	 * 机柜SN码
	 */
	private byte[] cabinetSnCode ;

	/**
	 * rfid卡号
	 */
	private List<String> rfids;
	/**
	 * netty序号
	 */
	private Byte redisNo;
	/**
	 * 机柜层级
	 */
	private String cabinetFloor;

	@Override
	public Byte getCommand() {
		return Command.REQ_RFIDINFO;
	}

	/**
	 * 重写这个方法
	 */
	@Override
	public byte[] toBytes(Packet packet){
		RfidStockRequestPacket rfidStockRequestPacket = (RfidStockRequestPacket)packet;
		byte[] bytes = new byte[1];
		bytes[0] = rfidStockRequestPacket.getRedisNo();
    	return bytes;
    }

}
