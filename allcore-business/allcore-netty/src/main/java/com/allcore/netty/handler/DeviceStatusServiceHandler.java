package com.allcore.netty.handler;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ArrayUtil;
import com.allcore.netty.components.ServiceFactory;
import com.allcore.netty.consts.Const;
import com.allcore.netty.protocol.Command;
import com.allcore.netty.protocol.DeviceStatusRequestPacket;
import com.allcore.netty.protocol.Packet;
import com.allcore.netty.protocol.PacketCodeC;
import com.allcore.netty.utils.ByteUtil;
import com.allcore.netty.utils.OrderUtil;
import com.allcore.common.constant.BasicConstant;
import com.allcore.core.redis.cache.AllcoreRedis;
import io.netty.buffer.ByteBuf;
import io.netty.channel.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;


/**
 * @ClassName: RegisterServiceHandler
 * @Description: 设备状态业务
 * <AUTHOR>
 * @date 2020年6月8日
 *
 */
@Component
@Slf4j
public class DeviceStatusServiceHandler extends SeviceHandlerAbstract {

	@Resource
	private AllcoreRedis allcoreRedis;

	@PostConstruct
	@Override
	public void init() {
		ServiceFactory.seviceMap.put(Command.REQ_DEVICESTATUS, this);
	}

	@Override
	public void handler(Channel ch, Packet packet) {
		DeviceStatusRequestPacket deviceStatusPacket = decode(packet);
		log.info("机柜sn:{},位置:{},电池sn:{},电池出入库:{}", packet.getDeviceSn(), deviceStatusPacket.getPosition(), deviceStatusPacket.getCode(), deviceStatusPacket.getDeviceStatus());
		// 检查电池出入库信息
		checkDeviceState(deviceStatusPacket.getDeviceSn(), deviceStatusPacket.getCode(), deviceStatusPacket.getDeviceStatus());
	}

	/**
	 * 堆叠出入库的电池信息 - 关门的时候销毁
	 * @param cabinetSnCode
	 * @param batterySn
	 * @param state
	 */
	private void checkDeviceState(String cabinetSnCode, String batterySn, int state) {
		String key = "";
		if(state == 0) {
			// 电池出库
			key = cabinetSnCode + BasicConstant.OUT_B;
		} else if(state == 1) {
			// 入库
			key = cabinetSnCode + BasicConstant.IN_B;
		} else {
			log.info("出入库状态不存在,机柜sn:{},电池sn:{},电池出入库:{}", cabinetSnCode, batterySn, state);
		}
		List<String> list = allcoreRedis.get(key);
		if(CollectionUtil.isEmpty(list)) {
			list = new ArrayList<>();
		}
		list.add(batterySn);
		allcoreRedis.set(key, list);
	}

	/**
	    * @Title: decode
	    * @Description: 数据包解析
	    * @param @param packet
	    * @param @return    参数
	    * @return DeviceStatusRequestPacket    返回类型
	    * @throws
	 */
	public static DeviceStatusRequestPacket decode(Packet packet) {
		DeviceStatusRequestPacket deviceStatusRequestPacket = (DeviceStatusRequestPacket) packet;
		byte[] data = packet.getData();
		if (ArrayUtil.isEmpty(data)) {
			return deviceStatusRequestPacket;
		}
		int idInt = ByteUtil.bytesToShort(Arrays.copyOfRange(data, 0, 1));
		byte[] deviceId = Arrays.copyOfRange(data, 1, idInt+1);//设备Id
		byte[] deviceStatus = Arrays.copyOfRange(data, idInt+1, idInt+2);//设备状态
		String clientId = OrderUtil.getClientId(packet.getDevicePosition());//位置

		String snCode = OrderUtil.byteArr2Char(deviceId);
		deviceStatusRequestPacket.setDeviceStatus(ByteUtil.byteOneInt(deviceStatus[0]));
		deviceStatusRequestPacket.setCode(snCode);
		deviceStatusRequestPacket.setPosition(clientId.split("-0")[0]);
		return deviceStatusRequestPacket;
	}

	@Override
	public void send(Channel ch, Packet packet) {
		// 返回成功的响应包
		packet.setLength((short)36);
		packet.setReturnType(Const.RETURN_TYPE_REPLY);
		ByteBuf encode = PacketCodeC.INSTANCE.encode(ch.alloc(), packet);

		byte[] bytes = new byte[encode.readableBytes()];
	   	int readerIndex = encode.readerIndex();
	   	encode.getBytes(readerIndex, bytes);
	   	log.info("设备状态响应数据包长度："+bytes.length);
	   	log.info("设备状态响应数据包："+ByteUtil.printHexString(bytes, bytes.length));
		log.info(">>>>设备状态变更已响应..");
		ch.writeAndFlush(encode);
	}
}
