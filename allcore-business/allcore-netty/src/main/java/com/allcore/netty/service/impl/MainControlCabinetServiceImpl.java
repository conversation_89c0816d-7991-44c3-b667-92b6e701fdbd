package com.allcore.netty.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.netty.entity.MainControlCabinetEntity;
import com.allcore.netty.mapper.MainControlCabinetMapper;
import com.allcore.netty.service.MainControlCabinetService;
import com.allcore.netty.service.ModelService;
import com.allcore.netty.vo.ControlCabinetVO;
import com.allcore.netty.vo.ModelVO;
import com.allcore.netty.wrapper.ControlCabinetWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;


@Service
public class MainControlCabinetServiceImpl extends ZxhcServiceImpl<MainControlCabinetMapper, MainControlCabinetEntity> implements MainControlCabinetService {


	@Resource
	private ModelService modelService;

	/**
	 * 根据主控柜guid查询主控柜
	 *
	 * @param guidList
	 * @return
	 */
	@Override
	public List<ControlCabinetVO> selectByGuidList(List<String> guidList) {
		if (CollectionUtil.isEmpty(guidList)) {
			return new ArrayList<>();
		}
		QueryWrapper<MainControlCabinetEntity> mainQueryWrapper = new QueryWrapper<>();
		mainQueryWrapper.lambda().in(MainControlCabinetEntity::getCabinetGuid, guidList);
		List<MainControlCabinetEntity> mainControlCabinetEntities = getBaseMapper().selectList(mainQueryWrapper);

		List<ModelVO> factoryList = modelService.getDropDownList("99");
		Map<String, String> factoryMap = factoryList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));

		List<ModelVO> modelList = modelService.getDropDownList(MainBizEnum.CABINET_TYPE_2.getCode());
		Map<String, String> modelMap = modelList.stream().collect(Collectors.toMap(ModelVO::getCode, ModelVO::getName, (existing, replace) -> replace));

		List<ControlCabinetVO> controlCabinetVOS = ControlCabinetWrapper.build(factoryMap, modelMap).listVO(mainControlCabinetEntities);
		return controlCabinetVOS;
	}

	/**
	 * 根据主控柜guid查询主控柜
	 *
	 * @param guid
	 * @return
	 */
	@Override
	public ControlCabinetVO selectByGuid(String guid) {
		List<ControlCabinetVO> controlCabinetVOS = selectByGuidList(Arrays.asList(guid));
		if (CollectionUtil.isEmpty(controlCabinetVOS)) {
			return null;
		}
		return controlCabinetVOS.get(0);
	}
}
