package com.allcore.netty.listener;

import cn.hutool.core.thread.ExecutorBuilder;
import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * binlog监听线程池
 *
 * <AUTHOR>
 * @date 2022/12/13 13:47
 **/
@Slf4j
public class ListenerThreadPool {

	public static final ExecutorService executor = ExecutorBuilder.create()
		.setCorePoolSize(5)
		.setMaxPoolSize(10)
		.setWorkQueue(new LinkedBlockingQueue<>(100))
		.build();

	static {
		new Thread(() -> {
			while (true) {
				int activeCount = ((ThreadPoolExecutor) executor).getActiveCount();
				if (activeCount >= 1) {
					log.info("binlog监听消费线程池活跃线程数：" + activeCount);
				}
				try {
					Thread.sleep(1000);
				} catch (InterruptedException e) {
					throw new RuntimeException(e);
				}
			}
		}).start();
	}

	public static void addTask(Runnable command) {
		executor.execute(command);
	}
}
