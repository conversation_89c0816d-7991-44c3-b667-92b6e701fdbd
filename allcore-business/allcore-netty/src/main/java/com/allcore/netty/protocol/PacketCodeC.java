package com.allcore.netty.protocol;

import com.allcore.netty.serial.Serializer;
import com.allcore.netty.utils.ByteUtil;
import com.allcore.netty.utils.CrcUtil;
import com.allcore.netty.utils.GetCharAscii;
import com.allcore.netty.utils.OrderUtil;
import io.netty.buffer.ByteBuf;
import io.netty.buffer.ByteBufAllocator;
import io.netty.buffer.ByteBufUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;



@Slf4j
public class PacketCodeC {

	private static final byte MAGIC_NUMBER = (byte) 0xAA;
//	private static final byte SERIALIZE_ALGORITHM = 1;// 默认值1 序列化算法
	public static final PacketCodeC INSTANCE = new PacketCodeC();

	private static final Map<Byte, Packet> packetTypeMap;
//	private static final Map<Byte, Serializer> serializerMap;

	static {
		packetTypeMap = new HashMap<>();
		packetTypeMap.put(Command.REQ_HEART, new HeartRequestPacket());//心跳包
		packetTypeMap.put(Command.REQ_AUTHENTICATION, new AuthRequestPacket());// 认证请求数据包
		packetTypeMap.put(Command.TEMPERATURE_HUMIDITY, new TemHumRequestPacket());//温湿度请求数据包
		packetTypeMap.put(Command.REQ_DEVICESTATUS, new DeviceStatusRequestPacket());//设备状态请求数据包
		packetTypeMap.put(Command.REQ_BATTERYINFO, new BatteryInfoRequestPacket());//电池信息请求包
		packetTypeMap.put(Command.REQ_BATTERYCHARGE, new ChargeRequestPacket());//电池充放电指令
		packetTypeMap.put(Command.REQ_UNLOCKCABINET, new UnlockRequestPacket());//机柜解锁业务请求包
		//packetTypeMap.put(Command.REQ_RFIDINFO, new RfidRequestPacket());//机柜解锁业务请求包
		packetTypeMap.put(Command.REQ_RFIDINFO, new RfidStockRequestPacket());//监测rfid是否在库指令
		packetTypeMap.put(Command.FLOOR_TYPE, new FloorRequestPacket());//动态获取层级
		/*serializerMap = new HashMap<>();
		Serializer serializer = new JSONSerializer();
		serializerMap.put(serializer.getSerializerAlogrithm(), serializer);*/
	}

	public ByteBuf encode(ByteBufAllocator byteBufAllocator, Packet packet) {
		// 1. 创建 ByteBuf 对象
		ByteBuf byteBuf = byteBufAllocator.ioBuffer();
		// 2. 序列化 java 对象
		byte[] bytes = Serializer.DEFAULT.serialize(packet);

		// 3. 实际编码过程
		byteBuf.writeByte(MAGIC_NUMBER);// 工控机发给嵌入式
		byteBuf.writeByte(packet.getDeviceType());// 设备类型
		byteBuf.writeBytes(packet.getDevicePosition());// 货架号或卡槽号
		byteBuf.writeShort(packet.getLength());// 整个数据包长度

		byteBuf.writeByte(packet.getReturnType());// 返回类型
		byteBuf.writeByte(packet.getCommand());// 命令类型
		byteBuf.writeBytes(packet.getDeviceSn().getBytes());//机柜码
//		log.info("==>响应命令："+packet.getCommand());
		if(packet.getReturnType() == 2 && packet.getCommand() !=100){
			byteBuf.writeShort(22);// 数据内容长度
			byteBuf.writeByte(packet.getResStatus());
		}else if(packet.getCommand() != 7){
			byteBuf.writeShort(bytes.length);// 数据内容长度
			byteBuf.writeBytes(bytes);// 数据内容
		}
		byte[] all = new byte[byteBuf.readableBytes()];
		int readerIndex = byteBuf.readerIndex();
		byteBuf.getBytes(readerIndex, all);
		if(packet.getCommand() !=100 && packet.getCommand()!=3
				&& packet.getCommand()!=5 && packet.getCommand() !=4 && packet.getCommand() !=7 && packet.getCommand() !=6){//如果是心跳包、还有工控机主动发送的命令不处理下面的逻辑
			//补0
			if(all.length < 34){
				int len = 34 - all.length;
				byte[] plus = new byte[len];
				for(int i = 0;i<len;i++){
					plus[0] = 0;
				}
				byteBuf.writeBytes(plus);
				all = ByteUtil.byteMerger(all,plus);
			}
		}
		byteBuf.writeShort((short) CrcUtil.calcCrc16(all));//CRC校验
		log.info("指令类型:"+packet.getCommand()+"发送的报文:" + ByteBufUtil.hexDump(byteBuf));
		return byteBuf;
	}

	public Packet decode(ByteBuf byteBuf) {
		try {
			byte[] bytes = new byte[byteBuf.readableBytes()];
			int readerIndex = byteBuf.readerIndex();
			byteBuf.getBytes(readerIndex, bytes);
			// 跳过前面1个字节
			byteBuf.skipBytes(1);
			byte deviceType = byteBuf.readByte();
			// 设备地址
			byte[] position = new byte[4];
			byteBuf.readBytes(position);
			byteBuf.skipBytes(2);
			// 返回类型
			byte returnType = byteBuf.readByte();
			// 指令
			byte command = byteBuf.readByte();
			// 设备机柜sn
			byte[] sn = new byte[12];
			byteBuf.readBytes(sn);
			// 数据包长度
			short readShort = byteBuf.readShort();
			byte[] data = new byte[readShort];
			byteBuf.readBytes(data);
			int readableBytes = byteBuf.readableBytes();
			short crc = 0;
			if(readableBytes > 0){
				crc = byteBuf.readShort();
			}
			Packet packet = getRequestType(command);
			if(packet !=null){
				packet.setDeviceSn(OrderUtil.byteArr2Char(sn));
				packet.setDeviceType(deviceType);//设备类型
				packet.setDevicePosition(position);//设备位置
				packet.setReturnType(returnType);
				packet.setData(data);
				packet.setCrc(crc);
				packet.setCrcBytes(Arrays.copyOfRange(bytes, 0, bytes.length-2));
				return packet;
			}
			/*Serializer serializer = getSerializer(SERIALIZE_ALGORITHM);
			if (requestType != null && serializer != null) {
				return serializer.deserialize(requestType, bytes);
			}*/
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}

	/*private Serializer getSerializer(byte serializeAlgorithm) {

		return serializerMap.get(serializeAlgorithm);
	}*/

	private Packet getRequestType(byte command) {

		return packetTypeMap.get(command);
	}

	/**
	 * @Title: getClientId
	 * @Description: 获取设备地址信息-作为客户端ID @param @param
	 * position @param @return 参数 @return String 返回类型 @throws
	 */
	public static String getClientId(byte[] position) {
		if (position == null || position.length != 4) {
			return null;
		}
		int cabinetNo = ByteUtil.byteOneInt(position[0]);// 机柜编号
		int floorNo = position[1];// 机柜编号
		int cellNo = position[2];// 层级编号
//		int slot = position[3];// 格子编号

		char cabinetNoChar = GetCharAscii.byteAsciiToChar(cabinetNo);
		StringBuffer clientId = new StringBuffer();
		clientId.append(String.valueOf(cabinetNoChar)).append("-").append(String.valueOf(floorNo)).append("-")
				.append(String.valueOf(cellNo));
		return clientId.toString();
	}

	/**
	 * 得到电池的位置
	 *
	 * @param position
	 * @return
	 */
	public static String getBatteryClientId(byte[] position) {
		if (position == null || position.length != 4) {
			return null;
		}

		int cabinetNo = ByteUtil.byteOneInt(position[0]);// 机柜编号
		int floorNo = position[1];// 电池柜编号
		int cellNo = position[2];// 层级编号
		int slot = position[3];// 格子编号
		char cabinetNoChar = GetCharAscii.byteAsciiToChar(cabinetNo);
		StringBuffer clientId = new StringBuffer();
		clientId.append(cabinetNoChar).append("-")
				.append(floorNo).append("-")
				.append(cellNo).append("-")
				.append(slot);
		return clientId.toString();
	}
}
