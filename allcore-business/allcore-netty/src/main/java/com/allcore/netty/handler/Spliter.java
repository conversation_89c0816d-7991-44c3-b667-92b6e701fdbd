package com.allcore.netty.handler;

import com.allcore.netty.consts.Const;

import io.netty.buffer.ByteBuf;
import io.netty.channel.ChannelHandlerContext;
import io.netty.handler.codec.LengthFieldBasedFrameDecoder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 *  屏蔽嵌入式设备不是以0xBB开头得数据包
    * @ClassName: Spliter
    * <AUTHOR>
    * @date 2020年6月15日
    *
 */
public class Spliter extends LengthFieldBasedFrameDecoder {
    private static final int LENGTH_FIELD_OFFSET = 6;
    private static final int LENGTH_FIELD_LENGTH = 2;
    private static final int LENGTH_ADJUSTMENT =-8;
    private static final int INITI_ALBYTESTOSTRIP =0;
    public Logger log = LoggerFactory.getLogger(this.getClass());
    public Spliter() {
        super(Integer.MAX_VALUE, LENGTH_FIELD_OFFSET, LENGTH_FIELD_LENGTH,LENGTH_ADJUSTMENT,INITI_ALBYTESTOSTRIP);
    }

    @Override
    protected Object decode(ChannelHandlerContext ctx, ByteBuf in) throws Exception {
//    	 byte[] bytes = new byte[in.readableBytes()];
//    	 int readerIndex = in.readerIndex();
//    	 in.getBytes(readerIndex, bytes);
//    	 System.out.println("======>"+bytes.length);
//    	 System.out.println("======>"+Arrays.toString(bytes));
    	// 屏蔽非本协议的客户端
        byte HEAD_DEVICE = in.getByte(in.readerIndex());
        log.info("HEAD_DEVICE:" + HEAD_DEVICE);
        if (in.getByte(in.readerIndex()) != Const.HEAD_DEVICE_PC) {
            ctx.channel().close();
            return null;
        }
        return super.decode(ctx, in);
    }
}
