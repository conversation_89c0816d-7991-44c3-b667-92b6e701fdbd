package com.allcore.netty.service.impl;

import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.enums.MainBizEnum;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.netty.dto.RfidBindAddDTO;
import com.allcore.netty.dto.RfidBindDTO;
import com.allcore.netty.entity.RfidBindEntity;
import com.allcore.netty.entity.SpareEntity;
import com.allcore.netty.entity.UavEntity;
import com.allcore.netty.enums.RfidBelongEnum;
import com.allcore.netty.enums.RfidStatusEnum;
import com.allcore.netty.mapper.RfidBindMapper;
import com.allcore.netty.mapper.SpareMapper;
import com.allcore.netty.mapper.UavMapper;
import com.allcore.netty.service.RfidBindService;
import com.allcore.netty.vo.RfidBindVO;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import net.logstash.logback.encoder.org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;


@Service
public class RfidBindServiceImpl extends ZxhcServiceImpl<RfidBindMapper, RfidBindEntity> implements RfidBindService {

	@Resource
	private RfidBindMapper rfidBindMapper;

	@Resource
	private AllcoreRedis allcoreRedis;

	@Resource
	private SpareMapper spareMapper;

	@Resource
	private UavMapper uavMapper;



	@Override
	public IPage<RfidBindVO> selectPage(RfidBindDTO rfidBindDto, IPage<RfidBindVO> page) {
		rfidBindDto.setDeptCode(AuthUtil.getDeptCode());
		List<RfidBindVO> list = rfidBindMapper.queryList(rfidBindDto,page);
		return page.setRecords(list);
	}

	@Override
	public Boolean updateToWhitelist(String rfidGuid) {
		RfidBindEntity rfidBindEntity = new RfidBindEntity();
		rfidBindEntity.setBelongCode(RfidBelongEnum.WHITE_LIST.getKey());
		LambdaUpdateWrapper<RfidBindEntity> wrapper = new LambdaUpdateWrapper<>();
		wrapper.eq(RfidBindEntity::getRfidGuid,rfidGuid);
		this.update(rfidBindEntity,wrapper);
		return true;
	}

	@Override
	public Boolean updateToBlacklist(String rfidGuid) {
		List<String> list = new ArrayList<>();
		list.add(rfidGuid);
		return unbindRfid(list);
	}

	@Override
	public Boolean removeByGuid(String rfidGuid) {
		 //rfidBindMapper.removeByGuid(rfidGuid);
		RfidBindEntity rfidBindEntity = new RfidBindEntity();
		rfidBindEntity.setBindStatus(RfidStatusEnum.UNBOUND.getKey());
		rfidBindEntity.setBelongCode(RfidBelongEnum.NOT_JOIN_LIST.getKey());
		LambdaUpdateWrapper<RfidBindEntity> updateWrapper = new LambdaUpdateWrapper<>();
		updateWrapper.eq(RfidBindEntity::getRfidGuid, rfidGuid).set(RfidBindEntity::getDeviceGuid, null).set(RfidBindEntity::getDeviceType, null);
		return this.update(rfidBindEntity,updateWrapper) && updateRfid(rfidGuid);
	}

	@Override
	public String addRfidBind(RfidBindAddDTO dto) {
		QueryWrapper<RfidBindEntity> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(RfidBindEntity::getRfidGuid,dto.getRfidGuid());
		List<RfidBindEntity> list = this.list(wrapper);
		RfidBindEntity rfidBindEntity = new RfidBindEntity();
		rfidBindEntity.setDeviceGuid(dto.getDeviceGuid());
		rfidBindEntity.setDeviceType(dto.getDeviceType());
		rfidBindEntity.setBindStatus(RfidStatusEnum.BOUND.getKey());
		if (list.size() == 0){
			rfidBindEntity.setRfidGuid(dto.getRfidGuid());
			if (StringUtils.isNoneEmpty(dto.getDeviceGuid())){
				rfidBindEntity.setBelongCode(RfidBelongEnum.WHITE_LIST.getKey());
			}else {
				rfidBindEntity.setBelongCode(RfidBelongEnum.NOT_JOIN_LIST.getKey());
				rfidBindEntity.setBindStatus(RfidStatusEnum.UNBOUND.getKey());
			}
			this.save(rfidBindEntity);
		}else{
			LambdaUpdateWrapper<RfidBindEntity> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(RfidBindEntity::getRfidGuid,dto.getRfidGuid());
			this.update(rfidBindEntity,updateWrapper);
			return list.get(0).getPosition();
		}
		return null;
	}

	@Override
	public List<RfidBindEntity> selectReserveStatus(List<String> rfids) {
		QueryWrapper<RfidBindEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(RfidBindEntity::getRfidGuid,rfids);
		List<RfidBindEntity> rfidBindEntities = rfidBindMapper.selectList(queryWrapper);
		List<String> collect = rfidBindEntities.stream().map(RfidBindEntity::getRfidGuid).collect(Collectors.toList());
		List<String> reduce1 = rfids.stream().filter(item -> !collect.contains(item)).collect(Collectors.toList());
		for (int i = 0; i < reduce1.size(); i++) {
			RfidBindEntity rfidBindEntity = new RfidBindEntity();
			rfidBindEntity.setBindStatus(RfidStatusEnum.UNBOUND.getKey());
			rfidBindEntity.setPosition(allcoreRedis.get(reduce1.get(i)));
			rfidBindEntity.setRfidGuid(reduce1.get(i));
			rfidBindEntity.setBelongCode(RfidBelongEnum.WHITE_LIST.getKey());
			this.save(rfidBindEntity);
		}
		return rfidBindEntities;
	}

	@Override
	public Boolean batchAddRfidBind(List<RfidBindAddDTO> list) {
		List<String> rfidGuids = list.stream().map(RfidBindAddDTO::getRfidGuid).collect(Collectors.toList());
		QueryWrapper<RfidBindEntity> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().in(RfidBindEntity::getRfidGuid,rfidGuids);
		List<RfidBindEntity> list1 = this.list(queryWrapper);
		if (list1.size()>0){
			List<RfidBindAddDTO> rfidBindAddDTOS = BeanUtil.copyProperties(list1, RfidBindAddDTO.class);
			list.removeAll(rfidBindAddDTOS);
		}
		for (RfidBindAddDTO dto:
		list) {
			RfidBindEntity rfidBindEntity = new RfidBindEntity();
			rfidBindEntity.setPosition(dto.getPosition());
			rfidBindEntity.setRfidGuid(dto.getRfidGuid());
			rfidBindEntity.setBindStatus(RfidStatusEnum.UNBOUND.getKey());
			rfidBindEntity.setBelongCode(RfidBelongEnum.WHITE_LIST.getKey());
			this.save(rfidBindEntity);
		}
		return true;
	}

	@Override
	public Boolean unbindRfid(List<String> rfidGuids) {
		for (String rfidGuid:
		rfidGuids) {
			RfidBindEntity rfidBindEntity = new RfidBindEntity();
			rfidBindEntity.setBindStatus(RfidStatusEnum.UNBOUND.getKey());
			rfidBindEntity.setBelongCode(RfidBelongEnum.BLACK_LIST.getKey());
			LambdaUpdateWrapper<RfidBindEntity> updateWrapper = new LambdaUpdateWrapper<>();
			updateWrapper.eq(RfidBindEntity::getRfidGuid, rfidGuid).set(RfidBindEntity::getDeviceGuid, null).set(RfidBindEntity::getDeviceType, null);
			this.update(rfidBindEntity,updateWrapper);
			updateRfid(rfidGuid);
		}
		return true;
	}


	public boolean updateRfid(String rfidGuid){
		spareMapper.update(new SpareEntity(),new LambdaUpdateWrapper<SpareEntity>()
			.set(SpareEntity::getRfidGuid,null).set(SpareEntity::getState, MainBizEnum.STATE_0.getCode())
			.set(SpareEntity::getEquipmentCabinetGuid,null).set(SpareEntity::getFloorGuid,null)
			.set(SpareEntity::getPosition,null).eq(SpareEntity::getRfidGuid,rfidGuid));
		uavMapper.update(new UavEntity(),new LambdaUpdateWrapper<UavEntity>()
			.set(UavEntity::getRfidGuid,null).set(UavEntity::getState,MainBizEnum.STATE_0.getCode())
			.set(UavEntity::getEquipmentCabinetGuid,null).set(UavEntity::getFloorGuid,null)
			.set(UavEntity::getPosition,null).eq(UavEntity::getRfidGuid,rfidGuid));
		return true;
	}

}
