package com.allcore.netty.mapper;

import com.allcore.netty.dto.ReceiveStatisticalDTO;
import com.allcore.netty.entity.ReceiveLogEntity;
import com.allcore.netty.vo.ModelZhVO;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 领用归还表
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-22 10:21:29
 */
@Mapper
public interface ReceiveLogMapper extends BaseMapper<ReceiveLogEntity> {

	List<Map<String, Object>> getReceiveLog(@Param("startTime") Date startTime, @Param("endTime") Date endTime, @Param("deptCode") String deptCode,
											@Param("receiveType") String receiveType, @Param("storeRoomGUID") String storeRoomGUID);

	/**
	 * 设备归还情况统计
	 *
	 * @param wrapper
	 * @return
	 */
	List<ReceiveStatisticalDTO> receiveStatistical(@Param(Constants.WRAPPER) Wrapper wrapper);

	/**
	 * 查询个人名下未归还设备
	 *
	 * @param userId
	 * @return
	 */
	List<ReceiveLogEntity> selectReceiveType(String userId);

	/**
	 * 查询个人名下未归还设备
	 *
	 * @param userId
	 * @return
	 */
	List<ReceiveLogEntity> selectReturnType(String userId, Date time, String deviceGuid);

	String selectPic(String deviceType, String deviceModel);

	List<String> selectUsers(String receiveOperator);

	int insertSelective(ReceiveLogEntity receiveLogEntity);

    List<ModelZhVO> selectModelZh();

	List<String> selectStoreGuids(String deptCode);
}
