package com.allcore.netty.service.impl;

import cn.hutool.core.util.IdUtil;
import com.allcore.common.base.ZxhcServiceImpl;
import com.allcore.common.cache.ThreeDeptInfo;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.mp.support.AllcorePage;
import com.allcore.core.secure.AllcoreUser;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.netty.dto.AccessUserDTO;
import com.allcore.netty.dto.RoomUserDTO;
import com.allcore.netty.entity.RoomUserEntity;
import com.allcore.netty.mapper.RoomUserMapper;
import com.allcore.netty.service.RoomUserService;
import com.allcore.netty.vo.AccessUserVO;
import com.allcore.netty.vo.RoomUserAddVO;
import com.allcore.netty.vo.RoomUserVO;
import com.allcore.system.entity.Dept;
import com.allcore.system.feign.ISysClient;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;


@Service
public class RoomUserServiceImpl extends ZxhcServiceImpl<RoomUserMapper, RoomUserEntity> implements RoomUserService {


	@Resource
	private ISysClient sysClient;

	@Resource
	private IUserClient userClient;

	@Override
	@Transactional
	public void saveRoomUser(RoomUserDTO dto) {
		for (String userGuid : dto.getUserGuids()) {
			//效验当前人是否存在
			RoomUserEntity entity = this.baseMapper.selectOne(new LambdaQueryWrapper<RoomUserEntity>().eq(RoomUserEntity::getUserGuid, userGuid));
			if (!Objects.isNull(entity)) {
				throw new ServiceException(userGuid + "为已添加人员，无法重复添加！");
			}
			User user = UserCache.getUser(userGuid);
			Dept dept = sysClient.getDept(user.getDeptId()).getData();
			AllcoreUser currentUser = AuthUtil.getUser();
			RoomUserEntity roomUserEntity = new RoomUserEntity();
			roomUserEntity.setRoomUserGuid(IdUtil.simpleUUID());
			roomUserEntity.setStoreRoomGuid(dto.getStoreRoomGuid());
			roomUserEntity.setUserGuid(userGuid);
			roomUserEntity.setCreateUser(currentUser.getUserId());
			roomUserEntity.setCreateTime(new Date());
			roomUserEntity.setUpdateUser(currentUser.getUserId());
			roomUserEntity.setUpdateTime(new Date());
			roomUserEntity.setCreateDept(currentUser.getDeptId());
			roomUserEntity.setDeptCode(dept.getDeptCode());
			this.baseMapper.insertSelective(roomUserEntity);
		}
	}

	@Override
	public void delRoomUser(List<String> roomUserGuids) {
		this.baseMapper.delete(new LambdaQueryWrapper<RoomUserEntity>().in(RoomUserEntity::getRoomUserGuid,roomUserGuids));
	}

	@Override
	public RoomUserAddVO getList(String storeRoomGuid) {
		List<RoomUserEntity> userEntityList = this.baseMapper.selectList(new LambdaQueryWrapper<RoomUserEntity>().eq(RoomUserEntity::getStoreRoomGuid, storeRoomGuid));
		if (Objects.isNull(userEntityList)) {
			userEntityList = new ArrayList<>();
		}
		int serialNumber = 0;
		RoomUserAddVO roomUserAddVo = new RoomUserAddVO();
		List<RoomUserVO> roomUserVOS = new ArrayList<>();
		for (RoomUserEntity roomUserEntity : userEntityList) {
			User user = UserCache.getUser(roomUserEntity.getUserGuid());
			ThreeDeptInfo threeDeptNames= CommonUtil.getThreeDeptNameByDeptId(user.getDeptId());
			RoomUserVO roomUserVo = new RoomUserVO();
			roomUserVo.setSerialNumber(++serialNumber);
			roomUserVo.setThirdDeptName(CommonUtil.isEmpty(threeDeptNames.getThirdName()) ? "" : threeDeptNames .getThirdName());
			roomUserVo.setPostId(user.getCode());
			roomUserVo.setRealName(user.getRealName());
			roomUserVo.setUserGuid(roomUserEntity.getUserGuid());
			roomUserVo.setRoomUserGuid(roomUserEntity.getRoomUserGuid());
			roomUserVOS.add(roomUserVo);
		}
		roomUserAddVo.setAddCount(roomUserVOS.size());
		roomUserAddVo.setRoomUserVOS(roomUserVOS);
		return roomUserAddVo;
	}

	@Override
	public IPage<AccessUserVO> userPageList(AccessUserDTO accessUserDto, IPage<AccessUserVO> page) {
		User user = new User();
		user.setRealName(accessUserDto.getRealName());
		int current = new Long(page.getCurrent()).intValue();
		int size = new Long(page.getSize()).intValue();
		String deptCode = Objects.isNull(accessUserDto.getDeptCode())|| "".equals(accessUserDto.getDeptCode()) ? "1000" : accessUserDto.getDeptCode();
		R<AllcorePage<User>> allcorePageR = userClient.userListByDeptCodeOfPage(user, deptCode, "000000", current, size);
		int serialNumber = (current - 1) * size;
		List<AccessUserVO> accessUserVOS = new ArrayList<>();
		for (User item : allcorePageR.getData().getRecords()) {
			ThreeDeptInfo threeDeptNames= CommonUtil.getThreeDeptNameByDeptId(item.getDeptId());
			AccessUserVO accessUserVo = new AccessUserVO();
			accessUserVo.setSerialNumber(++serialNumber);
			accessUserVo.setUserGUID(item.getId());
			accessUserVo.setFirstDeptName(CommonUtil.isEmpty(threeDeptNames.getFirstName()) ? "" : threeDeptNames .getFirstName());
			accessUserVo.setSecondDeptName(CommonUtil.isEmpty(threeDeptNames.getSecondName()) ? "" : threeDeptNames.getSecondName());
			accessUserVo.setThirdDeptName(CommonUtil.isEmpty(threeDeptNames.getThirdName()) ? "" : threeDeptNames .getThirdName());
			accessUserVo.setPostId(item.getCode());
			accessUserVo.setRealName(item.getRealName());
			accessUserVOS.add(accessUserVo);
		}
		page.setRecords(accessUserVOS);
		page.setTotal(allcorePageR.getData().getTotal());
		return page;
	}
}
