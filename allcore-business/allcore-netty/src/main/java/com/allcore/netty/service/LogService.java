package com.allcore.netty.service;

import com.allcore.common.base.ZxhcService;
import com.allcore.core.tool.api.R;
import com.allcore.netty.dto.LogEntityDTO;
import com.allcore.netty.entity.LogEntity;
import com.baomidou.mybatisplus.core.metadata.IPage;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 操作日志
 *
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2022-11-22 10:21:29
 */
public interface LogService extends ZxhcService<LogEntity> {

	/**
	 * 列表查询
	 * @param page
	 * @param logEntity
	 * @return
	 */
    IPage<LogEntity> pageList(IPage<LogEntity> page, LogEntityDTO logEntity);

	/**
	 * 新增
	 * @param logEntity
	 * @return
	 */
	R add(LogEntity logEntity);

	/**
	 * 删除
	 * @param guids
	 * @return
	 */
	R remove(String guids);


	/**
	 * 批量导出
	 * @param   logEntityDTO
	 * @param request
	 * @param response
	 */
	void exportExcel(LogEntityDTO logEntityDTO, HttpServletRequest request, HttpServletResponse response);


}

