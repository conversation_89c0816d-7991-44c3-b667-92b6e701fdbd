package com.allcore.external.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * PlatformProperties
 *
 * <AUTHOR>
 */
@Data
@ConfigurationProperties(prefix = "machine")
public class MachineProperties {

	private String accessKeyId;

	private String accessKeySecret;

	/**
	 * RAS
	 */
	private String rasHttpHost; //人员定位设备http地址
	private String rasWsHost; //人员定位ws地址
	private String rasPositionWsType; //选择推送类型

}
