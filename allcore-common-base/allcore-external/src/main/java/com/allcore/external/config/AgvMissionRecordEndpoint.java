package com.allcore.external.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.allcore.external.handler.MqttMessageHandler;
import com.allcore.external.handler.WebSocketMessageHandler;
import com.allcore.external.vo.sgc.AgvMissionRecordVO;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @program: bl
 * @description:
 * @author: fanxiang
 * @create: 2025-06-08 12:59
 **/

@Slf4j
@Component
@AllArgsConstructor
public class AgvMissionRecordEndpoint extends AbstractMessagingEndpoint{

    private final SgcProperties sgcProperties;

    private final WebSocketClientManager webSocketClientManager;

    @Override
    String getBusinessType() {
        return "agv_mission";
    }

    @Override
    public String getWebSocketUrl() {
        return "ws://***************:8093/agv/missionRecord";
    }

    @Override
    String getToken() {
        return "";
    }

    @Override
    String getTopic() {
        return "/topic/agv_mission";
    }

    @Override
    public WebSocketMessageHandler getWebSocketMessageHandler() {
        return (msg) -> {
            log.info("收到AGV任务记录服务端消息: {}", msg);

            try {
                String data;
                if (JSON.isValid(msg)) {
                    JSONObject jsonObject = JSON.parseObject(msg);
                    String code = jsonObject.getString("code");

                    if ("DATA".equals(code)) {
                        // 处理任务记录数据响应
                        JSONObject dataObj = jsonObject.getJSONObject("data");
                        if (dataObj != null && dataObj.containsKey("data")) {
                            JSONObject missionData = dataObj.getJSONObject("data");
                            if(missionData==null){
                                data="{}";
                            }

                            // 转换为VO对象
                          else{  AgvMissionRecordVO missionRecord = JSON.parseObject(missionData.toJSONString(), AgvMissionRecordVO.class);

                            log.info("解析到AGV任务记录: 任务名称={}, 状态={}, 完成率={}",
                                    missionRecord.getMissionName(),
                                    missionRecord.getStatus(),
                                    missionRecord.getFinishRate());

                            data = JSON.toJSONString(missionRecord);}
                        } else {
                            data = msg;
                        }
                    } else {
                        // 其他类型的响应
                        data = msg;
                    }
                } else {
                    data = msg;
                }

                // 转发给前端（前端订阅 "/topic/agv_mission"）
                simpMessagingTemplate.convertAndSend("/topic/agv_mission", data);

            } catch (Exception e) {
                log.error("处理AGV任务记录消息异常", e);
                // 发送错误信息给前端
                JSONObject errorMsg = new JSONObject();
                errorMsg.put("type", "error");
                errorMsg.put("message", "消息处理异常: " + e.getMessage());
                simpMessagingTemplate.convertAndSend("/topic/agv_mission", errorMsg.toJSONString());
            }
        };

    }

    @Override
    MqttMessageHandler getMqttMessageHandler() {
        return null;
    }

    /**
     * 发送查询AGV任务记录的请求
     * 根据图片显示，需要发送包含IP的查询请求
     *
     * @param ip AGV的IP地址
     * @return 是否发送成功
     */
    public boolean queryMissionRecord(String ip) {
        if (webSocketClientManager == null) {
            log.warn("WebSocketClientManager未注入，无法发送消息");
            return false;
        }

        if (!isConnected()) {
            log.warn("AGV WebSocket连接未建立，无法发送查询请求");
            return false;
        }

        // 构造查询请求，根据图片中的入参格式
        JSONObject request = new JSONObject();
        request.put("code", "DATA");

        JSONObject data = new JSONObject();
        data.put("ip", ip);
        request.put("data", data);

        String clientId = getBusinessType();
        boolean success = webSocketClientManager.sendMessage(clientId, request.toJSONString());

        if (success) {
            log.info("AGV任务记录查询请求已发送，IP: {}, 等待服务端响应...", ip);
        } else {
            log.error("AGV任务记录查询请求发送失败，IP: {}", ip);
        }

        return success;
    }

    /**
     * 检查连接是否可用
     *
     * @return 连接是否可用
     */
    public boolean isConnected() {
        if (webSocketClientManager == null) {
            log.warn("WebSocketClientManager为null");
            return false;
        }
        String clientId = getBusinessType();
        boolean connected = webSocketClientManager.isConnected(clientId);
        log.debug("检查AGV WebSocket连接状态 - ClientId: {}, Connected: {}", clientId, connected);
        return connected;
    }
}
