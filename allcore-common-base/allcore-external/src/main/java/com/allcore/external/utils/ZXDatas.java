package com.allcore.external.utils;

import lombok.Data;

import java.util.List;

/**
 * @author: <PERSON><PERSON>
 * @version: 1.0
 * @date: 2023/03/13
 **/
@Data
public class ZXDatas {
    private String EquivalentFocalLength;
    private String LineName;
    private String TowerNum;
    private String TowerVol;
    private String droneType;
    private List<?> items;
    private String mission_index;
    private String mission_time;
    private String towerTypeName;
    private String version;
    private int wy_count;
    private Integer deviceType;
    private String deviceGuid;
    private ZXIntoSafePoint intoSafePoint;
    private ZXOutSafePoint outSafePoint;
}

