package com.allcore.external.config;

import lombok.Getter;
import lombok.Setter;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 1. <AUTHOR>
 2. @date 2022/6/29 20:42
 */
@Component
@ConfigurationProperties(prefix = "mqtt")
@Getter
@Setter
public class MqttConfiguration {

    private static final Logger log = LoggerFactory.getLogger(MqttConfiguration.class);
//    @Value("${mqtt.host}")
    String host;
//    @Value("${mqtt.username}")
    String username;
//    @Value("${mqtt.password}")
    String password;
//    @Value("${mqtt.clientId}")
    String clientId;
//    @Value("${mqtt.timeout}")
    int timeOut;
//    @Value("${mqtt.keepalive}")
    int keepAlive;
//    @Value("#{'${mqtt.topic:}'.split(',')}")
    List<String> topic;
//    @Value("${mqtt.topic1}")
    String topic1;
//    @Value("${mqtt.topic2}")
    String topic2;
//    @Value("${mqtt.topic3}")
    String topic3;
//    @Value("${mqtt.topic4}")
    String topic4;


    @Bean//注入spring
    public MyMQTTClient myMqttClient() {
        clientId = clientId + "_" + System.currentTimeMillis();
        MyMQTTClient myMqttClient = new MyMQTTClient(host, username, password, clientId, timeOut, keepAlive);
        for (int i = 0; i < 10; i++) {
            try {
                myMqttClient.connect();
                //不同的主题
             //   myMQTTClient.subscribe(topic1, 1);
             //   myMQTTClient.subscribe(topic2, 1);
             //   myMQTTClient.subscribe(topic3, 1);
             //   myMQTTClient.subscribe(topic4, 1);
                return myMqttClient;
            } catch (MqttException e) {
                log.error("MQTT connect exception,connect time = " + i);
                try {
                    Thread.sleep(2000);
                } catch (InterruptedException e1) {
                    e1.printStackTrace();
                }
            }
        }
        return myMqttClient;
    }
}
