package com.allcore.external.utils;

import org.springframework.mock.web.MockMultipartFile;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 21 5月 2025
 */
public class UrlToMultipartFileConverter {
    public static MultipartFile downloadFileAsMultipartFile(String fileUrl) throws IOException {
        // 打开 URL 连接
        URL url = new URL(fileUrl);
        URLConnection connection = url.openConnection();
        String contentType = connection.getContentType();
        String fileName = extractFileName(fileUrl);

        try (InputStream inputStream = connection.getInputStream()) {
            // 使用 MockMultipartFile 构造 MultipartFile
            return new MockMultipartFile(
                    "file",         // form field name
                    fileName,       // original file name
                    contentType,    // content type
                    inputStream     // file content
            );
        }
    }

    private static String extractFileName(String fileUrl) {
        String[] parts = fileUrl.split("/");
        return parts[parts.length - 1];
    }
}
