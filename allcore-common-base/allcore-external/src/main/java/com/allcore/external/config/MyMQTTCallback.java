package com.allcore.external.config;

import cn.hutool.core.util.CharsetUtil;
import com.allcore.common.constant.CommonConstant;
import com.allcore.core.cache.utils.CacheUtil;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.tool.jackson.JsonUtil;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.external.cache.NestCache;
import com.allcore.external.constant.ExternalConstant;
import com.allcore.external.dto.*;
import com.allcore.external.entity.MessageStruct;
import com.allcore.external.entity.NestTask;
import com.allcore.external.service.INestTaskService;
import com.allcore.external.service.MachineNestOperateService;
import com.allcore.external.tool.MqTool;
import com.allcore.external.utils.SpringUtils;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallbackExtended;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.util.CollectionUtils;

import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2022/6/29 20:43
 */
public class MyMQTTCallback implements MqttCallbackExtended {

    private MqTool mqTool = SpringUtils.getBean(MqTool.class);

    private INestTaskService nestTaskService = SpringUtils.getBean(INestTaskService.class);

    // 手动注入
    private MqttConfiguration mqttConfiguration = SpringUtils.getBean(MqttConfiguration.class);
    private MachineNestOperateService machineNestOperateService = SpringUtils.getBean(MachineNestOperateService.class);
    private MessagingEndpointRegistry messagingEndpointRegistry = SpringUtils.getBean(MessagingEndpointRegistry.class);

    private static final Logger log = LoggerFactory.getLogger(MyMQTTCallback.class);
    private final String AIR_PORT = "airport:cache";
    private final String AIR_TASK = "airport:task";

    private MyMQTTClient myMqttClient;

    public MyMQTTCallback(MyMQTTClient myMqttClient) {
        this.myMqttClient = myMqttClient;
    }

    /**
     * 丢失连接，可在这里做重连 只会调用一次
     *
     * @param throwable
     */
    @Override
    public void connectionLost(Throwable throwable) {
        throwable.printStackTrace();
        log.error("mqtt connectionLost 连接断开，5S之后尝试重连: {}", throwable.getMessage());
        long reconnectTimes = 1;
        while (true) {
            try {
                if (MyMQTTClient.getClient().isConnected()) {
                    // 判断已经重新连接成功 需要重新订阅主题 可以在这个if里面订阅主题 或者 connectComplete（方法里面） 看你们自己选择
                    log.warn("mqtt reconnect success end  重新连接  重新订阅成功");
                    return;
                }
                reconnectTimes += 1;
                log.warn("mqtt reconnect times = {} try again...  mqtt重新连接时间 {}", reconnectTimes, reconnectTimes);
                MyMQTTClient.getClient().reconnect();
            } catch (MqttException e) {
                log.error("mqtt断连异常", e);
            }
            try {
                Thread.sleep(5000);
            } catch (InterruptedException e1) {
            }
        }
    }

    /**
     * @param topic
     * @param mqttMessage
     * @throws Exception subscribe后得到的消息会执行到这里面
     */
    @Override
    public void messageArrived(String topic, MqttMessage mqttMessage) throws Exception {
        log.info("接收消息主题 : {}，接收消息内容 : {}", topic, new String(mqttMessage.getPayload()));
        // 接收上报机巢数据
        Optional.ofNullable(messagingEndpointRegistry.getEndpointByTopic(topic))
                .ifPresent(endpoint ->
                        endpoint.getMqttMessageHandler().onMessage(new String(mqttMessage.getPayload(), StandardCharsets.UTF_8))
                );
        if (ExternalConstant.MACHINE_NEST_DATA_REPORT.equals(topic)) {
            // 获取JsonNode
            // JsonNode jsonNode = JsonUtil.readTree(new String(mqttMessage.getPayload(), CharsetUtil.UTF_8));
            // 获取Bean
            NestDataReportDTO msg =
                    JsonUtil.readValue(new String(mqttMessage.getPayload(), CharsetUtil.UTF_8), NestDataReportDTO.class);
            // 获取Map
            // Map maps = (Map) JSON.parse(new String(mqttMessage.getPayload(), CharsetUtil.UTF_8));
            // 你自己的业务接口
//            insertMachineNestDataReport(msg);
        }
        if (ExternalConstant.UAV_DATA_REPORT.equals(topic)) {
            UavDataReportDTO msg =
                    JsonUtil.readValue(new String(mqttMessage.getPayload(), CharsetUtil.UTF_8), UavDataReportDTO.class);
            // 判断无人机是否抵达并发送消息
            // nestService.judgeDroneArrive(msg);
//            insertUavDataReport(msg);
        }

    }

    private void insertMachineNestDataReport(NestDataReportDTO msg) {
        try {
            nestTaskService.saveMongoNest(msg);
            NestCache.putNestDataReportDTO(msg);
            // 作业中
            if (StringPool.ONE.equals(msg.getNestStatus())) {
                // 更新机巢任务名称
                nestTaskService.updateNestTaskName(msg.getTaskName(), msg.getTaskId());
            }
            // 空闲
            if (StringPool.ZERO.equals(msg.getNestStatus())) {
                // 判断当前的空闲机巢是否有待执行的任务，有，那就下发
                NestTask hasTask = nestTaskService.getById(msg.getTaskId());
                // 机场id
                if (Func.isNotEmpty(hasTask)) {
                    String nestId = hasTask.getAirportNestId();
                    List<NestTaskDTO> nestTaskDTOList = CacheUtil.get(AIR_PORT, AIR_TASK, nestId, List.class);
                    if (CollectionUtil.isNotEmpty(nestTaskDTOList)) {
                        sendTaskToMachine(nestTaskDTOList);
                        CacheUtil.put(AIR_PORT, AIR_TASK, nestId, nestTaskDTOList.remove(0));
                    }
                }
                // 机场的空闲任务
                /*List<NestTask> list = nestTaskService.list(new LambdaQueryWrapper<NestTask>()
                    .eq(NestTask::getAirportNestId, hasTask.getAirportNestId()).eq(NestTask::getTaskStatus, "0"));
                if (CollectionUtil.isNotEmpty(list)) {
                    NestTask one = list.get(0);
                }*/
            }

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("保存异常");
        }

    }

    @Async
    public Boolean sendTaskToMachine(List<NestTaskDTO> nestTaskDTOList) {
        if (CollectionUtil.isEmpty(nestTaskDTOList)) {
            return true;
        }
        // 下发第一条任务
        NestTaskDTO e = nestTaskDTOList.get(0);
        WorkOrderDeliverDTO dto = new WorkOrderDeliverDTO();
        dto.setMachineNestCode(e.getSnCode());
        dto.setTaskId(e.getId());
        log.info("=============================任务:ID为{}开始下发执行=======================", e.getId());
        dto.setTaskType(StringPool.ONE);
        dto.setFileType("JSON");
        dto.setActionTriggerType("reachPoint");
        dto.setActionTriggerParam("10");
        // 查询文件
        dto.setFlyFilePath(e.getFilePath());
        MachineNestBrandConfigDTO configDTO = new MachineNestBrandConfigDTO();
        configDTO.setMockFlag(true);
        configDTO.setAccessKeyId(e.getAccessKeyId());
        configDTO.setAccessKeySecret(e.getAccessKeySecret());
        configDTO.setServiceAddress(e.getServiceAddress());
        dto.setConfigDTO(configDTO);
        machineNestOperateService.workOrderDeliver(dto);
        return true;
    }

    private void insertUavDataReport(UavDataReportDTO msg) {
        try {
            nestTaskService.saveMongoUav(msg);

            // 无人机起飞的时候 修改机巢任务为进行中、修改工单机巢任务为进行中
            // 无人机降落完成的时候 修改机巢任务为已完成、且若所有机巢任务都已完成 把对应工单状态改已完成
            // 若无人机自监测未通过 修改机巢任务为异常任务
            if (StringUtil.isNotBlank(msg.getFlightMode())) {
                switch (msg.getFlightMode()) {
                    // 起飞
                    case "1":
                        nestTaskService.updateTaskStatus(StringPool.ONE, msg.getTaskId());
                        // DbUtil.updateOrderTaskStatus(StringPool.ONE, msg.getTaskId());

                        break;
                    // 降落完成
                    case "9":
                        nestTaskService.updateTaskStatus(CommonConstant.TWO_NUM, msg.getTaskId());
                        // DbUtil.updateOrderTaskStatus(CommonConstant.TWO_NUM, msg.getTaskId());
                        break;
                    default:
                        break;
                }
            }
            if (StringUtil.isNotBlank(msg.getDroneStatus())) {
                switch (msg.getDroneStatus()) {
                    // 自检未通过
                    case "1":
                        nestTaskService.updateTaskStatus(CommonConstant.THREE_NUM, msg.getTaskId());
                        break;
                    default:
                        break;
                }
            }

            log.info("====================发送 websocket=========================================");
            // TODO 测试异步是否成功
            // 发送webSocket消息 用于无人机信息里面的实时数据显示、机巢流程的状态显示
            // WebSocketUtil.sendMsgToAll(msg);
            MessageStruct struct = new MessageStruct();
            struct.setMessageBody(msg);
            String uavSn = msg.getDroneId();
            // 发给对应无人机的频道
            mqTool.sendMsgToAllByTopicEnd(uavSn, struct);

        } catch (Exception e) {
            e.printStackTrace();
            throw new ServiceException("保存异常");
        }

    }

    /**
     * 连接成功后的回调 可以在这个方法执行 订阅主题 生成Bean的 MqttConfiguration方法中订阅主题 出现bug 重新连接后 主题也需要再次订阅 将重新订阅主题放在连接成功后的回调 比较合理
     *
     * @param reconnect
     * @param serverURI
     */
    @Override
    public void connectComplete(boolean reconnect, String serverURI) {
        log.info("MQTT 连接成功，连接方式：{}", reconnect ? "重连" : "直连");

        // 订阅主题
        if (!CollectionUtils.isEmpty(mqttConfiguration.getTopic())) {
            mqttConfiguration.topic.forEach(t -> {
                myMqttClient.subscribe(t, 1);
            });
        }
//        myMqttClient.subscribe(mqttConfiguration.topic1, 1);
//        myMqttClient.subscribe(mqttConfiguration.topic2, 1);
        // myMQTTClient.subscribe(mqttConfiguration.topic3, 0);
        // myMQTTClient.subscribe(mqttConfiguration.topic4, 1);
    }

    /**
     * 消息到达后 subscribe后，执行的回调函数
     *
     * @param s
     * @param mqttMessage
     * @throws Exception
     */
    /**
     * publish后，配送完成后回调的方法
     *
     * @param iMqttDeliveryToken
     */
    @Override
    public void deliveryComplete(IMqttDeliveryToken iMqttDeliveryToken) {
        log.info("==========deliveryComplete={}==========", iMqttDeliveryToken.isComplete());
    }
}
