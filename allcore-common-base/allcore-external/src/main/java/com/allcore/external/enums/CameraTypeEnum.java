package com.allcore.external.enums;

/**
 * <AUTHOR>
 * @date 2025/5/15 16:46
 **/
public enum CameraTypeEnum {
    CAMERA_TYPE_1(0, "枪机"),
    CAMERA_TYPE_2(1, "半球"),
    CAMERA_TYPE_3(2, "快球"),
    CAMERA_TYPE_4(3, "云台枪机"),
;

    private Integer code;
    private String value;

    CameraTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

   public Integer getCode() { return code;}
    public String getValue() { return value;}
     public static String getValueByCode(Integer code) {
        for (CameraTypeEnum value : CameraTypeEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.getValue();
            }
        }
        return null;
    }


}
