package com.allcore.external.config;

import com.alibaba.fastjson.JSON;
import com.allcore.common.enums.BizDictEnum;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.external.dto.IntegratedLineAlarmDTO;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.entity.LineConfig;
import com.allcore.external.handler.MqttMessageHandler;
import com.allcore.external.handler.WebSocketMessageHandler;
import com.allcore.external.service.IAlarmInfoService;
import com.allcore.external.service.IIntegratedLineService;
import com.allcore.external.service.LineConfigService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

import static com.allcore.external.constant.ExternalConstant.LINE_WARNING;

/**
 * 天创机器人消息处理器
 *
 * <AUTHOR>
 * @date 2025/4/29 10:31
 **/
@Slf4j
@Component
public class IntegratedLineMessageEndpoint extends AbstractMessagingEndpoint {

    private final IIntegratedLineService iIntegratedLineService;

    private final IAlarmInfoService alarmInfoService;

    private final LineConfigService lineConfigService;

    public IntegratedLineMessageEndpoint(IIntegratedLineService iIntegratedLineService, IAlarmInfoService alarmInfoService, LineConfigService lineConfigService) {
        this.iIntegratedLineService = iIntegratedLineService;
        this.alarmInfoService = alarmInfoService;
        this.lineConfigService = lineConfigService;
    }

    @Override
    String getBusinessType() {
        return "integratedLine";
    }

    @Override
    public String getWebSocketUrl() {
        return "";
    }

    @Override
    String getToken() {
        return "";
    }

    @Override
    String getTopic() {
        return LINE_WARNING;
    }

    @Override
    public WebSocketMessageHandler getWebSocketMessageHandler() {
        return null;
    }

    @Override
    MqttMessageHandler getMqttMessageHandler() {
        return (msg) -> {
            log.info("收到订阅集电线路告警的消息: {}", msg);
            if (JSON.isValid(msg)) {
                IntegratedLineAlarmDTO integratedLineAlarmDTO = JSON.parseObject(msg, IntegratedLineAlarmDTO.class);
                String data = JSON.toJSONString(integratedLineAlarmDTO);
                // 将第三方实时消息转发给前端
                simpMessagingTemplate.convertAndSend("/topic/integratedLine", data);
                //todo 存入mongo,目前简单实现
                iIntegratedLineService.saveMongoAlarm(integratedLineAlarmDTO);
                List<AlarmInfo> alarmInfoList = new ArrayList<>();
                integratedLineAlarmDTO.getData().forEach(d -> {
                    LineConfig one = lineConfigService.getOne(new LambdaQueryWrapper<LineConfig>().eq(LineConfig::getSite, d.getSite()));
                    String deptCode = "";
                    if (one != null) {
                        deptCode = one.getDeptCode();
                    }
                    AlarmInfo alarmInfo = AlarmInfo.builder()
                            //第三方自己定义了个东站，西站
                            .deptId(deptCode)
                            .deviceId(d.getLine())
                            .deviceName(d.getLine())
                            .deviceType(BizDictEnum.DEVICE_TYPE_INTEGRATE_LINE.getCode())
                            .alarmSource("integrated_line")
                            //todo 该字段未知，待定
                            .alarmType("real_time_type")
                            .alarmContent(d.getValue())
                            .alarmLevel("general")
                            .alarmStatus(0)
                            .alarmTime(DateUtil.fromDate(DateUtil.parse(integratedLineAlarmDTO.getTimeStamp(), "yyyy/MM/dd HH:mm:ss")))
                            .createTime(DateUtil.fromDate(DateUtil.now()))
                            .updateTime(DateUtil.fromDate(DateUtil.now()))
                            .build();
                    alarmInfoList.add(alarmInfo);
                });
                //存入告警表
                alarmInfoService.saveBatch(alarmInfoList);
            }
        };
    }
}
