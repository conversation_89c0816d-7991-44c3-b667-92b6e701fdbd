package com.allcore.external.utils;

import lombok.Data;

/**
 * @author: <PERSON><PERSON>
 * @version: 1.0
 * @date: 2023/03/13
 **/
@Data
public class ZXActions {
    private int action_index;
    private int action_mode;
    private double cam_pitch;
    private double cam_roll;
    private double cam_yaw;
    private int focal;
    private double gimbal_pitch;
    private double gimbal_roll;
    private double gimbal_yaw;
    private int opticalZoomFocalLength;
    private int photo_count;
    private int recoder_time;
    private String targetName;
}
