package com.allcore.external.config;

import com.allcore.common.config.ExceptionErrorDecoder;
import com.allcore.external.handler.TokenProvider;
import feign.Logger;
import feign.Response;
import feign.RetryableException;
import feign.codec.ErrorDecoder;
import org.springframework.cloud.client.loadbalancer.LoadBalanced;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.client.RestTemplate;

import java.util.List;

/**
 * <p></p>
 *
 * @author: sunkun
 * Date: 07 5月 2025
 */
@Configuration
public class FeignConfig {

    @Bean
    public ErrorDecoder errorDecoder() {
        return new ExceptionErrorDecoder();
    }

//    @Bean
    public ErrorDecoder errorDecoder(List<TokenProvider> providers) {
        return new ErrorDecoder() {
            private final ErrorDecoder defaultDecoder = new Default();

            @Override
            public Exception decode(String methodKey, Response response) {
                if (response.status() == 401) {
                    String clientName = methodKey.split("#")[0];
                    providers.stream()
                            .filter(p -> p.supports(clientName))
                            .forEach(TokenProvider::clearToken);
                    return new RetryableException(
                            response.status(),
                            "token expired, retrying",
                            response.request().httpMethod(),
                            null,
                            response.request()
                    );
                }
                return defaultDecoder.decode(methodKey, response);
            }
        };
    }

    @Bean
    @LoadBalanced
    public RestTemplate restTemplate() {
        return new RestTemplate();
    }

    @Bean
    Logger.Level feignLoggerLevel() {
        return Logger.Level.FULL;
    }
}
