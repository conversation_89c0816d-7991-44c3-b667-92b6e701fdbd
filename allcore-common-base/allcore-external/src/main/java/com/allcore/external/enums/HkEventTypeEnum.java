package com.allcore.external.enums;

/**
 * 海康智能安防事件类型
 * <AUTHOR>
 * @date 2025/5/14 10:31
 **/
public enum HkEventTypeEnum {

    EVENT_TYPE_131329(131329, "视频丢失"),
    EVENT_TYPE_131330(131330, "视频遮挡"),
    EVENT_TYPE_131331(131331, "移动侦测"),
    EVENT_TYPE_131612(131612, "场景变更"),
    EVENT_TYPE_131613(131613, "虚焦"),
    EVENT_TYPE_589825(589825, "报警输入"),
    EVENT_TYPE_196355(196355,"可视域事件"),
    EVENT_TYPE_851969(851969,"GPS采集"),
    EVENT_TYPE_131588(131588,"区域入侵"),
    EVENT_TYPE_131585(131585	,"越界侦测"),
    EVENT_TYPE_131586(131586	,"进入区域"),
    EVENT_TYPE_131587(131587	,"离开区域"),
    EVENT_TYPE_131590(131590	,"徘徊侦测"),
    EVENT_TYPE_131593(131593	,"人员聚集"),
    EVENT_TYPE_131592(131592	,"快速移动"),
    EVENT_TYPE_131591(131591	,"停车侦测"),
    EVENT_TYPE_131594(131594, "物品遗留"),
    EVENT_TYPE_131595(131595, "物品拿取"),
    EVENT_TYPE_131664(131664, "人数异常"),
    EVENT_TYPE_131665(131665, "间距异常"),
    EVENT_TYPE_131596(131596, "剧烈运动"),
    EVENT_TYPE_131603(131603, "岗位值守"),
    EVENT_TYPE_131605(131605, "倒地"),
    EVENT_TYPE_131597(131597, "攀高"),
    EVENT_TYPE_131610(131610, "重点目标起"),
    EVENT_TYPE_131666(131666, "人员站立"),
    EVENT_TYPE_131609(131609, "防风场滞留"),
    EVENT_TYPE_131598(131598, "起身"),
    EVENT_TYPE_131599(131599, "人靠近ATM"),
    EVENT_TYPE_131600(131600, "操作超时"),
    EVENT_TYPE_131601(131601, "贴纸条"),
    EVENT_TYPE_131602(131602, "安装读卡器"),
    EVENT_TYPE_131604(131604, "尾随"),
    EVENT_TYPE_131606(131606, "声强突变"),
    EVENT_TYPE_131607(131607, "折线攀高"),
    EVENT_TYPE_131611(131611, "折线警戒面"),
    EVENT_TYPE_192518(192518, "温差报警"),
    EVENT_TYPE_192516(192516, "船只检测"),
    EVENT_TYPE_192515(192515, "火点检测"),
    EVENT_TYPE_192514(192514, "烟火检测"),
    EVENT_TYPE_192513(192513, "烟雾检测"),
    EVENT_TYPE_889196545(889196545, "监控点"),
    ;
    private Integer code;
    private String value;


    HkEventTypeEnum(Integer code, String value) {
        this.code = code;
        this.value = value;

    }
    public Integer getCode() {
        return code;
    }
    public String getValue() {
        return value;
    }

    public static String getValue(Integer code) {
        for (HkEventTypeEnum eventTypeEnum : HkEventTypeEnum.values()) {
            if (eventTypeEnum.getCode().equals(code)) {
                return eventTypeEnum.getValue();
            }
        }
        return null;
    }
     public static Integer getCode(String value) {
        for (HkEventTypeEnum eventTypeEnum : HkEventTypeEnum.values()) {
            if (eventTypeEnum.getValue().equals(value)) {
                return eventTypeEnum.getCode();
            }
        }
        return null;
    }
}
