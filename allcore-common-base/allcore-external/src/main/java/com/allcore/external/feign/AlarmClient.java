package com.allcore.external.feign;

import com.allcore.core.tenant.annotation.NonDS;
import com.allcore.core.tool.api.R;
import com.allcore.external.entity.AlarmInfo;
import com.allcore.external.service.IAlarmInfoService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 告警feign接口
 *
 * <AUTHOR>
 * @Date 2022/09/07 14:21
 **/
@NonDS
@RestController
@AllArgsConstructor
public class AlarmClient implements IAlarmClient {

	private final IAlarmInfoService alarmInfoService;
	@Override
	public R saveBatch(List<AlarmInfo> alarmInfoList) {
		return R.data(alarmInfoService.saveBatch(alarmInfoList));
	}

	@Override
	public R updateBatch(List<AlarmInfo> alarmInfoList) {
		return R.data(alarmInfoService.updateBatchById(alarmInfoList));
	}

	@Override
	public R<AlarmInfo> getDetail(String id) {
		return R.data(alarmInfoService.getById(id));
	}

	@Override
	public R<AlarmInfo> getInfoByTaggingId(String inspectionPictureTaggingId) {
		return R.data(alarmInfoService.getAlarmInfoByTaggingId(inspectionPictureTaggingId));
	}
}
