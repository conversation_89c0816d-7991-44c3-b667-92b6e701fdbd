package com.allcore.external.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/28 17:17
 **/
@NoArgsConstructor
@Data
public class TaskInfo {

    @JsonProperty("taskId")
    private Integer taskId;
    @JsonProperty("taskName")
    private String taskName;
    @JsonProperty("taskCode")
    private Object taskCode;
    @JsonProperty("taskType")
    private Integer taskType;
    @JsonProperty("startTime")
    private String startTime;
    @JsonProperty("endTime")
    private Object endTime;
    @JsonProperty("effectStartTime")
    private String effectStartTime;
    @JsonProperty("effectEndTime")
    private String effectEndTime;
    @JsonProperty("repeatRate")
    private Object repeatRate;
    @JsonProperty("repeatRateUnit")
    private Object repeatRateUnit;
    @JsonProperty("executeTime")
    private Object executeTime;
    @JsonProperty("cycleType")
    private Object cycleType;
    @JsonProperty("validType")
    private Object validType;
    @JsonProperty("validStatus")
    private Integer validStatus;
    @JsonProperty("validStatusList")
    private Object validStatusList;
    @JsonProperty("validStatusName")
    private String validStatusName;
    @JsonProperty("taskTypeName")
    private String taskTypeName;
    @JsonProperty("agingTime")
    private String agingTime;
    @JsonProperty("overTimeLength")
    private String overTimeLength;
    @JsonProperty("overTimeActionType")
    private Integer overTimeActionType;
    @JsonProperty("stopPoint")
    private String stopPoint;
    @JsonProperty("addType")
    private Integer addType;
    @JsonProperty("tempId")
    private Integer tempId;
    @JsonProperty("trainingSampleGather")
    private Object trainingSampleGather;
    @JsonProperty("trainingSampleCount")
    private Object trainingSampleCount;
    @JsonProperty("trainingSampleAngle")
    private Object trainingSampleAngle;
    @JsonProperty("viewType")
    private Integer viewType;
    @JsonProperty("viewTypeName")
    private String viewTypeName;
    @JsonProperty("overType")
    private Integer overType;
    @JsonProperty("overStopPoint")
    private String overStopPoint;
    @JsonProperty("overTypeName")
    private String overTypeName;
    @JsonProperty("pointIdList")
    private Object pointIdList;
    @JsonProperty("deviceIdList")
    private List<Integer> deviceIdList;
    @JsonProperty("mapId")
    private String mapId;
    @JsonProperty("dimension")
    private Integer dimension;
    @JsonProperty("priority")
    private Object priority;
    @JsonProperty("priorityList")
    private Object priorityList;
    @JsonProperty("dimensionName")
    private String dimensionName;
    @JsonProperty("mapName")
    private String mapName;
    @JsonProperty("patrolPathType")
    private Integer patrolPathType;
    @JsonProperty("createTime")
    private String createTime;
    @JsonProperty("updateTime")
    private String updateTime;
    @JsonProperty("isDelete")
    private Integer isDelete;
    @JsonProperty("isSaveToTemp")
    private Object isSaveToTemp;
    @JsonProperty("taskDeviceRealList")
    private Object taskDeviceRealList;
    @JsonProperty("taskPointRealList")
    private Object taskPointRealList;
    @JsonProperty("taskTemplateName")
    private Object taskTemplateName;
    @JsonProperty("execId")
    private Object execId;
    @JsonProperty("isRun")
    private Integer isRun;
    @JsonProperty("taskIdLnzn")
    private Object taskIdLnzn;
    @JsonProperty("imageUploadMode")
    private Object imageUploadMode;
    @JsonProperty("taskAddType")
    private Object taskAddType;
    @JsonProperty("isAddExec")
    private Boolean isAddExec;
}
