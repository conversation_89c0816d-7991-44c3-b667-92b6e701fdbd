package com.allcore;

import com.allcore.common.constant.LauncherConstant;
import com.allcore.core.cloud.client.AllcoreCloudApplication;
import com.allcore.core.launch.AllcoreApplication;
import com.allcore.external.config.MachineProperties;
import com.allcore.external.config.TetraProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 系统模块启动器
 * <AUTHOR>
 */
@EnableAsync
@AllcoreCloudApplication
@EnableScheduling
@EnableConfigurationProperties({ MachineProperties.class, TetraProperties.class})
public class ExternalApplication {

	public static void main(String[] args) {
		AllcoreApplication.run(LauncherConstant.MACHINE_NEST_SERVER_NAME, ExternalApplication.class, args);
	}

}

