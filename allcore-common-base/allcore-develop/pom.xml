<?xml version="1.0"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <parent>
        <groupId>com.allcore</groupId>
        <artifactId>allcore-common-base</artifactId>
        <version>PRODUCT.1.0.0.RELEASE</version>
    </parent>

    <modelVersion>4.0.0</modelVersion>

    <artifactId>allcore-develop</artifactId>
    <name>${project.artifactId}</name>
    <version>${allcore.project.version}</version>
    <packaging>jar</packaging>

    <dependencies>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-core-boot</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-develop</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-starter-swagger</artifactId>
        </dependency>
        <dependency>
            <groupId>com.allcore</groupId>
            <artifactId>allcore-common</artifactId>
            <version>${allcore.project.version}</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.spotify</groupId>
                <artifactId>dockerfile-maven-plugin</artifactId>
                <configuration>
                    <username>${docker.username}</username>
                    <password>${docker.password}</password>
                    <repository>${docker.registry.url}/${docker.namespace}/${project.artifactId}</repository>
                    <tag>${docker.version}</tag>
                    <useMavenSettingsForAuth>true</useMavenSettingsForAuth>
                    <buildArgs>
                        <JAR_FILE>target/${project.build.finalName}.jar</JAR_FILE>
                    </buildArgs>
                    <skip>false</skip>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
            </plugin>
            <!--功能强大的查插件 推荐使用-->
            <!--打包推送命令 mvn clean package docker:push-->
            <plugin>
                <groupId>io.fabric8</groupId>
                <artifactId>docker-maven-plugin</artifactId>
                <version>0.42.0</version>
                <configuration>
                    <!--配置远程docker守护进程url-->
                    <dockerHost>${docker.host}</dockerHost>
                    <!--认证配置,用于私有registry认证-->
                    <authConfig>
                        <username>${docker.username}</username>
                        <password>${docker.password}</password>
                    </authConfig>
                    <!-- harbor镜像仓库地址-->
                    <pushRegistry>http://${docker.registry.url}</pushRegistry>
                    <images>
                        <image>
                            <!--推送到私有镜像仓库，镜像名需要添加仓库地址-->
                            <name>${docker.registry.url}/${docker.namespace}/${project.artifactId}:${docker.version}</name>
                            <!--定义镜像构建行为-->

                            <build>
                                <dockerFileDir>${project.basedir}</dockerFileDir>
                            </build>
                        </image>
                    </images>
                </configuration>
                <executions>
                    <execution>
                        <id>docker-exec</id>
                        <!-- 绑定mvn install阶段，当执行mvn install时 就会执行docker build 和docker push-->
                        <phase>install</phase>
                        <goals>
                            <goal>build</goal>
                            <goal>push</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
