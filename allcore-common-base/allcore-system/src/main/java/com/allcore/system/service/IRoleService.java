package com.allcore.system.service;

import com.allcore.system.entity.Role;
import com.allcore.system.vo.RoleVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IRoleService extends IService<Role> {

	/**
	 * （自定义分页）
	 * <AUTHOR>
	 * @date 2023/03/22 18:05
	 * @param page
	 * @param role
	 * @return com.baomidou.mybatisplus.core.metadata.IPage<com.allcore.system.vo.RoleVO>
	 */
	IPage<RoleVO> selectRolePage(IPage<RoleVO> page, RoleVO role);

	/**
	 * （树形结构）
	 * <AUTHOR>
	 * @date 2022/09/20 17:18
	 * @param tenantId
	 * @param status
	 * @return java.util.List<com.allcore.system.vo.RoleVO>
	 */
	List<RoleVO> tree(String tenantId,String status);

	/**
	 * （权限配置）
	 * <AUTHOR>
	 * @date 2023/03/22 18:05
	 * @param roleIds
	 * @param menuIds
	 * @param dataScopeIds
	 * @param apiScopeIds
	 * @return boolean
	 */
	boolean grant(@NotEmpty List<String> roleIds, List<String> menuIds, List<String> dataScopeIds, List<String> apiScopeIds);
	boolean grantNew(@NotEmpty List<String> roleIds, List<String> menuIds, List<String> dataScopeIds, List<String> apiScopeIds, List<String> topMenuIds);

	/**
	 * （根据名称获取角色ids）
	 * <AUTHOR>
	 * @date 2023/03/22 18:05
	 * @param tenantId
	 * @param roleNames
	 * @return java.lang.String
	 */
	String getRoleIds(String tenantId, String roleNames);

	/**
	 * （根据ids获取角色名）
	 * <AUTHOR>
	 * @date 2023/03/22 18:05
	 * @param roleIds
	 * @return java.util.List<java.lang.String>
	 */
	List<String> getRoleNames(String roleIds);

	/**
	 * （根据ids获取角色首页配置）
	 * <AUTHOR>
	 * @date 2023/03/22 18:06
	 * @param roleIds
	 * @return java.util.List<java.lang.String>
	 */
	List<String> getRoleHomeConfigs(String roleIds);

	/**
	 * （获取角色别名）
	 * <AUTHOR>
	 * @date 2023/03/22 18:06
	 * @param roleIds
	 * @return java.util.List<java.lang.String>
	 */
	List<String> getRoleAliases(String roleIds);

	/**
	 * （新增或修改）
	 * <AUTHOR>
	 * @date 2023/03/22 18:07
	 * @param role
	 * @return boolean
	 */
	boolean submit(Role role);

	/**
	 * （角色信息查询:父id+名称模糊）
	 * <AUTHOR>
	 * @date 2023/03/22 18:07
	 * @param roleName
	 * @param parentId
	 * @return java.util.List<com.allcore.system.vo.RoleVO>
	 */
	List<RoleVO> search(String roleName, String parentId);

	/**
	 * （根据ids删除角色）
	 * <AUTHOR>
	 * @date 2023/03/22 18:20
	 * @param ids
	 * @return boolean
	 */
	boolean removeRole(String ids);

	/**
	 * （角色启用禁用）
	 * <AUTHOR>
	 * @date 2022/09/08 13:33
	 * @param id
	 * @param status
	 * @return java.lang.Boolean
	 */
	Boolean enable(String id, String status);

	/**
	 * （根据角色别名获取角色list）
	 * <AUTHOR>
	 * @date 2023/03/22 18:20
	 * @param tenantId
	 * @param roleAlias
	 * @return java.util.List<com.allcore.system.entity.Role>
	 */
	List<Role> getRoleByRoleAlias(String tenantId, String roleAlias);

	/**
	 * （根据ids获取角色list）
	 * <AUTHOR>
	 * @date 2023/03/22 18:21
	 * @param ids
	 * @return java.util.List<com.allcore.system.entity.Role>
	 */
	List<Role> getRoleList(String ids);
}
