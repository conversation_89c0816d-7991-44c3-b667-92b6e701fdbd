<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.system.mapper.RoleMenuMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleMenuResultMap" type="com.allcore.system.entity.RoleMenu">
        <id column="id" property="id"/>
        <result column="menu_id" property="menuId"/>
        <result column="role_id" property="roleId"/>
        <result column="ref_type" property="refType"/>
    </resultMap>

    <select id="selectRoleMenuPage" resultMap="roleMenuResultMap">
        select id,
               menu_id,
               role_id,
               ref_type from sys_role_menu where is_deleted = 0 and ref_type = #{refType}
    </select>

</mapper>
