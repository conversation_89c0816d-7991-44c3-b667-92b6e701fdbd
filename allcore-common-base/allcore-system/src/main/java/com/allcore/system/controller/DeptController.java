package com.allcore.system.controller;


import static com.allcore.core.cache.constant.CacheConstant.SYS_CACHE;

import java.util.List;
import java.util.Map;

import javax.validation.Valid;

import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.cache.utils.CacheUtil;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.mp.support.Query;
import com.allcore.core.secure.AllcoreUser;
import com.allcore.core.tenant.annotation.NonDS;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.constant.AllcoreConstant;
import com.allcore.core.tool.support.Kv;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.dict.cache.DictCache;
import com.allcore.dict.enums.DictEnum;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.allcore.system.service.IDeptService;
import com.allcore.system.vo.DeptVO;
import com.allcore.system.wrapper.DeptWrapper;
import com.allcore.user.cache.UserCache;
import com.allcore.user.entity.User;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;

import cn.hutool.core.collection.CollectionUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import springfox.documentation.annotations.ApiIgnore;

/**
 * （机构管理控制器）
 * <AUTHOR>
 * @date 2023/03/21 19:44
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("/dept")
@Api(value = "部门", tags = "部门")
public class DeptController extends AllcoreController {

	private final IDeptService deptService;

	/**
	 * （根据id查详情）
	 * <AUTHOR>
	 * @date 2023/03/21 19:10
	 * @param id
	 * @return com.allcore.core.tool.api.R<com.allcore.system.vo.DeptVO>
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "根据id查详情", notes = "传入id")
	public R<DeptVO> detail(@RequestParam String id) {
		Dept dept = new Dept();
		dept.setId(id);
		Dept detail = deptService.getOne(Condition.getQueryWrapper(dept));
		return R.data(DeptWrapper.build().entityVO(detail));
	}

	/**
	 * （条件构造器列表）
	 * <AUTHOR>
	 * @date 2023/03/21 19:18
	 * @param dept
	 * @param allcoreUser
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "当前状态", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "条件构造器列表", notes = "对象的字段自由组合")
	public R<List<DeptVO>> list(@ApiIgnore @RequestParam Map<String, Object> dept, AllcoreUser allcoreUser) {
		QueryWrapper<Dept> queryWrapper = Condition.getQueryWrapper(dept, Dept.class);
		List<Dept> list = deptService.list((!allcoreUser.getTenantId().equals(AllcoreConstant.ADMIN_TENANT_ID)) ? queryWrapper.lambda().eq(Dept::getTenantId, allcoreUser.getTenantId()) : queryWrapper);
		return R.data(DeptWrapper.build().listNodeVO(list));
	}

	/**
	 * 获取本部门与子部门列表
	 * <AUTHOR>
	 * @date 2023/03/21 19:21
	 * @param dept
	 * @param parentId
	 * @param allcoreUser
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/lazy-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "deptName", value = "部门名称", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "status", value = "当前状态", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "fullName", value = "部门全称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "名称+全称+状态+父级机构id")
	public R<List<DeptVO>> lazyList(@ApiIgnore @RequestParam Map<String, Object> dept, String parentId, AllcoreUser allcoreUser) {
		List<DeptVO> list = deptService.lazyList(allcoreUser.getTenantId(), parentId, dept);
		return R.data(DeptWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * （获取部门树形结构）
	 * <AUTHOR>
	 * @date 2023/03/21 19:23
	 * @param tenantId
	 * @param allcoreUser
	 * @param status
	 * @param disabledNoAuth
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "树形结构", notes = "租户id+状态+是否禁用无权限")
	public R<List<DeptVO>> tree(String tenantId, AllcoreUser allcoreUser, String status,String disabledNoAuth) {
		List<DeptVO> tree = deptService.tree(Func.toStrWithEmpty(tenantId, allcoreUser.getTenantId()),  status,disabledNoAuth);
		return R.data(tree);
	}

	/**
	 * （获取树形节点:父节只显示每个的直属上级）
	 * <AUTHOR>
	 * @date 2023/03/21 19:25
	 * @param tenantId
	 * @param allcoreUser
	 * @param status
	 * @param disabledNoAuth
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/treeWithParentOneLine")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "获取树形节点:父节只显示每个的直属上级", notes = "租户id+状态+是否禁用无权限")
	public R<List<DeptVO>> treeWithParentOneLine(String tenantId, AllcoreUser allcoreUser, String status,String disabledNoAuth) {
		List<DeptVO> tree = deptService.treeWithParentOneLine(Func.toStrWithEmpty(tenantId, allcoreUser.getTenantId()),  status,disabledNoAuth);
		return R.data(tree);
	}

	@GetMapping("/treeByDeptId")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "获取树形节点:父节只显示每个的直属上级", notes = "租户id+状态+是否禁用无权限")
	public R<List<DeptVO>> treeByDeptId(String tenantId, AllcoreUser allcoreUser, String status,String disabledNoAuth,String deptId) {
		List<DeptVO> tree = deptService.treeByDeptId(Func.toStrWithEmpty(tenantId, allcoreUser.getTenantId()),  status,disabledNoAuth,deptId);
		return R.data(tree);
	}

	/**
	 * （场站下拉专用）
	 * <AUTHOR>
	 * @date 2023/12/01 16:01
	 * @param tenantId
	 * @param allcoreUser
	 * @param status
	 * @param disabledNoAuth
	 * @param deptId
	 * @param deptCategory
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/czTreeByDeptId")
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "获取树形节点:父节只显示每个的直属上级", notes = "租户id+状态+是否禁用无权限")
	public R<List<DeptVO>> czTreeByDeptId(String tenantId, AllcoreUser allcoreUser, String status,String disabledNoAuth,String deptId,Integer notDeptCategory) {
		List<DeptVO> tree = deptService.czTreeByDeptId(Func.toStrWithEmpty(tenantId, allcoreUser.getTenantId()),  status,disabledNoAuth,deptId,notDeptCategory);
		return R.data(tree);
	}


	/**
	 * （获取树形节点:重庆定制只显示到重庆的第三层）
	 * <AUTHOR>
	 * @date 2023/03/21 19:27
	 * @param tenantId
	 * @param allcoreUser
	 * @param status
	 * @param disabledNoAuth
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/treeByProvincialLevel")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "获取树形节点:重庆定制只显示到重庆的第三层", notes = "租户id+状态+是否禁用无权限")
	public R<List<DeptVO>> treeByProvincialLevel(String tenantId, AllcoreUser allcoreUser, String status,String disabledNoAuth) {
		List<DeptVO> tree = deptService.treeByProvincialLevel(Func.toStrWithEmpty(tenantId, allcoreUser.getTenantId()),  status,disabledNoAuth);
		return R.data(tree);
	}

	/**
	 * （懒加载树形结构）
	 * <AUTHOR>
	 * @date 2023/03/21 19:28
	 * @param tenantId
	 * @param parentId
	 * @param status
	 * @param allcoreUser
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/lazy-tree")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "懒加载树形结构", notes = "租户id+父id+状态")
	public R<List<DeptVO>> lazyTree(String tenantId, String parentId, String status, AllcoreUser allcoreUser) {
		List<DeptVO> tree = deptService.lazyTree(Func.toStrWithEmpty(tenantId, allcoreUser.getTenantId()), parentId,status);
		return R.data(tree);
	}

	/**
	 * （新增或修改）
	 * <AUTHOR>
	 * @date 2023/03/21 19:29
	 * @param dept
	 * @return com.allcore.core.tool.api.R
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "新增或修改", notes = "传入dept对象")
	public R submit(@Valid @RequestBody Dept dept) {
		if (deptService.submit(dept)) {
			CacheUtil.clear(SYS_CACHE);
			String parentName = "";
			if (Func.equals(dept.getParentId(), AllcoreConstant.TOP_PARENT_ID)) {
				parentName = AllcoreConstant.TOP_PARENT_NAME;
			} else {
				Dept parent = SysCache.getDept(dept.getParentId());
				parentName = parent.getDeptName();
			}
			// 返回懒加载树更新节点所需字段
			Kv kv = Kv.create().set("id", String.valueOf(dept.getId())).set("tenantId", dept.getTenantId())
				.set("natureIdZh", DictCache.getValue(DictEnum.DEPT_NATURE, dept.getNatureId()))
				.set("parentName", parentName)
				.set("deptCode", dept.getDeptCode())
				.set("statusZh", DictCache.getValue(DictEnum.NOW_STATUS, dept.getStatus()))
				.set("deptCategoryName", DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory()));
			return R.data(kv);
		}
		return R.fail("操作失败");
	}

	/**
	 * （删除）
	 * <AUTHOR>
	 * @date 2023/03/21 19:29
	 * @param ids
	 * @return com.allcore.core.tool.api.R
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(SYS_CACHE);
		return R.status(deptService.removeDept(ids));
	}

	/**
	 * （下拉数据源）
	 * <AUTHOR>
	 * @date 2023/03/21 19:30
	 * @param userId
	 * @param deptId
	 * @param status
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.entity.Dept>>
	 */
	@GetMapping("/select")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "下拉数据源", notes = "用户id+机构id+状态")
	public R<List<Dept>> select(String userId, String deptId, String status) {
		if (Func.isNotEmpty(userId)) {
			User user = UserCache.getUser(userId);
			deptId = user.getDeptId();
		}
		List<Dept> list = deptService.list(Wrappers.<Dept>lambdaQuery()
			.in(Dept::getId, Func.toStrList(deptId))
			.eq(StringUtil.isNotBlank(status),Dept::getStatus,status));
		return R.data(list);
	}

	/**
	 * 单位子节点
	 * @param userId
	 * @param deptId
	 * @param status
	 * @return
	 */
	@GetMapping("/node")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "单位子节点", notes = "用户id+机构id+状态")
	public R<List<Dept>> node(String userId, String deptId, String deptCode, String status) {
		if (Func.isNotEmpty(userId)) {
			User user = UserCache.getUser(userId);
			if(user != null) {
				deptId = user.getDeptId();
			}
		}
		List<String> deptIds = Func.toStrList(deptId);
		List<Dept> list = deptService.list(Wrappers.<Dept>lambdaQuery()
			.in(CollectionUtil.isNotEmpty(deptIds), Dept::getParentId, deptIds)
			.likeRight(StringUtil.isNotBlank(deptCode), Dept::getDeptCode, deptCode)
			.eq(StringUtil.isNotBlank(status),Dept::getStatus,status));
		return R.data(list);
	}
	
	/**
	 * （根据父id查询下级机构分页列表）
	 * <AUTHOR>
	 * @date 2023/03/21 19:37
	 * @param query
	 * @param parentId
	 * @param allcoreUser
	 * @return com.allcore.core.tool.api.R<com.baomidou.mybatisplus.core.metadata.IPage < com.allcore.system.vo.DeptVO>>
	 */
	@GetMapping("/page")
	@ApiOperationSupport(order = 9)
	@ApiOperation(value = "根据父id查询下级机构分页列表", notes = "用户id+机构id+状态")
	public R<IPage<DeptVO>> page(Query query, String parentId, AllcoreUser allcoreUser) {
		IPage<Dept> pages = deptService.selectDeptPage(
			Condition.getPage(query),
			parentId,
			(allcoreUser.getTenantId().equals(AllcoreConstant.ADMIN_TENANT_ID) ? StringPool.EMPTY : allcoreUser.getTenantId()));
		return R.data(DeptWrapper.build().pageVO(pages));
	}


	/**
	 * （激活与禁用
	 * 禁用:禁用本级和下级
	 * 启用的时候 父级得全部启用 不操作下级）
	 * <AUTHOR>
	 * @date 2023/03/21 19:39
	 * @param id
	 * @param status
	 * @return com.allcore.core.tool.api.R
	 */
	@GetMapping("/enable")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "激活与禁用", notes = "传入id+状态")
	public R enable(@ApiParam(value = "主键", required = true) @RequestParam String id,String status) {
		return R.status(deptService.enable(id,status));
	}


}
