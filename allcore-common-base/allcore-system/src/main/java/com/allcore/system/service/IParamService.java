package com.allcore.system.service;


import com.allcore.core.mp.base.BaseService;
import com.allcore.system.entity.LoginManage;
import com.allcore.system.entity.Param;

/**
 * 服务类
 *
 * <AUTHOR>
 */
public interface IParamService extends BaseService<Param> {

	/**
	 * 获取参数值
	 *
	 * @param paramKey 参数key
	 * @return String
	 */
	String getValue(String paramKey);

	/**
	 * （登陆管理回显）
	 * <AUTHOR>
	 * @date 2022/09/02 09:06
	 * @return com.allcore.system.entity.LoginManage
	 */
    LoginManage loginManage();

	/**
	 * （登陆管理保存）
	 * <AUTHOR>
	 * @date 2022/09/02 10:27
	 * @param loginManage
	 * @return boolean
	 */
	boolean updateLoginManage(LoginManage loginManage);

	/**
	 * 获取param对象
	 * @param key
	 * @return
	 */
	Param getParam(String key);
}
