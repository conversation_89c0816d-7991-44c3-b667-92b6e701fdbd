package com.allcore.system.service.impl;


import cn.hutool.core.collection.CollectionUtil;
import com.allcore.common.config.IndexConfig;
import com.allcore.common.utils.CommonUtil;
import com.allcore.core.datascope.model.DataScopeModel;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.AllcoreUser;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.constant.AllcoreConstant;
import com.allcore.core.tool.node.ForestNodeMerger;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.core.tool.utils.*;
import com.allcore.system.cache.DataScopeCache;
import com.allcore.system.cache.SysCache;
import com.allcore.system.dto.DeptParamDTO;
import com.allcore.system.entity.Dept;
import com.allcore.system.entity.Region;
import com.allcore.system.mapper.DeptMapper;
import com.allcore.system.service.IDeptService;
import com.allcore.system.service.IRegionService;
import com.allcore.system.vo.DeptEx;
import com.allcore.system.vo.DeptVO;
import com.allcore.system.vo.DeptWithParentVO;
import com.allcore.system.wrapper.DeptWrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
@RefreshScope
public class DeptServiceImpl extends ServiceImpl<DeptMapper, Dept> implements IDeptService {

	@Value("${dept.firstFilter}")
	private String firstDept;

	@Value("${dept.secondFilter}")
	private String secondDept;

	@Value("${dept.thirdFilter}")
	private String thirdDept;

	//项目过程管控单位关闭懒加载开关 true:关闭
	@Value("${dept.projectControlSwitch:false}")
	private boolean projectControlSwitch;
	private static final String TENANT_ID = "tenantId";
	private static final String PARENT_ID = "parentId";
	private static final String LAST_PARENT_CODE = "9999";
	private static final Integer DEPT_CATEGORY_THREE= 3;
	private static final Integer DEPT_CATEGORY_TWO= 2;
	/**
	 * 行政区域service
	 */
	@Autowired
	private IRegionService iRegionService;

	@Resource
	private IndexConfig indexConfig;

	@Override
	public List<DeptVO> lazyList(String tenantId, String parentId, Map<String, Object> param) {
		String deptCode = "";
		if(projectControlSwitch){
			AllcoreUser user = AuthUtil.getUser();
			//根据mapperId与用户角色查询配置生效的数据权限
			//如果存在查询全部单位根据配置的数据权限查询，不存在默认查询本机构及子级机构
			DataScopeModel dataScope = DataScopeCache.getDataScopeByMapper("com.allcore.system.mapper.DeptMapper.lazyList",user.getRoleId());
			//判断如果选择租户按租户查询
			String paramTenantId = Func.toStr(param.get(TENANT_ID));
			if (Func.isNotEmpty(paramTenantId)) {
				tenantId = paramTenantId;
			}

			if (Func.isNull(dataScope)){
				String[] deptIdArray = Func.splitTrim(AuthUtil.getDeptId(), ",");
				List<DeptVO> resultDeptList = new ArrayList<>();
				for (String deptId : deptIdArray) {
					Dept dept = SysCache.getDept(deptId);
					List<DeptVO> tempDeptList = baseMapper.lazyListPart(tenantId, deptId, dept.getAncestors() + StringPool.COMMA + deptId, param);
					resultDeptList.addAll(tempDeptList);
				}
				return resultDeptList.stream().distinct().collect(Collectors.toList());
			} else {
				return baseMapper.lazyList(tenantId, parentId, param,"");
			}
		}else{
			// 设置租户ID
			if (AuthUtil.isAdministrator()) {
				tenantId = StringPool.EMPTY;
			}
			String paramTenantId = Func.toStr(param.get(TENANT_ID));
			if (Func.isNotEmpty(paramTenantId) && AuthUtil.isAdministrator()) {
				tenantId = paramTenantId;
			}
			// 判断点击搜索但是没有查询条件的情况
			if (Func.isEmpty(param.get(PARENT_ID)) && param.size() == 1) {
				parentId = StringPool.ZERO;
			}
			// 判断数据权限控制,非超管角色只可看到本级及以下数据
			if (StringUtil.isBlank(parentId) && !AuthUtil.isAdministrator()) {
				String deptId = Func.firstStr(AuthUtil.getDeptId());
				Dept dept = SysCache.getDept(deptId);
//				if (!StringPool.ZERO.equals(dept.getParentId())) {
//					parentId = dept.getParentId();
//				}
				parentId = null;
				deptCode = dept.getDeptCode();
			}
			// 判断点击搜索带有查询条件的情况
			if (Func.isEmpty(param.get(PARENT_ID)) && param.size() > 1 && StringUtil.isBlank(parentId)) {
				parentId = null;
			}
			return baseMapper.lazyList(tenantId, parentId, param,deptCode);
		}
	}


	@Override
	public List<DeptVO> tree(String tenantId, String status,String disabledNoAuth) {
		String deptCode = "";
		// 非超管角色只可看到本级及以下数据
		if (!AuthUtil.isAdministrator()) {
			String deptId = Func.firstStr(AuthUtil.getDeptId());
			Dept dept = SysCache.getDept(deptId);
			if (!StringPool.ZERO.equals(dept.getParentId())) {
				deptCode = dept.getDeptCode();
			}
		}
		return ForestNodeMerger.merge(baseMapper.tree(tenantId, status,deptCode,disabledNoAuth));
	}

	@Override
	public List<DeptVO> treeByProvincialLevel(String tenantId, String status,String disabledNoAuth) {
		String deptCode = "";
		// 非超管角色只可看到本级及以下数据
		if (!AuthUtil.isAdministrator()) {
			String deptId = Func.firstStr(AuthUtil.getDeptId());
			Dept dept = SysCache.getDept(deptId);
			if (!StringPool.ZERO.equals(dept.getParentId())) {
				deptCode = dept.getDeptCode();
			}
		}
		return ForestNodeMerger.merge(baseMapper.treeByProvincialLevel(tenantId, status,deptCode,disabledNoAuth));
	}

	@Override
	public List<DeptVO> treeWithParentOneLine(String tenantId, String status,String disabledNoAuth) {
		String deptCode = "";
		// 非超管角色只可看到本级及以下数据
		if (!AuthUtil.isAdministrator()) {
			String deptId = Func.firstStr(AuthUtil.getDeptId());
			Dept dept = SysCache.getDept(deptId);
			if (!StringPool.ZERO.equals(dept.getParentId())) {
				deptCode = dept.getDeptCode();
			}
		}
		return ForestNodeMerger.merge(baseMapper.treeWithParentOneLine(tenantId, status,deptCode,disabledNoAuth));
	}

	@Override
	public List<DeptVO> treeByDeptId(String tenantId, String status,String disabledNoAuth,String deptId) {
		String deptCode = "";
		// 非超管角色只可看到本级及以下数据
		if (!AuthUtil.isAdministrator()) {
			Dept dept = SysCache.getDept(deptId);
			if (!StringPool.ZERO.equals(dept.getParentId())) {
				deptCode = dept.getDeptCode();
			}
		}
		return ForestNodeMerger.merge(baseMapper.treeByDeptId(tenantId, status,deptCode,disabledNoAuth));
	}

	@Override
	public List<DeptVO> czTreeByDeptId(String tenantId, String status,String disabledNoAuth,String deptId,Integer notDeptCategory) {
		String deptCode = "";
		// 非超管角色只可看到本级及以下数据
		if (!AuthUtil.isAdministrator()) {
			Dept dept = SysCache.getDept(deptId);
			if (!StringPool.ZERO.equals(dept.getParentId())) {
				deptCode = dept.getDeptCode();
			}
		}
		return ForestNodeMerger.merge(baseMapper.czTreeByDeptId(tenantId, status,deptCode,disabledNoAuth,notDeptCategory));
	}

	@Override
	public List<TreeNode> treeWithParentOneLineFeign(String tenantId, String status, String disabledNoAuth, Integer category) {
		String deptCode = "";
		// 非超管角色只可看到本级及以下数据
		if (!AuthUtil.isAdministrator()) {
			String deptId = Func.firstStr(AuthUtil.getDeptId());
			Dept dept = SysCache.getDept(deptId);
			if (!StringPool.ZERO.equals(dept.getParentId())) {
				deptCode = dept.getDeptCode();
			}
		}
		List<TreeNode> treeNodeList = baseMapper.treeWithParentOneLineFeign(tenantId, status, deptCode, disabledNoAuth, category);
		Map<String, TreeNode> deptNodeMap =  treeNodeList.stream().collect(Collectors.toMap(TreeNode::getParentId, obj -> obj));
		List<TreeNode> resultList = this.sortDeptNodeList(deptNodeMap);
		return resultList;
	}

	/**
	 * 按照父子级单位顺序排序
	 * @param deptNodeMap
	 */
	private List<TreeNode> sortDeptNodeList(Map<String, TreeNode> deptNodeMap){
		List<TreeNode> sortDeptList = new ArrayList<>();
		//顶层单位parent_id为0
		TreeNode dept = deptNodeMap.get("0");
		sortDeptList.add(dept);
		TreeNode tempDept = dept;
		int size = deptNodeMap.keySet().size();
		for (int i = 0; i < size; i++) {
			TreeNode dep = deptNodeMap.get(tempDept.getId());
			if (dep != null){
				sortDeptList.add(dep);
				tempDept = dep;
			}
		}
		return sortDeptList;
	}

	@Override
	public List<TreeNode> treeFeign(String tenantId, String status, String disabledNoAuth,Integer category) {
		String deptCode = "";
		// 非超管角色只可看到本级及以下数据
		if (!AuthUtil.isAdministrator()) {
			String deptId = Func.firstStr(AuthUtil.getDeptId());
			Dept dept = SysCache.getDept(deptId);
			if (!StringPool.ZERO.equals(dept.getParentId())) {
				deptCode = dept.getDeptCode();
			}
		}
		return baseMapper.treeFeign(tenantId, status,deptCode,disabledNoAuth,category);
	}

	@Override
	public List<DeptVO> lazyTree(String tenantId, String parentId, String status) {
		if (AuthUtil.isAdministrator()) {
			tenantId = StringPool.EMPTY;
		}
		return ForestNodeMerger.merge(baseMapper.lazyTree(tenantId, parentId,status));
	}

	@Override
	public String getDeptIds(String tenantId, String deptNames) {
		List<Dept> deptList = baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId).in(Dept::getDeptName, Func.toStrList(deptNames)));
		if (deptList != null && deptList.size() > 0) {
			return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public List<Dept> getDeptInfos(String tenantId, String deptNames) {
		List<Dept> deptList = baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId).in(Dept::getDeptName, Func.toStrList(deptNames)));
		return deptList;
	}

	@Override
	public String getDeptIdsByFuzzy(String tenantId, String deptNames) {
		LambdaQueryWrapper<Dept> queryWrapper = Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId);
		queryWrapper.and(wrapper -> {
			List<String> names = Func.toStrList(deptNames);
			names.forEach(name -> wrapper.like(Dept::getDeptName, name).or());
		});
		List<Dept> deptList = baseMapper.selectList(queryWrapper);
		if (deptList != null && deptList.size() > 0) {
			return deptList.stream().map(dept -> Func.toStr(dept.getId())).distinct().collect(Collectors.joining(","));
		}
		return null;
	}

	@Override
	public List<String> getDeptNames(String deptIds) {
		return baseMapper.getDeptNames(Func.toStrArray(deptIds));
	}

	@Override
	public Dept getByDeptCode(String deptCode) {
		return baseMapper.selectOne(Wrappers.<Dept>query().lambda().eq(Dept::getDeptCode, deptCode));
	}


	@Override
	public List<Dept> getDeptChild(String deptId) {
		return baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getTenantId,AuthUtil.getTenantId()).like(Dept::getAncestors, deptId));
	}

	@Override
	public List<Dept> getUnDisableDeptChild(String deptId,String tenantId) {
		return baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getTenantId,tenantId)
				.eq(Dept::getStatus,"yes")
				.like(Dept::getAncestors, deptId));
	}

	@Override
	public List<Dept> getDeptByCategory(Integer category) {
		return baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getTenantId,AuthUtil.getTenantId()).eq(Dept::getDeptCategory, category));
	}

	@Override
	public List<Dept> getDeptChildByParentId(String parentId) {
		return baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getParentId, parentId));
	}

	@Override
	public boolean removeDept(String ids) {
		Long cnt = baseMapper.selectCount(Wrappers.<Dept>query().lambda().in(Dept::getParentId, Func.toStrList(ids)));
		if (cnt > 0L) {
			throw new ServiceException("请先删除子节点!");
		}
		return removeByIds(Func.toStrList(ids));
	}

	@Override
	public boolean submit(Dept dept) {

		//虽然表结构有默认值，但这边再判断下 用于前端回显
		if(StringUtil.isBlank(dept.getStatus())){
			dept.setStatus(StringPool.YES);
		}
		String deptCode = "";
		if (Func.isEmpty(dept.getParentId())) {
			//获取最上级最新部门信息
			Dept lastDept = baseMapper.selectOwnerDept("0");
			//一个都没有
			if (ObjectUtil.isEmpty(lastDept)) {
				dept.setDeptCode("1000");
			} else if (LAST_PARENT_CODE.equals(lastDept.getDeptCode())) {
				throw new ServiceException("最大父节点已使用完毕!");
			} else {
				deptCode = String.valueOf(Long.valueOf(lastDept.getDeptCode())+1);
			}
			//最大级赋值
			dept.setDeptCode(deptCode);
			dept.setTenantId(AuthUtil.getTenantId());
			dept.setParentId(AllcoreConstant.TOP_PARENT_ID);
			dept.setAncestors(AllcoreConstant.TOP_PARENT_ID);
		}
		if (!StringPool.ZERO.equals(dept.getParentId())) {
			Dept parent = getById(dept.getParentId());
			if (dept.getParentId().equals(dept.getId())) {
				throw new ServiceException("父节点不可选择自身!");
			}
			String ancestors = "";
			dept.setTenantId(parent.getTenantId());
			ancestors = parent.getAncestors() + StringPool.COMMA + dept.getParentId();
			dept.setAncestors(ancestors);

			//如果是修改
			if(Func.isNotEmpty(dept.getId())){
				Dept hasOld = getById(dept.getId());
				//如果所属父级变更了 需要删除后再新增
				if(Func.isNotEmpty(hasOld)){
					if(!hasOld.getParentId().equals(dept.getParentId())){
						//根据deptCode修改id和is_deteled
						update(Wrappers.<Dept>update().lambda()
							.set(Dept::getId, dept.getId()+"-"+DateUtil.formatDateTime(DateUtil.now()))
							.set(Dept::getIsDeleted,1)
							.eq(Dept::getId, dept.getId())
						);
					}
				}
			}
			//修改逻辑上面已经做了
			//查出新增所属组的最大deptCode
			if(Func.isNotEmpty(dept.getId())){
				Dept hasOld = getById(dept.getId());
				//如果所属父级变更了 需要删除后再新增
				if(Func.isNotEmpty(hasOld)) {
					if (!hasOld.getParentId().equals(dept.getParentId())) {
						Dept lastDept = baseMapper.selectOwnerDept(ancestors);
						if (ObjectUtil.isEmpty(lastDept)) {
							deptCode = parent.getDeptCode()+"001";
						} else {
							//新增直接+1
							String preStr =lastDept.getDeptCode().substring(0,lastDept.getDeptCode().length()-3);
							String threeStr =lastDept.getDeptCode().substring(lastDept.getDeptCode().length()-3);
							DecimalFormat decimalFormat=new DecimalFormat("000");// 字符串数字的位数
							deptCode = preStr + decimalFormat.format(Integer.parseInt(threeStr)+1);
						}
					}else{
						deptCode = hasOld.getDeptCode();
					}
				}else{
					Dept lastDept = baseMapper.selectOwnerDept(ancestors);
					if (ObjectUtil.isEmpty(lastDept)) {
						deptCode = parent.getDeptCode()+"001";
					} else {
						//新增直接+1
						String preStr =lastDept.getDeptCode().substring(0,lastDept.getDeptCode().length()-3);
						String threeStr =lastDept.getDeptCode().substring(lastDept.getDeptCode().length()-3);
						DecimalFormat decimalFormat=new DecimalFormat("000");// 字符串数字的位数
						deptCode = preStr + decimalFormat.format(Integer.parseInt(threeStr)+1);
					}
				}
			}else{
				Dept lastDept = baseMapper.selectOwnerDept(ancestors);
				if (ObjectUtil.isEmpty(lastDept)) {
					deptCode = parent.getDeptCode()+"001";
				} else {
					//新增直接+1
					String preStr =lastDept.getDeptCode().substring(0,lastDept.getDeptCode().length()-3);
					String threeStr =lastDept.getDeptCode().substring(lastDept.getDeptCode().length()-3);
					DecimalFormat decimalFormat=new DecimalFormat("000");// 字符串数字的位数
					deptCode = preStr + decimalFormat.format(Integer.parseInt(threeStr)+1);
				}
			}

			dept.setDeptCode(deptCode);
		}


		//如果是修改 要把下级的所属区域都赋一样的值
		// 下级单位已维护经纬度 不可进行统一修改
//		if(Func.isNotEmpty(dept.getId())){
//			this.update(Wrappers.<Dept>update().lambda()
//				.set(Dept::getArea, dept.getArea())
//				.set(Dept::getLatitude, dept.getLatitude())
//				.set(Dept::getLongitude, dept.getLongitude())
//				.set(Dept::getElevation, dept.getElevation())
//				.likeRight(Dept::getDeptCode, dept.getDeptCode()));
//		}
		dept.setIsDeleted(AllcoreConstant.DB_NOT_DELETED);
		return saveOrUpdate(dept);
	}

//	public static void main(String[] args) {
//		String sss = "014003209003001032002013001";
//		String preStr =sss.substring(0,sss.length()-3);
//		System.out.println(preStr);
//		System.out.println(sss.substring(sss.length()-3));
//
////		DecimalFormat decimalFormat=new DecimalFormat(sss);// 字符串数字的位数
////		System.out.println(decimalFormat.format(sss+1));
//	}


	@Override
	public boolean submitByIsc(Dept dept) {
		Dept hasOld = getById(dept.getId());
		//如果所属父级变更了 需要删除后再新增
		if(Func.isNotEmpty(hasOld)){
			if(!hasOld.getParentId().equals(dept.getParentId()) && !dept.getId().equals(indexConfig.getUnitParentId())){
				//根据deptCode修改id和is_deteled
				update(Wrappers.<Dept>update().lambda()
					.set(Dept::getId, dept.getId()+"-"+DateUtil.formatDateTime(DateUtil.now()))
					.set(Dept::getIsDeleted,1)
					.eq(Dept::getId, dept.getId())
				);
			}
		}

		Dept parent = getById(dept.getParentId());
		if (dept.getParentId().equals(dept.getId())) {
			throw new ServiceException("父节点不可选择自身!");
		}
		String ancestors = "";
		//isc新增的直接有值 不需要获取父租户
		if(Func.isEmpty(parent)){
			dept.setParentId(CommonUtil.ISC_DEPT_ID);
			//找不到父机构 直接默认在isc组下面
			ancestors = StringPool.ZERO + StringPool.COMMA + CommonUtil.ISC_DEPT_ID;
		}else{
			ancestors = parent.getAncestors() + StringPool.COMMA + dept.getParentId();
		}
		dept.setAncestors(ancestors);

		dept.setIsDeleted(AllcoreConstant.DB_NOT_DELETED);
		return saveOrUpdate(dept);
	}

	@Override
	public List<DeptVO> search(String deptName, String parentId) {
		String tenantId = AuthUtil.getTenantId();
		LambdaQueryWrapper<Dept> queryWrapper = Wrappers.<Dept>query().lambda();
		if (Func.isNotEmpty(tenantId)) {
			queryWrapper.eq(Dept::getTenantId, tenantId);
		}
		if (Func.isNotEmpty(deptName)) {
			queryWrapper.like(Dept::getDeptName, deptName);
		}
		if (Func.isNotEmpty(parentId) && !StringPool.ZERO.equals(parentId)) {
			queryWrapper.eq(Dept::getParentId, parentId);
		}
		List<Dept> deptList = baseMapper.selectList(queryWrapper);
		return DeptWrapper.build().listNodeVO(deptList);
	}

	@Override
	public IPage<Dept> selectDeptPage(IPage<Dept> page, String deptId, String tenantId) {
		return page.setRecords(baseMapper.selectDeptPage(page,deptId, tenantId));
	}

	/**
	 * 根据子部门ID获取所有父部门ID
	 *
	 * @param tenantId String
	 * @param deptId   Long
	 * @return String
	 */
	@Override
	public String getDeptIdsByDeptId(String tenantId, String deptId) {
		List<Dept> deptList = baseMapper.getFatherDeptByDeptId(tenantId, deptId);
		return String.join(",", deptList.stream().map(Dept::getId).distinct().collect(Collectors.toList()));
	}

	/**
	 * 根据子部门code获取所有父部门ID
	 *
	 * @param tenantId String
	 * @param deptCode   String
	 * @return String
	 */
	@Override
	public String getDeptCodesByDeptCode(String tenantId, String deptCode) {
		List<Dept> deptList = baseMapper.getFatherDeptByDeptCode(tenantId, deptCode);
		return String.join(",", deptList.stream().map(Dept::getDeptCode).distinct().collect(Collectors.toList()));
	}

	/**
	 * 根据子部门ID获取所有父部门 包含子部门
	 *
	 * @param deptId   Long
	 * @return String
	 */
	@Override
	public List<Dept> getFatherAndOwnDeptByDeptId(String deptId) {
		return baseMapper.getFatherAndOwnDeptByDeptId(deptId);
	}

	/**
	 * 根据deptCode likeRight查询
	 *
	 * @param deptCode deptCode
	 * @return 单位集合
	 */
	@Override
	public List<Dept> getDeptLikeByDeptCode(String deptCode) {
		return baseMapper.selectList(Wrappers.<Dept>query().lambda().likeRight(Dept::getDeptCode, deptCode));
	}

	@Override
	public List<Dept> getChildAndOwnByDeptId(String deptId) {
		Dept dept = getById(deptId);
		return baseMapper.getChildAndOwnByDeptId(deptId,dept.getAncestors()+StringPool.COMMA+deptId);
	}

	/**
	 * 根据单位编码获取单位信息
	 *
	 * @param deptCode String
	 * @return DeptVO
	 */
	@Override
	public DeptVO getDeptInfoByDeptCode(String deptCode) {
		// 根据部门编码查询部门信息
		Dept dept = baseMapper.selectOne(Wrappers.<Dept>query().lambda().eq(Dept::getDeptCode, deptCode));
		// 复制属性内容
		DeptVO deptVO = BeanUtil.copy(dept, DeptVO.class);
		// 根据行政区域编码查询行政区域信息
		Region region = iRegionService.getOne(Wrappers.<Region>query().lambda().eq(Region::getCode, dept.getRegionCode()));
		if (null != region) {
			// 行政区域地址由 省 市 区 镇 村拼接而成
			deptVO.setRegionName(region.getProvinceName() + region.getCityName() + region.getDistrictName() + region.getTownName() + region.getVillageName());
		}
		return deptVO;
	}

	/**
	 * 获取场站信息和分公司信息
	 *
	 * @param deptCode 指定单位
	 * @return
	 */
	@Override
	public R<List<DeptEx>> getCzAndFgs(String deptCode) {
		// 获取当前单位信息
		Dept dept = this.getByDeptCode(deptCode);
		Integer deptCategory = dept.getDeptCategory();
		String parentId = dept.getParentId();
		if (deptCategory.equals(DEPT_CATEGORY_THREE)) {
			Dept parentDept = this.getById(parentId);
			DeptEx deptEx = new DeptEx();
			deptEx.setCompanyId(parentDept.getId());
			deptEx.setCompanyCode(parentDept.getDeptCode());
			deptEx.setCompanyName(parentDept.getDeptName());
			deptEx.setDeptId(dept.getId());
			deptEx.setDeptCode(dept.getDeptCode());
			deptEx.setDeptName(dept.getDeptName());
			return R.data(Arrays.asList(deptEx));
		} else {
			return R.data(baseMapper.getCzAndFgs(DEPT_CATEGORY_TWO.equals(deptCategory) ? deptCode : StringPool.EMPTY));
		}
	}

	@Override
	public Boolean enable(String id, String status) {

		//启用的时候逻辑需要调整 父级得全部启用 不操作下级;禁用还是禁用本级和下级
		boolean rst;
		if(status.equals(StringPool.YES)){
			List<Dept> deptList = getFatherAndOwnDeptByDeptId(id);
			rst = this.update(Wrappers.<Dept>update().lambda().set(Dept::getStatus, status).in(Dept::getId,Optional.ofNullable(deptList).get().stream().map(Dept::getId).collect(Collectors.toList())));
		}else{
			rst = this.update(Wrappers.<Dept>update().lambda().set(Dept::getStatus, status).likeRight(Dept::getDeptCode, SysCache.getDept(id).getDeptCode()));
		}
		return rst;
	}

	@Override
	public List<String> getDeptNatures(String tenantId) {
		List<Dept> list = baseMapper.selectList(Wrappers.<Dept>query().lambda().eq(Dept::getTenantId, tenantId));
		return list.stream().map(e -> e.getNatureId()).distinct().collect(Collectors.toList());
	}

	/**
	 * 获取同一层级最终的部门信息
	 *
	 * @param ancestors
	 * @return
	 */
	@Override
	public Dept selectOwnerDept(String ancestors){
		return baseMapper.selectOwnerDept(ancestors);
	}

	@Override
	public String getDeptCq() {
		return baseMapper.selectCqMaxId();
	}

	public static String strToDbIn(String str){
		return String.format("'%s'", StringUtils.join(str.split(","),"','"));
	}

	@Override
	public List<DeptWithParentVO> getDeptWithParent(String tenantId, String deptName) {
		List<DeptWithParentVO> deptList = baseMapper.getDeptWithParent(tenantId, deptName);
		return deptList;
	}
	/**
	 * 根据子部门ID获取上级部门
	 *
	 * @param deptId   String
	 * @return String
	 */
	@Override
	public Dept getFatherDeptByDeptId(String deptId) {
		//查询出上级deptid
		String fatherId = baseMapper.selectOne(Wrappers.<Dept>query().lambda().eq(Dept::getId, deptId)).getParentId();
		return baseMapper.selectOne(Wrappers.<Dept>query().lambda().eq(Dept::getId, fatherId));
	}

	@Override
	public List<Dept> getListByDeptIds(List<String> deptIds) {
		if (CollectionUtil.isEmpty(deptIds)){
			return null;
		}
		QueryWrapper<Dept> wrapper = new QueryWrapper<>();
		wrapper.lambda().in(Dept::getId,deptIds);
		return baseMapper.selectList(wrapper);
	}

	@Override
	public List<Dept> getDeptList(DeptParamDTO dto) {
		List<String> natureIds = new ArrayList<>();
		if (1 == dto.getDeptLevel()) {
			String[] split = firstDept.split(",");
			natureIds = Arrays.asList(split);
		}
		if (2 == dto.getDeptLevel()) {
			String[] split = secondDept.split(",");
			natureIds = Arrays.asList(split);
		}
		if (3 == dto.getDeptLevel()) {
			String[] split = thirdDept.split(",");
			natureIds = Arrays.asList(split);
		}
		QueryWrapper<Dept> wrapper = new QueryWrapper<>();
		wrapper.lambda().eq(StringUtils.isNotBlank(dto.getDeptName()), Dept::getDeptName, dto.getDeptName())
				.eq(StringUtils.isNotBlank(dto.getFullName()), Dept::getFullName, dto.getFullName())
				.eq(StringUtils.isNotBlank(dto.getTenantId()), Dept::getTenantId, dto.getTenantId())
				.likeLeft(StringUtils.isNotBlank(dto.getLDeptCodeLike()), Dept::getDeptCode, dto.getLDeptCodeLike())
				.likeRight(StringUtils.isNotBlank(dto.getRDeptCodeLike()), Dept::getDeptCode, dto.getRDeptCodeLike())
				.like(StringUtils.isNotBlank(dto.getDeptCodeLike()), Dept::getDeptCode, dto.getDeptCodeLike())
				.in(Dept::getNatureId, natureIds).eq(Dept::getIsDeleted, 0).eq(Dept::getStatus, "yes");
		return baseMapper.selectList(wrapper);
	}

	@Override
	public String getDeptLevel(String deptId) {
		return baseMapper.getDeptLevel(deptId);
	}

	@Override
	public List<Dept> getDeptsByNatureIds(List<String> natureIds) {
		if (natureIds == null || natureIds.size()==0){
			return null;
		}
		QueryWrapper<Dept> wrapper = new QueryWrapper<>();
		wrapper.lambda().in(Dept::getNatureId,natureIds);
		return baseMapper.selectList(wrapper);
	}
}
