<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.system.mapper.RoleMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="roleResultMap" type="com.allcore.system.entity.Role">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="role_name" property="roleName"/>
        <result column="sort" property="sort"/>
        <result column="role_alias" property="roleAlias"/>
        <result column="home_config" property="homeConfig"/>
        <result column="status" property="status"/>
        <result column="is_deleted" property="isDeleted"/>
    </resultMap>

    <resultMap id="treeNodeResultMap" type="com.allcore.core.tool.node.TreeNode">
        <id column="id" property="id"/>
        <result column="parent_id" property="parentId"/>
        <result column="title" property="title"/>
        <result column="value" property="value"/>
        <result column="key" property="key"/>
    </resultMap>

    <select id="selectRolePage" resultMap="roleResultMap">
        select id,
               tenant_id,
               parent_id,
               role_name,
               sort,
               role_alias,
               is_deleted,
               status,
               home_config from sys_role where is_deleted = 0
    </select>

    <!-- 获取所有角色数据用于Java内存递归查询 -->
    <select id="getAllRolesForTree" resultType="java.util.HashMap">
        SELECT id, parent_id, role_name, role_alias, status
        FROM sys_role
        WHERE is_deleted = 0
        <if test="param1!=null">
            and tenant_id = #{param1}
        </if>
    </select>

    <!-- 保持原有方法签名，但实现改为Java递归，这里返回空结果 -->
    <select id="tree" resultMap="treeNodeResultMap">
        SELECT id, parent_id, role_name as title, id as "value", id as "key"
        FROM sys_role
        WHERE 1=0
    </select>

    <select id="getRoleNames" resultType="java.lang.String">
        SELECT
        role_name
        FROM
        sys_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

    <select id="getRoleHomeConfigs" resultType="java.lang.String">
        SELECT
        home_config
        FROM
        sys_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
        and home_config is not null
        and home_config !=''
    </select>


    <select id="getRoleAliases" resultType="java.lang.String">
        SELECT
        role_alias
        FROM
        sys_role
        WHERE
        id IN
        <foreach collection="array" item="ids" index="index" open="(" close=")" separator=",">
            #{ids}
        </foreach>
        and is_deleted = 0
    </select>

</mapper>
