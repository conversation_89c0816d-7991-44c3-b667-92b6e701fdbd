package com.allcore.system.feign;

import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tenant.annotation.NonDS;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.system.dto.DeptParamDTO;
import com.allcore.system.entity.*;
import com.allcore.system.service.*;
import com.allcore.system.vo.*;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.ArrayList;
import java.util.List;

/**
 * 系统服务Feign实现类
 *
 * <AUTHOR>
 */
@NonDS
@ApiIgnore
@RestController
@AllArgsConstructor
public class SysClient implements ISysClient {

	private final IDeptService deptService;

	private final IPostService postService;

	private final IRoleService roleService;

	private final IMenuService menuService;

	private final ITenantService tenantService;

	private final ITenantPackageService tenantPackageService;

	private final IParamService paramService;

	private final IRegionService regionService;

	private final IAuthClientService authClientService;


	@Override
	@GetMapping(MENU)
	public R<Menu> getMenu(String id) {
		return R.data(menuService.getById(id));
	}

	@Override
	public R<List<Menu>> getMenuListByIds(String ids) {
		return R.data(menuService.listByIds(Func.toStrList(ids)));
	}

	@Override
	@GetMapping(ROUTES)
	public R<List<MenuVO>> clientRoutes(String userRoleId, String topMenuId){
		List<MenuVO> list = menuService.routes(StringUtil.isBlank(userRoleId) ? null : userRoleId, topMenuId);
		return R.data(list);
	}


	@Override
	@GetMapping(MENU_BY_CODE)
	public R<List<Menu>> getMenuByCode(String code) {
		return R.data(menuService.getMenuByCode(code));
	}

	@Override
	@PostMapping(MENU_SAVE)
	public R<Boolean> menuSave(@RequestBody Menu menu){
		return R.data(menuService.submit(menu));
	}

	@Override
	@PostMapping(MENU_REMOVE)
	public R<Boolean> menuRemove(@RequestParam("id") String id){
		return R.data(menuService.remove(Wrappers.<Menu>query().lambda().eq(Menu::getId, id)));
	}

	@Override
	@GetMapping(DEPT_NAME_BY_CODE)
	public R<String> getDeptNameByDeptCode(String deptCode) {
		return R.data(deptService.getByDeptCode(deptCode).getDeptName());
	}

	@Override
	@GetMapping(DEPT)
	public R<Dept> getDept(String id) {
		return R.data(deptService.getById(id));
	}

	@Override
	public R<String> getDeptIds(String tenantId, String deptNames) {
		return R.data(deptService.getDeptIds(tenantId, deptNames));
	}

	@Override
	public R<List<Dept>> getDeptInfos(String tenantId, String deptNames) {
		return R.data(deptService.getDeptInfos(tenantId, deptNames));
	}

	@Override
	public R<List<DeptWithParentVO>> getDeptWithParent(String tenantId, String deptName) {
		return R.data(deptService.getDeptWithParent(tenantId, deptName));
	}

	@Override
	public R<String> getDeptIdsByFuzzy(String tenantId, String deptNames) {
		return R.data(deptService.getDeptIdsByFuzzy(tenantId, deptNames));
	}

	@Override
	@GetMapping(DEPT_NAME)
	public R<String> getDeptName(String id) {
		Dept byId = deptService.getById(id);
		if(byId == null){
			throw new ServiceException("未找到部门");
		}
		return R.data(byId.getDeptName());
	}
	@Override
	@GetMapping(DEPT_TREE)
	public R<List<TreeNode>> getDepTree(String tenantId, String status, String disabledNoAuth){
		return R.data(deptService.treeWithParentOneLineFeign(Func.toStrWithEmpty(tenantId, AuthUtil.getUser().getTenantId()),  status,disabledNoAuth, null));
	}

	@Override
	@GetMapping(DEPT_TREE_CATEGORY)
	public R<List<TreeNode>> getDepTreeByCategory(String tenantId, String status, String disabledNoAuth, Integer category) {
		return R.data(deptService.treeFeign(Func.toStrWithEmpty(tenantId, AuthUtil.getUser().getTenantId()),  status,disabledNoAuth, category));
	}

	@Override
	@GetMapping(DEPT_NAMES)
	public R<List<String>> getDeptNames(String deptIds) {
		return R.data(deptService.getDeptNames(deptIds));
	}

	@Override
	@GetMapping(DEPT_CHILD)
	public R<List<Dept>> getDeptChild(String deptId) {
		return R.data(deptService.getDeptChild(deptId));
	}

	@Override
	public R<List<Dept>> getUnDisableDeptChild(String deptId, String tenantId) {
		return R.data(deptService.getUnDisableDeptChild(deptId,tenantId));
	}

	@Override
	@GetMapping(DEPT_BY_CATEGORY)
	public R<List<Dept>> getDeptByCategory(Integer category) {
		return R.data(deptService.getDeptByCategory(category));
	}

	@Override
	@GetMapping(DEPT_CQ)
	public R<String> getDeptCq() {
		return R.data(deptService.getDeptCq());
	}

	@Override
	@GetMapping(DEPT_CHILD_PARENT_ID)
	public R<List<Dept>> getDeptChildByParentId(String parentId) {
		return R.data(deptService.getDeptChildByParentId(parentId));
	}

	@Override
	@PostMapping(DEPT_SAVE)
	public R<Boolean> deptSave(@RequestBody Dept dept){
		return R.data(deptService.submit(dept));
	}

	@Override
	@PostMapping(DEPT_SAVE_BY_ISC)
	public R<Boolean> deptSaveByIsc(@RequestBody Dept dept){
		return R.data(deptService.submitByIsc(dept));
	}

	@Override
	@PostMapping(DEPT_REMOVE)
	public R<Boolean> deptRemove(@RequestParam("id") String id){
		return R.data(deptService.remove(Wrappers.<Dept>query().lambda().eq(Dept::getId, id)));
	}

	@Override
	public R<Post> getPost(String id) {
		return R.data(postService.getById(id));
	}

	@Override
	public R<String> getPostIds(String tenantId, String postNames) {
		return R.data(postService.getPostIds(tenantId, postNames));
	}

	@Override
	public R<String> getPostIdsByFuzzy(String tenantId, String postNames) {
		return R.data(postService.getPostIdsByFuzzy(tenantId, postNames));
	}

	@Override
	public R<String> getPostName(String id) {
		return R.data(postService.getById(id).getPostName());
	}

	@Override
	public R<List<String>> getPostNames(String postIds) {
		return R.data(postService.getPostNames(postIds));
	}

	@Override
	@GetMapping(ROLE)
	public R<Role> getRole(String id) {
		return R.data(roleService.getById(id));
	}

	@Override
	@GetMapping(ROLE_LIST)
	public R<List<Role>> getRoleList(String ids) {
		return R.data(roleService.getRoleList(ids));
	}

	@Override
	@GetMapping(ROLE_BY_ROLE_ALIAS)
	public R<List<Role>> getRoleByRoleAlias(String tenantId, String roleAlias) {
		return R.data(roleService.getRoleByRoleAlias(tenantId,roleAlias));
	}

	@Override
	public R<String> getRoleIds(String tenantId, String roleNames) {
		return R.data(roleService.getRoleIds(tenantId, roleNames));
	}

	@Override
	@GetMapping(ROLE_NAME)
	public R<String> getRoleName(String id) {
		return R.data(roleService.getById(id).getRoleName());
	}

	@Override
	@GetMapping(ROLE_ALIAS)
	public R<String> getRoleAlias(String id) {
		return R.data(roleService.getById(id).getRoleAlias());
	}

	@Override
	@GetMapping(ROLE_NAMES)
	public R<List<String>> getRoleNames(String roleIds) {
		return R.data(roleService.getRoleNames(roleIds));
	}

	@Override
	@GetMapping(ROLE_HOME_CONFIGS)
	public R<List<String>> getRoleHomeConfigs(String roleIds) {
		return R.data(roleService.getRoleHomeConfigs(roleIds));
	}

	@Override
	@GetMapping(ROLE_ALIASES)
	public R<List<String>> getRoleAliases(String roleIds) {
		return R.data(roleService.getRoleAliases(roleIds));
	}

	@Override
	@PostMapping(ROLE_SAVE)
	public R<Boolean> roleSave(@RequestBody Role role){
		return R.data(roleService.submit(role));
	}

	@Override
	@PostMapping(ROLE_REMOVE)
	public R<Boolean> roleRemove(@RequestParam("id") String id){
		return R.data(roleService.remove(Wrappers.<Role>query().lambda().eq(Role::getId, id)));
	}

	@Override
	@PostMapping(GRANT)
	public R<Boolean> grant(@RequestBody GrantVO grantVO) {
		return R.data(roleService.grant(grantVO.getRoleIds(), grantVO.getMenuIds(), grantVO.getDataScopeIds(), grantVO.getApiScopeIds()));
	}

	@Override
	@GetMapping(TENANT)
	public R<Tenant> getTenantById(String id) {
		return R.data(tenantService.getById(id));
	}

	@Override
	@GetMapping(TENANT_ID)
	public R<Tenant> getTenantByTenantId(String tenantId) {
		return R.data(tenantService.getByTenantId(tenantId));
	}

	@Override
	@GetMapping(TENANT_PACKAGE)
	public R<TenantPackage> getTenantPackage(String tenantId) {
		Tenant tenant = tenantService.getByTenantId(tenantId);
		return R.data(tenantPackageService.getById(tenant.getPackageId()));
	}

	@Override
	@GetMapping(PARAM)
	public R<Param> getParam(String id) {
		return R.data(paramService.getById(id));
	}

	@Override
	@GetMapping(PARAM_VALUE)
	public R<String> getParamValue(String paramKey) {
		return R.data(paramService.getValue(paramKey));
	}

	@Override
	@GetMapping(REGION)
	public R<Region> getRegion(String code) {
		return R.data(regionService.getById(code));
	}


	/**
	 * 根据子部门ID获取所有父部门ID
	 *
	 * @param tenantId String
	 * @param deptId   Long
	 * @return String
	 */
	@Override
	@GetMapping(DEPT_GET_DEPT_IDS)
	public R<String> getDeptIdsByDeptId(String tenantId, String deptId) {
		return R.data(deptService.getDeptIdsByDeptId(tenantId, deptId));
	}

	/**
	 * 根据子部门code获取所有父部门ID
	 *
	 * @param tenantId String
	 * @param deptCode   String
	 * @return String
	 */
	@Override
	@GetMapping(DEPT_GET_DEPT_CODES)
	public R<String> getDeptCodesByDeptCode(String tenantId, String deptCode){
		return R.data(deptService.getDeptCodesByDeptCode(tenantId, deptCode));
	}
	/**
	 * 根据子部门ID获取所有父部门 包含子部门
	 ** @param deptId
	 * @return String
	 */
	@Override
	@GetMapping(DEPT_GET_DEPT_INFOS)
	public R<List<Dept>> getFatherAndOwnDeptByDeptId(String deptId) {
		return R.data(deptService.getFatherAndOwnDeptByDeptId(deptId));
	}

	/**
	 * 查询子行政区域,通过regionLevel过滤
	 *
	 * @param regionLevel 层级
	 * @return 区域集合
	 */
	@Override
	public R<List<Region>> getRegionByLevel(int regionLevel) {
		return R.data(regionService.getRegionByLevel(regionLevel));
	}

	/**
	 * 根据deptCode likeRight查询
	 *
	 * @param deptCode 单位code
	 * @return 单位集合
	 */
	@Override
	public R<List<Dept>> getDeptLikeByDeptCode(String deptCode) {
		return R.data(deptService.getDeptLikeByDeptCode(deptCode));
	}

	@Override
	@GetMapping(GET_CHILD_AND_OWN_BY_DEPT_ID)
	public R<List<Dept>> getChildAndOwnByDeptId(String deptId) {
		return R.data(deptService.getChildAndOwnByDeptId(deptId));
	}

	/**
	 * 根据deptCode获取单位信息
	 *
	 * @param deptCode 单位code
	 * @return DeptVO
	 */
	@Override
	public DeptVO getDeptInfoByDeptCode(String deptCode) {
		return deptService.getDeptInfoByDeptCode(deptCode);
	}

	/**
	 * 根据deptCode获取单位信息
	 *
	 * @param deptCode 单位code
	 * @return DeptVO
	 */
	@Override
	public R<Dept> getDeptByDeptCode(String deptCode) {
		return StringUtil.isBlank(deptCode)? R.data(new Dept()): R.data(deptService.getByDeptCode(deptCode));
	}

	/**
	 * 批量查询region
	 *
	 * @param regionCodes region code拼接的字符串
	 * @return
	 */
	@Override
	public R<List<Region>> getRegionsByCodes(String regionCodes) {
		return  StringUtil.isBlank(regionCodes)? R.data(new ArrayList<Region>()): regionService.getRegionsByCodes(regionCodes);
	}

	/**
	 * 获取场站信息和分公司信息
	 *
	 * @param deptCode 指定单位
	 * @return
	 */
	@Override
	public R<List<DeptEx>> getCzAndFgs(String deptCode) {
		return StringUtil.isBlank(deptCode)? R.data(new ArrayList<>()): deptService.getCzAndFgs(deptCode);
	}

	@Override
	@GetMapping(DEPT_NATURES)
	public R<List<String>> getDeptNatures(String tenantId){
		return R.data(deptService.getDeptNatures(tenantId));
	}

	@Override
	@GetMapping(CLIENT)
	public AuthClient getClientByClientId(String clientId){
		return authClientService.getClientByClientId(clientId);
	}

	@Override
	@GetMapping(DEPT_GET_DEPT_ID)
	public Dept getFatherDeptByDeptId(String deptId) {
		return deptService.getFatherDeptByDeptId(deptId);
	}

	@Override
	public String getParamByKey(String key) {
		return paramService.getValue(key);
	}

	@Override
	public Param getParamObjectByKey(String key) {
		return paramService.getParam(key);
	}

	@Override
	public List<Dept> getDeptList(DeptParamDTO dto) {
		return deptService.getDeptList(dto);
	}

	@Override
	public List<Dept> getListByDeptIds(List<String> deptIds) {
		return deptService.getListByDeptIds(deptIds);
	}

	@Override
	public String getDeptLevel(String deptId) {
		return deptService.getDeptLevel(deptId);
	}

	@Override
	public List<Dept> getDeptsByNatureIds(List<String> natureIds) {
		return deptService.getDeptsByNatureIds(natureIds);
	}
}
