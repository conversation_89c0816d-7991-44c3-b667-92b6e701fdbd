package com.allcore.system.wrapper;


import com.allcore.core.mp.support.BaseEntityWrapper;
import com.allcore.core.tool.constant.AllcoreConstant;
import com.allcore.core.tool.node.ForestNodeMerger;
import com.allcore.core.tool.utils.BeanUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.dict.cache.DictCache;
import com.allcore.dict.enums.DictEnum;
import com.allcore.system.cache.SysCache;
import com.allcore.system.entity.Dept;
import com.allcore.system.vo.DeptVO;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 包装类,返回视图层所需的字段
 *
 * <AUTHOR>
 */
public class DeptWrapper extends BaseEntityWrapper<Dept, DeptVO> {

	public static DeptWrapper build() {
		return new DeptWrapper();
	}

	@Override
	public DeptVO entityVO(Dept dept) {
		DeptVO deptVO = Objects.requireNonNull(BeanUtil.copy(dept, DeptVO.class));
		if (Func.equals(dept.getParentId(), AllcoreConstant.TOP_PARENT_ID)) {
			deptVO.setParentName(AllcoreConstant.TOP_PARENT_NAME);
		} else {
			Dept parent = SysCache.getDept(dept.getParentId());
			deptVO.setParentName(parent.getDeptName());
		}
		deptVO.setNatureIdZh(DictCache.getValue(DictEnum.DEPT_NATURE,dept.getNatureId()));
		deptVO.setStatusZh(DictCache.getValue(DictEnum.NOW_STATUS, dept.getStatus()));

		String category = DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory());
		deptVO.setDeptCategoryName(category);
		return deptVO;
	}


	public List<DeptVO> listNodeVO(List<Dept> list) {
		List<DeptVO> collect = list.stream().map(dept -> {
			DeptVO deptVO = BeanUtil.copy(dept, DeptVO.class);
			String category = DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory());
			Objects.requireNonNull(deptVO).setDeptCategoryName(category);
			return deptVO;
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

	public List<DeptVO> listNodeLazyVO(List<DeptVO> list) {
		List<DeptVO> collect = list.stream().peek(dept -> {
			if (Func.equals(dept.getParentId(), AllcoreConstant.TOP_PARENT_ID)) {
				Objects.requireNonNull(dept).setParentName(AllcoreConstant.TOP_PARENT_NAME);
			} else {
				Dept parent = SysCache.getDept(dept.getParentId());
				Objects.requireNonNull(dept).setParentName(parent.getDeptName());
			}
			Objects.requireNonNull(dept).setNatureIdZh(DictCache.getValue(DictEnum.DEPT_NATURE,dept.getNatureId()));
			Objects.requireNonNull(dept).setStatusZh(DictCache.getValue(DictEnum.NOW_STATUS, dept.getStatus()));
			String category = DictCache.getValue(DictEnum.ORG_CATEGORY, dept.getDeptCategory());
			Objects.requireNonNull(dept).setDeptCategoryName(category);
		}).collect(Collectors.toList());
		return ForestNodeMerger.merge(collect);
	}

}
