package com.allcore.system.controller;


import com.allcore.core.boot.ctrl.AllcoreController;
import com.allcore.core.cache.utils.CacheUtil;
import com.allcore.core.mp.support.Condition;
import com.allcore.core.secure.AllcoreUser;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tenant.annotation.NonDS;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.api.ResultCode;
import com.allcore.core.tool.node.TreeNode;
import com.allcore.core.tool.support.Kv;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.system.entity.Menu;
import com.allcore.system.entity.RoleTopMenu;
import com.allcore.system.entity.TopMenu;
import com.allcore.system.service.IMenuService;
import com.allcore.system.service.ITopMenuService;
import com.allcore.system.vo.CheckedTreeVO;
import com.allcore.system.vo.<PERSON>;
import com.allcore.system.vo.MenuVO;
import com.allcore.system.wrapper.MenuWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.github.xiaoymin.knife4j.annotations.ApiOperationSupport;
import io.swagger.annotations.*;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.Map;

import static com.allcore.core.cache.constant.CacheConstant.MENU_CACHE;

/**
 * （菜单管理控制器）
 * <AUTHOR>
 * @date 2023/03/21 19:44
 */
@NonDS
@RestController
@AllArgsConstructor
@RequestMapping("/menu")
@Api(value = "菜单", tags = "菜单")
public class MenuController extends AllcoreController {

	private final IMenuService menuService;
	private final ITopMenuService topMenuService;

	/**
	 * （根据id查询单条）
	 * <AUTHOR>
	 * @date 2023/03/22 18:43
	 * @param id
	 * @return com.allcore.core.tool.api.R<com.allcore.system.vo.MenuVO>
	 */
	@GetMapping("/detail")
	@ApiOperationSupport(order = 1)
	@ApiOperation(value = "详情", notes = "传入menu")
	public R<MenuVO> detail(@RequestParam String id) {
		Menu menu = new Menu();
		menu.setId(id);
		Menu detail = menuService.getOne(Condition.getQueryWrapper(menu));
		return R.data(MenuWrapper.build().entityVO(detail));
	}

	/**
	 * （条件构造器列表）
	 * <AUTHOR>
	 * @date 2023/03/22 18:43
	 * @param menu
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.MenuVO>>
	 */
	@GetMapping("/list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "菜单编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "菜单名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 2)
	@ApiOperation(value = "列表", notes = "传入menu")
	public R<List<MenuVO>> list(@ApiIgnore @RequestParam Map<String, Object> menu) {
		List<Menu> list = menuService.list(Condition.getQueryWrapper(menu, Menu.class).lambda().orderByAsc(Menu::getSort));
		return R.data(MenuWrapper.build().listNodeVO(list));
	}

	/**
	 * （懒加载列表 名称模糊+code模糊+别名模糊+父id）
	 * <AUTHOR>
	 * @date 2023/03/22 18:45
	 * @param parentId
	 * @param menu
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.MenuVO>>
	 */
	@GetMapping("/lazy-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "菜单编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "菜单名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 3)
	@ApiOperation(value = "懒加载列表", notes = "传入menu")
	public R<List<MenuVO>> lazyList(String parentId, @ApiIgnore @RequestParam Map<String, Object> menu) {
		List<MenuVO> list = menuService.lazyList(parentId, menu);
		return R.data(MenuWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * （条件构造器列表:光菜单）
	 * <AUTHOR>
	 * @date 2023/03/22 18:47
	 * @param menu
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.MenuVO>>
	 */
	@GetMapping("/menu-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "菜单编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "菜单名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 4)
	@ApiOperation(value = "菜单列表", notes = "传入menu")
	public R<List<MenuVO>> menuList(@ApiIgnore @RequestParam Map<String, Object> menu) {
		List<Menu> list = menuService.list(Condition.getQueryWrapper(menu, Menu.class).lambda().eq(Menu::getCategory, 1).orderByAsc(Menu::getSort));
		return R.data(MenuWrapper.build().listNodeVO(list));
	}

	/**
	 * （懒加载列表:光菜单 名称模糊+code模糊+别名模糊+父id）
	 * <AUTHOR>
	 * @date 2023/03/22 18:50
	 * @param parentId
	 * @param menu
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.MenuVO>>
	 */
	@GetMapping("/lazy-menu-list")
	@ApiImplicitParams({
		@ApiImplicitParam(name = "code", value = "菜单编号", paramType = "query", dataType = "string"),
		@ApiImplicitParam(name = "name", value = "菜单名称", paramType = "query", dataType = "string")
	})
	@ApiOperationSupport(order = 5)
	@ApiOperation(value = "懒加载菜单列表", notes = "传入menu")
	public R<List<MenuVO>> lazyMenuList(String parentId, @ApiIgnore @RequestParam Map<String, Object> menu) {
		List<MenuVO> list = menuService.lazyMenuList(parentId, menu);
		return R.data(MenuWrapper.build().listNodeLazyVO(list));
	}

	/**
	 * （新增或修改）
	 * <AUTHOR>
	 * @date 2023/03/22 18:51
	 * @param menu
	 * @return com.allcore.core.tool.api.R
	 */
	@PostMapping("/submit")
	@ApiOperationSupport(order = 6)
	@ApiOperation(value = "新增或修改", notes = "传入menu")
	public R submit(@Valid @RequestBody Menu menu) {
		if (menuService.submit(menu)) {
			CacheUtil.clear(MENU_CACHE);
			CacheUtil.clear(MENU_CACHE, Boolean.FALSE);
			// 返回懒加载树更新节点所需字段
			Kv kv = Kv.create().set("id", String.valueOf(menu.getId()));
			return R.data(kv);
		}
		return R.fail("操作失败");
	}


	/**
	 * （删除）
	 * <AUTHOR>
	 * @date 2023/03/22 18:52
	 * @param ids
	 * @return com.allcore.core.tool.api.R
	 */
	@PostMapping("/remove")
	@ApiOperationSupport(order = 7)
	@ApiOperation(value = "删除", notes = "传入ids")
	public R remove(@ApiParam(value = "主键集合", required = true) @RequestParam String ids) {
		CacheUtil.clear(MENU_CACHE);
		CacheUtil.clear(MENU_CACHE, Boolean.FALSE);
		return R.status(menuService.removeMenu(ids));
	}

	/**
	 * （前端菜单数据 角色id+顶部菜单id）
	 * <AUTHOR>
	 * @date 2023/03/22 18:52
	 * @param user
	 * @param topMenuId
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.MenuVO>>
	 */
	@GetMapping("/routes")
	@ApiOperationSupport(order = 8)
	@ApiOperation(value = "前端菜单数据", notes = "前端菜单数据")
	public R<List<MenuVO>> routes(AllcoreUser user, String topMenuId) {
		if(user == null || StringUtil.isBlank(user.getRoleId())){
			return R.fail(ResultCode.CLIENT_UN_AUTHORIZED);
		}
		List<MenuVO> list = menuService.routes((user == null) ? null : user.getRoleId(), topMenuId);
		return R.data(list);
	}

//	/**
//	 * （前端按钮数据）
//	 * <AUTHOR>
//	 * @date 2023/03/22 18:54
//	 * @param roleId
//	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.system.vo.MenuVO>>
//	 */
//	@GetMapping("/buttons")
//	@ApiOperationSupport(order = 10)
//	@ApiOperation(value = "前端按钮数据", notes = "前端按钮数据")
//	public R<List<MenuVO>> buttons(@RequestParam String roleId) {
//		List<MenuVO> list = menuService.buttons(roleId);
//		return R.data(list);
//	}

	/**
	 * 前端按钮数据
	 */
	@GetMapping("/buttons")
	@ApiOperationSupport(order = 10)
	@ApiOperation(value = "前端按钮数据", notes = "前端按钮数据")
	public R<List<MenuVO>> buttons(AllcoreUser user) {
		List<MenuVO> list = menuService.buttons(user.getRoleId());
		return R.data(list);
	}

	/**
	 * （获取菜单树形结构）
	 * <AUTHOR>
	 * @date 2023/03/22 18:54
	 * @return com.allcore.core.tool.api.R<java.util.List < com.allcore.core.tool.node.TreeNode>>
	 */
	@GetMapping("/tree")
	@ApiOperationSupport(order = 11)
	@ApiOperation(value = "树形结构", notes = "树形结构")
	public R<List<TreeNode>> tree() {
		List<TreeNode> tree = menuService.tree();
		return R.data(tree);
	}

	/**
	 * （获取权限分配树形结构）
	 * <AUTHOR>
	 * @date 2023/03/22 18:56
	 * @param user
	 * @return com.allcore.core.tool.api.R<com.allcore.system.vo.GrantTreeVO>
	 */
	@GetMapping("/grant-tree")
	@ApiOperationSupport(order = 12)
	@ApiOperation(value = "权限分配树形结构", notes = "权限分配树形结构")
	public R<GrantTreeVO> grantTree(AllcoreUser user) {
		GrantTreeVO vo = new GrantTreeVO();
		vo.setMenu(menuService.grantTree(user));
		vo.setDataScope(menuService.grantDataScopeTree(user));
		vo.setApiScope(menuService.grantApiScopeTree(user));
		return R.data(vo);
	}

	/**
	 * 获取权限分配树形结构
	 */
	@GetMapping("/role-tree-keys")
	@ApiOperationSupport(order = 13)
	@ApiOperation(value = "角色所分配的树", notes = "角色所分配的树")
	public R<CheckedTreeVO> roleTreeKeys(String roleIds) {
		CheckedTreeVO vo = new CheckedTreeVO();
		vo.setMenu(menuService.roleTreeKeys(roleIds));
		vo.setTopMenu(menuService.roleTopMenuKeys(roleIds));
		vo.setDataScope(menuService.dataScopeTreeKeys(roleIds));
		vo.setApiScope(menuService.apiScopeTreeKeys(roleIds));
		return R.data(vo);
	}

	/**
	 * 获取顶部菜单树形结构
	 */
	@GetMapping("/grant-top-tree")
	@ApiOperationSupport(order = 14)
	@ApiOperation(value = "顶部菜单树形结构", notes = "顶部菜单树形结构")
	public R<GrantTreeVO> grantTopTree(AllcoreUser user) {
		GrantTreeVO vo = new GrantTreeVO();
		vo.setMenu(menuService.grantTopTree(user));
		return R.data(vo);
	}

	/**
	 * 获取顶部菜单树形结构
	 */
	@GetMapping("/top-tree-keys")
	@ApiOperationSupport(order = 15)
	@ApiOperation(value = "顶部菜单所分配的树", notes = "顶部菜单所分配的树")
	public R<CheckedTreeVO> topTreeKeys(String topMenuIds) {
		CheckedTreeVO vo = new CheckedTreeVO();
		vo.setMenu(menuService.topTreeKeys(topMenuIds));
		return R.data(vo);
	}

	/**
	 * 顶部菜单数据
	 */
	@GetMapping("/top-menu")
	@ApiOperationSupport(order = 16)
	@ApiOperation(value = "顶部菜单数据", notes = "顶部菜单数据")
	public R<List<TopMenu>> topMenu(AllcoreUser user) {
		if (Func.isEmpty(user)) {
			return R.fail(ResultCode.CLIENT_UN_AUTHORIZED);
		}
		// 顶部菜单若无子菜单 不希望显示
		//语法报错，暂注释 报错信息Unknown column 'a.sort' in 'order clause'
//		List<TopMenu> list = topMenuService.listHasMenu();

		List<TopMenu> list = topMenuService.topMenu(user.getRoleId());
		return R.data(list);
	}

	/**
	 * 获取配置的角色权限
	 */
	@GetMapping("auth-routes")
	@ApiOperationSupport(order = 17)
	@ApiOperation(value = "菜单的角色权限")
	public R<List<Kv>> authRoutes(AllcoreUser user) {
		if (Func.isEmpty(user)) {
			return null;
		}
		return R.data(menuService.authRoutes(user));
	}
}
