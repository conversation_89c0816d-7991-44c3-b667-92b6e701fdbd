package com.allcore.system.mapper;


import com.allcore.core.tool.node.TreeNode;
import com.allcore.system.entity.Role;
import com.allcore.system.vo.RoleVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;
import java.util.Map;

/**
 * Mapper 接口
 *
 * <AUTHOR>
 */
public interface RoleMapper extends BaseMapper<Role> {

	/**
	 * （自定义分页）
	 * <AUTHOR>
	 * @date 2023/03/22 18:27
	 * @param page
	 * @param role
	 * @return java.util.List<com.allcore.system.vo.RoleVO>
	 */
	List<RoleVO> selectRolePage(IPage page, RoleVO role);

	/**
	 * （获取所有角色数据用于Java内存递归查询）
	 * <AUTHOR>
	 * @date 2022/09/20 17:18
	 * @param tenantId
	 * @return java.util.List<java.util.Map<java.lang.String, java.lang.Object>>
	 */
	List<Map<String, Object>> getAllRolesForTree(String tenantId);

	/**
	 * （获取树形节点 排除某个角色别名）
	 * <AUTHOR>
	 * @date 2022/09/20 17:18
	 * @param tenantId
	 * @param excludeRole
	 * @param status
	 * @return java.util.List<com.allcore.core.tool.node.TreeNode>
	 */
	List<TreeNode> tree(String tenantId, String excludeRole,String status,String userRole);

	/**
	 * （根据ids获取角色名）
	 * <AUTHOR>
	 * @date 2023/03/22 18:30
	 * @param ids
	 * @return java.util.List<java.lang.String>
	 */
	List<String> getRoleNames(String[] ids);

	/**
	 * （根据ids获取角色首页配置）
	 * <AUTHOR>
	 * @date 2023/03/22 18:30
	 * @param ids
	 * @return java.util.List<java.lang.String>
	 */
	List<String> getRoleHomeConfigs(String[] ids);

	/**
	 * （获取角色别名）
	 * <AUTHOR>
	 * @date 2023/03/22 18:31
	 * @param ids
	 * @return java.util.List<java.lang.String>
	 */
	List<String> getRoleAliases(String[] ids);

}
