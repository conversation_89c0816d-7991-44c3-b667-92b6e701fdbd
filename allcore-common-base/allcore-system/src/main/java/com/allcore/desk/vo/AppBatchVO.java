package com.allcore.desk.vo;


import com.allcore.desk.entity.AppBatch;
import io.swagger.annotations.ApiModel;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 视图实体类
 *
 * <AUTHOR>
 * @since 2022-10-11
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AppBatchVO对象", description = "AppBatchVO对象")
public class AppBatchVO extends AppBatch {
	private static final long serialVersionUID = 1L;

	/**
	 * 绑定数量
	 */
	private int bindingNum;
	/**
	 * 未绑定数量
	 */
	private int unBindingNum;
	/**
	 * 使用单位名称
	 */
	private String useDeptName;
	/**
	 * 购买单位名称
	 */
	private String buyDeptName;
	/**
	 * 已授权数量
	 */
	private int authNum;

	/**
	 * 绑定自己批次的设备数量
	 */
	private int selfBindingNum;
}
