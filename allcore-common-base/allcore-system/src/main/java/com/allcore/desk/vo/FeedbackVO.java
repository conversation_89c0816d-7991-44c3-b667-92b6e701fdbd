package com.allcore.desk.vo;


import com.allcore.desk.entity.Feedback;
import com.allcore.desk.entity.FeedbackAnswer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 意见反馈表视图实体类
 *
 * <AUTHOR>
 * @since 2022-08-26
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "FeedbackVO对象", description = "意见反馈表")
public class FeedbackVO extends Feedback {
	private static final long serialVersionUID = 1L;

	/**
	 * 意见反馈模糊查询条件（标题/内容）
	 */
	@JsonSerialize(using = ToStringSerializer.class)
	@ApiModelProperty(value = "是否我的 0我的发布  1公共发布 默认 0")
	private String queryStr;

	/**
	 * 意见回复
	 */
	@ApiModelProperty(value = "意见回复")
	private FeedbackAnswer feedbackAnswer;

//	/**
//	 * 意见回复集合
//	 */
//	@ApiModelProperty(value = "意见回复集合")
//	private List<FeedbackAnswer> feedbackAnswerList;

}
