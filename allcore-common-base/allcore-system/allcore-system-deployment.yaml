kind: Deployment
apiVersion: apps/v1
metadata:
  name: allcore-system
  #namespace: hbwrj-info
  labels:
    app: allcore-system
  annotations:
    deployment.kubernetes.io/revision: '2'
    kubectl.kubernetes.io/last-applied-configuration: >
      {"apiVersion":"apps/v1","kind":"Deployment","metadata":{"annotations":{"deployment.kubernetes.io/revision":"1","kubesphere.io/creator":"project-admin"},"labels":{"app":"allcore-system"},"name":"allcore-system","namespace":"product-project"},"spec":{"progressDeadlineSeconds":600,"replicas":1,"revisionHistoryLimit":10,"selector":{"matchLabels":{"app":"allcore-system"}},"strategy":{"rollingUpdate":{"maxSurge":"25%","maxUnavailable":"25%"},"type":"RollingUpdate"},"template":{"metadata":{"creationTimestamp":null,"labels":{"app":"allcore-system"}},"spec":{"containers":[{"env":[{"name":"NACOS_CONFIG","valueFrom":{"configMapKeyRef":{"key":"NACOS_CONFIG","name":"common-conf"}}},{"name":"NACOS_DISCOVERY","valueFrom":{"configMapKeyRef":{"key":"NACOS_DISCOVERY","name":"common-conf"}}},{"name":"SPRING_PROFILES_ACTIVE","valueFrom":{"configMapKeyRef":{"key":"SPRING_PROFILES_ACTIVE","name":"common-conf"}}},{"name":"TZ","valueFrom":{"configMapKeyRef":{"key":"TZ","name":"common-conf"}}}],"image":"allcore.io/allcore-devops/allcore-system:SNAPSHOT-5","imagePullPolicy":"Always","name":"allcore-system","ports":[{"containerPort":18100,"name":"tcp-18100","protocol":"TCP"}],"resources":{},"terminationMessagePath":"/dev/termination-log","terminationMessagePolicy":"File","volumeMounts":[{"mountPath":"/etc/localtime","name":"host-time","readOnly":true}]}],"dnsPolicy":"ClusterFirst","imagePullSecrets":[{"name":"harbor"}],"restartPolicy":"Always","schedulerName":"default-scheduler","securityContext":{},"serviceAccount":"default","serviceAccountName":"default","terminationGracePeriodSeconds":30,"volumes":[{"hostPath":{"path":"/etc/localtime","type":""},"name":"host-time"}]}}}}
    kubesphere.io/creator: allcore
spec:
  replicas: 1
  selector:
    matchLabels:
      app: allcore-system
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: allcore-system
      annotations:
        kubesphere.io/restartedAt: '2023-03-06T04:14:11.403Z'
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
      containers:
        - name: allcore-system
          image: 'allcore.io/allcore/allcore-system:1.0.0.RELEASE'
          ports:
            - name: tcp-18082
              containerPort: 18082
              protocol: TCP
          env:
            - name: NACOS_CONFIG
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_CONFIG
            - name: NACOS_DISCOVERY
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_DISCOVERY
            - name: NACOS_NAMESPACE
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_NAMESPACE
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: SPRING_PROFILES_ACTIVE
            - name: TZ
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: TZ
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirstWithHostNet
      hostNetwork: true
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: harbor-repo
      schedulerName: default-scheduler
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxUnavailable: 25%
      maxSurge: 25%
  revisionHistoryLimit: 10
  progressDeadlineSeconds: 600
