package com.allcore.business.service;

import com.allcore.common.base.ZxhcService;
import com.allcore.core.entity.FlowConfig;
import com.allcore.core.mp.base.BaseService;
import com.allcore.core.vo.FlowConfigVO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 业务流程设置 服务类
 *
 * <AUTHOR>
 * @since 2023-10-16
 */
public interface IFlowConfigService extends ZxhcService<FlowConfig> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param flowConfig
	 * @return
	 */
	IPage<FlowConfigVO> selectFlowConfigPage(IPage<FlowConfigVO> page, FlowConfigVO flowConfig);

}
