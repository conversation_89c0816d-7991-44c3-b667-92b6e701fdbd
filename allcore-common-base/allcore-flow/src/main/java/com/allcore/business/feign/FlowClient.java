
package com.allcore.business.feign;

import com.allcore.business.service.IBusinessRelationService;
import com.allcore.core.constant.ProcessConstant;
import com.allcore.core.entity.AllcoreFlow;
import com.allcore.core.entity.BusinessRelation;
import com.allcore.core.enums.FlowModeEnum;
import com.allcore.core.feign.IFlowClient;
import com.allcore.core.tenant.annotation.NonDS;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.support.Kv;
import com.allcore.core.tool.utils.CollectionUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.core.utils.FlowUtil;
import com.allcore.core.utils.TaskUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.flowable.bpmn.model.*;
import org.flowable.engine.*;
import org.flowable.engine.repository.ProcessDefinition;
import org.flowable.engine.repository.ProcessDefinitionQuery;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.api.TaskQuery;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程远程调用实现类
 *
 * <AUTHOR>
 */
@NonDS
@RestController
@AllArgsConstructor
public class FlowClient implements IFlowClient {

	private final RuntimeService runtimeService;
	private final IdentityService identityService;
	private final TaskService taskService;
	private final IBusinessRelationService iBusinessRelationService;
	private final RepositoryService repositoryService;
	private final HistoryService historyService;

	private final ProcessEngine processEngine;

	/**
	 * 流程启动-管理平台启动
	 *
	 * @param processDefinitionId 流程id
	 * @param businessKey         业务key
	 * @param variables           参数
	 * @return
	 */
	@Override
	@PostMapping(START_PROCESS_INSTANCE_BY_ID)
	public R<AllcoreFlow> startProcessInstanceById(String processDefinitionId, String businessKey, @RequestBody Map<String, Object> variables) {
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinitionId, businessKey, variables);
		// 组装流程通用类
		AllcoreFlow flow = new AllcoreFlow();
		flow.setProcessInstanceId(processInstance.getId());
		return R.data(flow);
	}

	/**
	 * 流程启动-业务启动
	 *
	 * @param processDefinitionId 流程id
	 * @param tableName           业务表名
	 * @param tableGuid           业务表主键id
	 * @param variables           流程附带数据
	 * @return
	 */
	@Override
	@PostMapping(START_BUSINESS_PROCESS_INSTANCE_BY_ID)
	public R<AllcoreFlow> startBusinessProcessInstanceById(String processDefinitionId, String tableName, String tableGuid, @RequestBody Map<String, Object> variables) {
		BusinessRelation businessRelation = new BusinessRelation();
		businessRelation.setProcessDefinitionGuid(processDefinitionId);
		businessRelation.setTableName(tableName);
		businessRelation.setTableGuid(tableGuid);
		businessRelation.setIsDeleted(0);
		String businessKey = FlowUtil.getBusinessKey(tableName, tableGuid);
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinitionId, businessKey, variables);
		if (Func.isNotEmpty(processInstance.getProcessInstanceId())) {
			businessRelation.setProcessInstanceGuid(processInstance.getId());
			iBusinessRelationService.save(businessRelation);
			return R.success("流程启动成功");
		} else {
			return R.fail("流程启动失败");
		}
	}

	/**
	 * 开启流程-业务开启
	 *
	 * @param processDefinitionId 流程id
	 * @param tableName           业务表名
	 * @param tableGuid           业务表id
	 * @param userId			  用户ID
	 * @param createTime          创建时间
	 * @param flag                是否迁移数据
	 * @param variables           参数
	 * @return AllcoreFlow
	 */
	@Override
	@PostMapping(START_BUSINESS_PROCESS_INSTANCE_BY_ID2)
	public R<AllcoreFlow> startBusinessProcessInstanceById2(String processDefinitionId, String tableName, String tableGuid, String userId, Date createTime, Integer flag, Map<String, Object> variables) {
		BusinessRelation businessRelation = new BusinessRelation();
		businessRelation.setProcessDefinitionGuid(processDefinitionId);
		businessRelation.setTableName(tableName);
		businessRelation.setTableGuid(tableGuid);
		businessRelation.setIsDeleted(0);
		businessRelation.setFlag(flag);
		businessRelation.setCreateTime(createTime);
		String businessKey = FlowUtil.getBusinessKey(tableName, tableGuid);
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser(userId));
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceById(processDefinitionId, businessKey, variables);
		if (Func.isNotEmpty(processInstance.getProcessInstanceId())) {
			businessRelation.setProcessInstanceGuid(processInstance.getId());
			iBusinessRelationService.save(businessRelation);
			return R.success("流程启动成功");
		} else {
			return R.fail("流程启动失败");
		}
	}

	/**
	 * 流程同意-表单后按钮直接同意 或 驳回
	 *
	 * @param tableGuid 业务表id
	 * @param comment   评论
	 * @param passNum   是否通过
	 * @return
	 */
	@Override
	@PostMapping(COMPLETE_TASK_DIRECT)
	public R completeTaskDirect(String tableGuid, String comment, Integer passNum) {
		QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper();
		queryWrapper.eq("table_guid", tableGuid);
		BusinessRelation businessRelation = iBusinessRelationService.getOne(queryWrapper);
		String processInstanceId = businessRelation.getProcessInstanceGuid();
		String taskUser = TaskUtil.getTaskUser();
		//执行到下一节点
		completeToNext(processInstanceId, taskUser, comment, passNum);

		List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceEndTime().asc().list();
		if (Func.isNotEmpty(historyTasks)) {
			HistoricTaskInstance historyTask = historyTasks.get(0);
			boolean b =	getNextTask( processInstanceId,historyTask.getTaskDefinitionKey());
			//如果下一节点是 结束节点  则再走一步
			if(b){
				completeToNext(processInstanceId, taskUser, "", 1);
			}
		}
		return R.success("流程提交成功");
	}


	@Override
	public R completeTaskDirect2(String tableGuid, String comment, Integer passNum, String userId) {
		QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper();
		queryWrapper.eq("table_guid", tableGuid);
		BusinessRelation businessRelation = iBusinessRelationService.getOne(queryWrapper);
		String processInstanceId = businessRelation.getProcessInstanceGuid();
		String taskUser = TaskUtil.getTaskUser(userId);
		//执行到下一节点
		completeToNext(processInstanceId, taskUser, comment, passNum);

		List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceEndTime().asc().list();
		if (Func.isNotEmpty(historyTasks)) {
			HistoricTaskInstance historyTask = historyTasks.get(0);
			boolean b =	getNextTask( processInstanceId,historyTask.getTaskDefinitionKey());
			//如果下一节点是 结束节点  则再走一步
			if(b){
				completeToNext(processInstanceId, taskUser, "", 1);
			}
		}
		return R.success("流程提交成功");
	}

	/**
	 * 判断下一个节点是否为 结束节点
	 * @param processInstanceId
	 * @param taskDefinitionKey
	 * @return
	 */
	private boolean getNextTask(String processInstanceId,String taskDefinitionKey) {
		//获取流程发布Id信息
		String definitionId = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult().getProcessDefinitionId();
		//获取bpm对象
		BpmnModel bpmnModel = repositoryService.getBpmnModel(definitionId);
		//传节点定义key 获取当前节点
		FlowNode flowNode = (FlowNode) bpmnModel.getFlowElement(taskDefinitionKey);
		//输出连线
		List<SequenceFlow> outgoingFlows = flowNode.getOutgoingFlows();
		//遍历返回下一个节点信息
		for (SequenceFlow outgoingFlow : outgoingFlows) {
			//类型自己判断
			FlowElement targetFlowElement = outgoingFlow.getTargetFlowElement();
			//用户任务
			if (targetFlowElement instanceof EndEvent) {
				return true;
			}
		}
		return false;
	}


	private void completeToNext(String processInstanceId, String taskUser, String comment, Integer passNum) {
		// 通用流程等待签收的任务 taskCandidateGroupIn(Func.toStrList(taskGroup))
		TaskQuery claimRoleWithoutTenantIdQuery = taskService.createTaskQuery().taskWithoutTenantId().processInstanceId(processInstanceId)
			.includeProcessVariables().active().orderByTaskCreateTime().desc();
		String claimTaskId = claimRoleWithoutTenantIdQuery.singleResult().getId();
		//签收
		taskService.claim(claimTaskId, taskUser);
		// 已签收的任务
		TaskQuery todoQuery = taskService.createTaskQuery().taskAssignee(taskUser).processInstanceId(processInstanceId).active()
			.includeProcessVariables().orderByTaskCreateTime().desc();
		String taskId = todoQuery.singleResult().getId();
		// 增加评论
		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
			taskService.addComment(taskId, processInstanceId, comment);
		}
		Map<String, Object> variables = new HashMap<>(1);
		variables.put(ProcessConstant.PASS_KEY, passNum);
		// 执行任务
		taskService.complete(taskId, variables);
	}


	/**
	 * 流程同意
	 *
	 * @param taskId            任务id
	 * @param processInstanceId 流程实例id
	 * @param comment           评论
	 * @param variables         参数
	 * @return
	 */
	@Override
	@PostMapping(COMPLETE_TASK)
	public R completeTask(String taskId, String processInstanceId, String comment, Integer passNum, @RequestBody Map<String, Object> variables) {
		//签收
		taskService.claim(taskId, TaskUtil.getTaskUser());
		// 增加评论
		if (StringUtil.isNoneBlank(processInstanceId, comment)) {
			taskService.addComment(taskId, processInstanceId, comment);
		}
		// 非空判断
		if (Func.isEmpty(variables)) {
			variables = Kv.create();
		}
		variables.put(ProcessConstant.PASS_KEY, passNum);
		// 执行任务
		taskService.complete(taskId, variables);
		return R.success("流程提交成功");
	}


	@Override
	@PostMapping(DELETE_PROCESS)
	public R deleteProcessInstance(String tableGuid, String deleteReason) {
		QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("table_guid", tableGuid);
		BusinessRelation businessRelation = iBusinessRelationService.getOne(queryWrapper);
		runtimeService.deleteProcessInstance(businessRelation.getProcessInstanceGuid(), deleteReason);
		iBusinessRelationService.removeById(businessRelation);
		return R.success("删除成功");
	}

	@Override
	@GetMapping(TASK_VARIABLE)
	public R<Object> taskVariable(String taskId, String variableName) {
		if (Func.isEmpty(variableName)) {
			return R.data(taskService.getVariables(taskId));
		} else {
			return R.data(taskService.getVariable(taskId, variableName));
		}

	}

	@Override
	@GetMapping(TASK_VARIABLES)
	public R<Map<String, Object>> taskVariables(String taskId) {
		return R.data(taskService.getVariables(taskId));
	}


	@Override
	@PostMapping(START_PROCESS_INSTANCE_BY_KEY)
	public R<AllcoreFlow> startProcessInstanceByKey(String processDefinitionKey, String businessKey, @RequestBody Map<String, Object> variables) {
		// 设置流程启动用户
		identityService.setAuthenticatedUserId(TaskUtil.getTaskUser());
		// 开启流程
		ProcessInstance processInstance = runtimeService.startProcessInstanceByKey(processDefinitionKey, businessKey, variables);
		// 组装流程通用类
		AllcoreFlow flow = new AllcoreFlow();
		flow.setProcessInstanceId(processInstance.getId());
		return R.data(flow);
	}


	@Override
	@GetMapping(GET_PROCESS_DEFINITION_ID)
	public R<AllcoreFlow> getProcessDefinitionId(String key, Integer mode, String tenantId) {
		ProcessDefinitionQuery processDefinitionQuery = repositoryService.createProcessDefinitionQuery().latestVersion().orderByProcessDefinitionKey().asc();
		// 通用流程
		if (mode == FlowModeEnum.COMMON.getMode()) {
			processDefinitionQuery.processDefinitionWithoutTenantId();
		}
		// 定制流程
		if (Func.isNotEmpty(tenantId)) {
			processDefinitionQuery.processDefinitionTenantId(tenantId);
		}
		if (StringUtils.isNotEmpty(key)) {
			processDefinitionQuery.processDefinitionKey(key);
		}
		List<ProcessDefinition> processDefinitionList = processDefinitionQuery.list();
		AllcoreFlow allcoreFlow = new AllcoreFlow();
		if (processDefinitionList.size() != 0) {
			allcoreFlow.setProcessDefinitionId(processDefinitionList.get(0).getId());
		}
		return R.data(allcoreFlow);
	}


	@Override
	@GetMapping(GET_TASK_NAME)
	public R<AllcoreFlow> getTaskName(String tableGuid) {
		AllcoreFlow allcoreFlow = new AllcoreFlow();
		QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("table_guid", tableGuid);
		BusinessRelation businessRelation = iBusinessRelationService.getOne(queryWrapper);
		String processInstanceId = businessRelation.getProcessInstanceGuid();
		String taskName = "";
		//orderByHistoricTaskInstanceEndTime
		String description = "";
		List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceStartTime().desc().list();
		if (Func.isNotEmpty(historyTasks)) {
			HistoricTaskInstance historyTask = historyTasks.get(0);
			taskName = historyTask.getName();
			description = historyTask.getDescription();
		}
		allcoreFlow.setTaskName(taskName);
		allcoreFlow.setDescription(description);
		return R.data(allcoreFlow);
	}

	@Override
	@GetMapping(GET_TODO_LIST)
	public R<List<AllcoreFlow>> getToDoList(String tableName) {
		// 通用流程等待签收的任务
		String taskGroup = TaskUtil.getCandidateGroup();
		//.taskCandidateGroupIn(Func.toStrList(taskGroup))
		TaskQuery claimRoleWithoutTenantIdQuery = taskService.createTaskQuery().taskWithoutTenantId().taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();

		List<String> listClaim = claimRoleWithoutTenantIdQuery.list().stream().map(Task::getProcessInstanceId).collect(Collectors.toList());
		List<AllcoreFlow> list = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(listClaim)) {
			QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper<>();
			queryWrapper.eq("table_name", tableName);
			queryWrapper.in("process_instance_guid", listClaim);
			List<BusinessRelation> businessRelationList = iBusinessRelationService.list(queryWrapper);
			businessRelationList.forEach(e -> {
				AllcoreFlow allcoreFlow = new AllcoreFlow();
				allcoreFlow.setTableGuid(e.getTableGuid());
				list.add(allcoreFlow);
			});
		}
		return R.data(list);
	}




	@Override
	public R<List<AllcoreFlow>> getToDoListAll() {
		// 通用流程等待签收的任务
		String taskGroup = TaskUtil.getCandidateGroup();
		//.taskCandidateGroupIn(Func.toStrList(taskGroup))
		TaskQuery claimRoleWithoutTenantIdQuery = taskService.createTaskQuery().taskWithoutTenantId().taskCandidateGroupIn(Func.toStrList(taskGroup))
			.includeProcessVariables().active().orderByTaskCreateTime().desc();

		List<String> listClaim = claimRoleWithoutTenantIdQuery.list().stream().map(Task::getProcessInstanceId).collect(Collectors.toList());
		List<AllcoreFlow> list = new ArrayList<>();
		if (CollectionUtil.isNotEmpty(listClaim)) {
			QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper<>();
			queryWrapper.in("process_instance_guid", listClaim);
			List<BusinessRelation> businessRelationList = iBusinessRelationService.list(queryWrapper);
			businessRelationList.forEach(e -> {
				AllcoreFlow allcoreFlow = new AllcoreFlow();
				allcoreFlow.setTableGuid(e.getTableGuid());
				allcoreFlow.setTaskName(e.getTableName());
				list.add(allcoreFlow);
			});
		}
		return R.data(list);
	}

	@Override
	@GetMapping(GET_HISTORY_DETAIL)
	public R<AllcoreFlow> getWorkOrderDetail(String tableGuid) {
		AllcoreFlow allcoreFlow = new AllcoreFlow();
		QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("table_guid", tableGuid);
		BusinessRelation businessRelation = iBusinessRelationService.getOne(queryWrapper);
		String processInstanceId = businessRelation.getProcessInstanceGuid();
		Date planPermitTime = new Date();
		String name = "";
		List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceStartTime().desc().list();
		if (Func.isNotEmpty(historyTasks)) {
			for(HistoricTaskInstance h :historyTasks){
				//开票的时间
				if("5".equals(h.getDescription())){
					planPermitTime = h.getEndTime();
					name = TaskUtil.getUserId(h.getAssignee()).toString();
				}
			}
		}
		allcoreFlow.setCreateTime(planPermitTime);
		allcoreFlow.setAssignee(name);
		return R.data(allcoreFlow);
	}

	@Override
	@GetMapping(GET_HISTORY_DETAIL_BAK)
	public R<List<AllcoreFlow>> getWorkOrderDetailBak(String tableGuid) {
		List<AllcoreFlow> allcoreFlowList = new ArrayList<>();
		QueryWrapper<BusinessRelation> queryWrapper = new QueryWrapper<>();
		queryWrapper.eq("table_guid", tableGuid);
		BusinessRelation businessRelation = iBusinessRelationService.getOne(queryWrapper);
		String processInstanceId = businessRelation.getProcessInstanceGuid();
		List<HistoricTaskInstance> historyTasks = historyService.createHistoricTaskInstanceQuery().processInstanceId(processInstanceId).orderByHistoricTaskInstanceStartTime().desc().list();
		if (Func.isNotEmpty(historyTasks)) {
			for(HistoricTaskInstance historicTaskInstance :historyTasks){
				AllcoreFlow flow = new AllcoreFlow();
				flow.setTaskId(historicTaskInstance.getId());
				flow.setTaskDefinitionKey(historicTaskInstance.getTaskDefinitionKey());
				flow.setTaskName(historicTaskInstance.getName());
				flow.setDescription(historicTaskInstance.getDescription());
				flow.setAssignee(TaskUtil.getUserId(historicTaskInstance.getAssignee()).toString());
				flow.setCreateTime(historicTaskInstance.getCreateTime());
				flow.setExecutionId(historicTaskInstance.getExecutionId());
				flow.setHistoryTaskEndTime(historicTaskInstance.getEndTime());
				flow.setVariables(historicTaskInstance.getProcessVariables());
				allcoreFlowList.add(flow);
			}
		}
		return R.data(allcoreFlowList);
	}


}
