<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.business.mapper.FlowConfigMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="flowConfigResultMap" type="com.allcore.core.vo.FlowConfigVO">
        <result column="id" property="id"/>
<!--        <result column="create_user" property="createUser"/>-->
<!--        <result column="create_time" property="createTime"/>-->
<!--        <result column="update_user" property="updateUser"/>-->
<!--        <result column="update_time" property="updateTime"/>-->
<!--        <result column="create_dept" property="createDept"/>-->
<!--        <result column="is_deleted" property="isDeleted"/>-->
        <result column="status" property="status"/>
        <result column="bus_code" property="busCode"/>
        <result column="bus_name" property="busName"/>
        <result column="process_definition_guid" property="processDefinitionGuid"/>
        <result column="processName" property="processName"/>
    </resultMap>


    <select id="selectFlowConfigPage" resultMap="flowConfigResultMap">
        select a.status,
               a.id,
               a.bus_code,
               a.bus_name,
               a.process_definition_guid,
               b.NAME_ as processName
               from SYS_FLOW_CONFIG a,ACT_RE_PROCDEF b where a.is_deleted = 0 and a.process_definition_guid = b.ID_
                <if test="dto.busCode!=null and dto.busCode!=''">
                    and a.bus_code like concat('%',#{dto.busCode},'%')
                </if>
                <if test="dto.busName!=null and dto.busName!=''">
                    and a.bus_name like concat('%',#{dto.busName},'%')
                </if>
    </select>

</mapper>
