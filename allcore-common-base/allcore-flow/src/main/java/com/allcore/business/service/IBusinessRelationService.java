
package com.allcore.business.service;

import com.allcore.core.entity.AllcoreFlow;
import com.allcore.core.entity.BusinessRelation;
import com.allcore.core.mp.base.BaseService;
import com.allcore.core.tool.api.R;

/**
 *  服务类
 *
 * <AUTHOR>
 * @since 2022-04-01
 */
public interface IBusinessRelationService extends BaseService<BusinessRelation> {


	/**
	 * （待办事务列表页）
	 * <AUTHOR>
	 * @date 2022/09/14 13:45
	 * @param allcoreFlow
	 * @return com.allcore.core.tool.api.R
	 */
	R todoBusiness(AllcoreFlow allcoreFlow);
}
