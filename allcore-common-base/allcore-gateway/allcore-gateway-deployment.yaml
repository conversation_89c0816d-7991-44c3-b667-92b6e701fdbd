kind: Deployment
apiVersion: apps/v1
metadata:
  name: allcore-gateway
  labels:
    app: allcore-gateway
spec:
  replicas: 1
  selector:
    matchLabels:
      app: allcore-gateway
  template:
    metadata:
      labels:
        app: allcore-gateway
    spec:
      volumes:
        - name: host-time
          hostPath:
            path: /etc/localtime
            type: ''
      containers:
        - name: allcore-gateway
          image: 'allcore.io/allcore/allcore-gateway:1.0.0.RELEASE'
          ports:
            - name: tcp-18080
              containerPort: 18080
              protocol: TCP
          env:
            - name: NACOS_CONFIG
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_CONFIG
            - name: NACOS_DISCOVERY
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_DISCOVERY
            - name: NACOS_NAMESPACE
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: NACOS_NAMESPACE
            - name: SPRING_PROFILES_ACTIVE
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: SPRING_PROFILES_ACTIVE
            - name: TZ
              valueFrom:
                configMapKeyRef:
                  name: common-conf
                  key: TZ
          resources: {}
          volumeMounts:
            - name: host-time
              readOnly: true
              mountPath: /etc/localtime
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          imagePullPolicy: Always
      restartPolicy: Always
      terminationGracePeriodSeconds: 30
      dnsPolicy: ClusterFirstWithHostNet
      hostNetwork: true
      serviceAccountName: default
      serviceAccount: default
      securityContext: {}
      imagePullSecrets:
        - name: harbor-repo
      schedulerName: default-scheduler
