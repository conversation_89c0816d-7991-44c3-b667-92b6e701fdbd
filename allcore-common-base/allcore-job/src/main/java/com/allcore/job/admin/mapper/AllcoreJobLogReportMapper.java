package com.allcore.job.admin.mapper;

import com.allcore.job.core.entity.AllcoreJobLogReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Mapper
public interface AllcoreJobLogReportMapper extends BaseMapper<AllcoreJobLogReport> {

    public int save(AllcoreJobLogReport logReport);

    public int update(AllcoreJobLogReport logReport);

    public List<AllcoreJobLogReport> queryLogReport(@Param("startDate") String startDate,
                                                    @Param("endDate") String endDate);

    public AllcoreJobLogReport queryLogReportTotal();

}
