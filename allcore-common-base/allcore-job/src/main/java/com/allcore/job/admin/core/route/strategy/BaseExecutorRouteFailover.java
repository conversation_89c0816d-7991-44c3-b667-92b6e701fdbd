package com.allcore.job.admin.core.route.strategy;

import com.allcore.core.tool.api.R;
import com.allcore.core.tool.api.ResultCode;
import com.allcore.job.admin.core.route.BaseExecutorRouter;
import com.allcore.job.admin.core.scheduler.AllcoreJobScheduler;
import com.allcore.job.admin.core.util.I18nUtil;
import com.allcore.job.core.biz.ExecutorBiz;
import com.allcore.job.core.biz.model.TriggerParam;

import java.util.List;

/**
* @author: ldh
* @date: 2023/3/17 10:04
* @description:
*/
public class BaseExecutorRouteFailover extends BaseExecutorRouter {

    @Override
    public R<String> route(TriggerParam triggerParam, List<String> addressList) {

        StringBuffer beatResultS = new StringBuffer();
        for (String address : addressList) {
            // beat
            R<String> beatResult = null;
            try {
                ExecutorBiz executorBiz = AllcoreJobScheduler.getExecutorBiz(address);
                beatResult = executorBiz.beat();
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                beatResult = R.fail( ""+e );
            }
            beatResultS.append( (beatResultS.length()>0)?"<br><br>":"")
                    .append(I18nUtil.getString("jobconf_beat") + "：")
                    .append("<br>address：").append(address)
                    .append("<br>code：").append(beatResult.getCode())
                    .append("<br>msg：").append(beatResult.getMsg());

            // beat success
            if (beatResult.getCode() == ResultCode.SUCCESS.getCode()) {

                beatResult.setMsg(beatResultS.toString());
                beatResult.setData(address);
                return beatResult;
            }
        }
        return R.data(beatResultS.toString());

    }
}
