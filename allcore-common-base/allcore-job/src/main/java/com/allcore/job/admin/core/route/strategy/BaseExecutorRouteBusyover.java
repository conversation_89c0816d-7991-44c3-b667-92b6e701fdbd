package com.allcore.job.admin.core.route.strategy;

import com.allcore.core.tool.api.R;
import com.allcore.core.tool.api.ResultCode;
import com.allcore.job.admin.core.route.BaseExecutorRouter;
import com.allcore.job.admin.core.scheduler.AllcoreJobScheduler;
import com.allcore.job.admin.core.util.I18nUtil;
import com.allcore.job.core.biz.ExecutorBiz;
import com.allcore.job.core.biz.model.IdleBeatParam;
import com.allcore.job.core.biz.model.TriggerParam;

import java.util.List;

/**
* @author: ldh
* @date: 2023/3/17 9:58
* @description:
*/
public class BaseExecutorRouteBusyover extends BaseExecutorRouter {

    @Override
    public R<String> route(TriggerParam triggerParam, List<String> addressList) {
        StringBuffer idleBeatRs = new StringBuffer();
        for (String address : addressList) {
            // beat
            R<String> idleBeatResult = null;
            try {
                ExecutorBiz executorBiz = AllcoreJobScheduler.getExecutorBiz(address);
                idleBeatResult = executorBiz.idleBeat(new IdleBeatParam(triggerParam.getJobId()));
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
                idleBeatResult = R.fail( ""+e );
            }
            idleBeatRs.append( (idleBeatRs.length()>0)?"<br><br>":"")
                    .append(I18nUtil.getString("jobconf_idleBeat") + "：")
                    .append("<br>address：").append(address)
                    .append("<br>code：").append(idleBeatResult.getCode())
                    .append("<br>msg：").append(idleBeatResult.getMsg());

            // beat success
            if (idleBeatResult.getCode() == ResultCode.SUCCESS.getCode()) {
                idleBeatResult.setMsg(idleBeatRs.toString());
                idleBeatResult.setData(address);
                return idleBeatResult;
            }
        }

        return R.fail(idleBeatRs.toString());
    }

}
