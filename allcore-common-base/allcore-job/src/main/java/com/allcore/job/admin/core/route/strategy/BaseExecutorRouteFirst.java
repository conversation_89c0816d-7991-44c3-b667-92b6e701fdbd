package com.allcore.job.admin.core.route.strategy;

import com.allcore.core.tool.api.R;
import com.allcore.job.admin.core.route.BaseExecutorRouter;
import com.allcore.job.core.biz.model.TriggerParam;

import java.util.List;

/**
* @author: ldh
* @date: 2023/3/20 11:19
* @description:
*/
public class BaseExecutorRouteFirst extends BaseExecutorRouter {

    @Override
    public R<String> route(TriggerParam triggerParam, List<String> addressList){
        return R.data(addressList.get(0));
    }

}
