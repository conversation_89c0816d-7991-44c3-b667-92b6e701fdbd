package com.allcore.job.admin.mapper;

import com.allcore.job.core.entity.AllcoreJobLog;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <p>
 *  Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-02-28
 */
@Mapper
public interface AllcoreJobLogMapper extends BaseMapper<AllcoreJobLog> {
    // exist jobId not use jobGroup, not exist use jobGroup
    public List<AllcoreJobLog> pageList(@Param("offset") int offset,
                                        @Param("pagesize") int pagesize,
                                        @Param("jobGroup") int jobGroup,
                                        @Param("jobId") int jobId,
                                        @Param("triggerTimeStart") Date triggerTimeStart,
                                        @Param("triggerTimeEnd") Date triggerTimeEnd,
                                        @Param("logStatus") int logStatus);
    public int pageListCount(@Param("offset") int offset,
                             @Param("pagesize") int pagesize,
                             @Param("jobGroup") int jobGroup,
                             @Param("jobId") int jobId,
                             @Param("triggerTimeStart") Date triggerTimeStart,
                             @Param("triggerTimeEnd") Date triggerTimeEnd,
                             @Param("logStatus") int logStatus);

    public AllcoreJobLog load(@Param("id") long id);

    public long save(AllcoreJobLog jobLog);

    public int updateTriggerInfo(AllcoreJobLog jobLog);

    public int updateHandleInfo(AllcoreJobLog jobLog);

    public int delete(@Param("jobId") int jobId);

    public Map<String, Object> findLogReport(@Param("from") Date from,
                                             @Param("to") Date to);

    public List<Long> findClearLogIds(@Param("jobGroup") int jobGroup,
                                      @Param("jobId") int jobId,
                                      @Param("clearBeforeTime") Date clearBeforeTime,
                                      @Param("clearBeforeNum") int clearBeforeNum,
                                      @Param("pagesize") int pagesize);
    public int clearLog(@Param("logIds") List<Long> logIds);

    public List<Long> findFailJobLogIds(@Param("pagesize") int pagesize);

    public int updateAlarmStatus(@Param("logId") long logId,
                                 @Param("oldAlarmStatus") int oldAlarmStatus,
                                 @Param("newAlarmStatus") int newAlarmStatus);

    public List<Long> findLostJobIds(@Param("losedTime") Date losedTime);

}
