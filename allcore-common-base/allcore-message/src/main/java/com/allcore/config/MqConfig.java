package com.allcore.config;

import com.allcore.listener.MqListener;
import com.allcore.msg.MqMsg;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import com.allcore.core.tool.utils.SpringUtil;
import org.springframework.amqp.core.*;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.amqp.rabbit.connection.CachingConnectionFactory;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.amqp.support.converter.Jackson2JsonMessageConverter;
import org.springframework.amqp.support.converter.MessageConverter;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * RabbitMQ配置
 * <AUTHOR>
 */
@Slf4j
@Configuration
public class MqConfig {

	@Bean
	public RabbitTemplate rabbitTemplate(CachingConnectionFactory connectionFactory) {
		connectionFactory.setPublisherConfirms(true);
		connectionFactory.setPublisherReturns(true);
		RabbitTemplate rabbitTemplate = new RabbitTemplate(connectionFactory);
		rabbitTemplate.setMessageConverter(new Jackson2JsonMessageConverter());
		rabbitTemplate.setMandatory(true);
		rabbitTemplate.setConfirmCallback((correlationData, ack, cause) -> log.info("消息发送成功:correlationData({}),ack({}),cause({})", correlationData, ack, cause));
		rabbitTemplate.setReturnCallback((message, replyCode, replyText, exchange, routingKey) -> log.info("消息丢失:exchange({}),route({}),replyCode({}),replyText({}),message:{}", exchange, routingKey, replyCode, replyText, message));
		return rabbitTemplate;
	}

	@Bean
	public RabbitAdmin rabbitAdmin(CachingConnectionFactory connectionFactory) {
		return new RabbitAdmin(connectionFactory);
	}

	@Bean
	public MessageConverter jsonMessageConverter(ObjectMapper objectMapper) {
		return new Jackson2JsonMessageConverter(objectMapper);
	}

	/**
	 * 点对点消息队列
	 * 默认交换机
	 */
	public static final String QUEUE_RANCH_ONE_TO_ONE="queue.ranch.one.to.one";
	/**
	 * 发送一对多(广播)消息队列
	 */
	public static final String QUEUE_RANCH_ONE_TO_ALL="queue.ranch.one.to.all";

	/**
	 * 延时消息队列
	 */
	public static final String QUEUE_RANCH_DELAY="queue.ranch.delay";

	/**
	 * 广播交换机
	 */
	public static final String EXCHANGE_FANOUT_RANCH ="exchange.fanout.ranch";

	/**
	 * 延时交换机
	 */
	public static final String EXCHANGE_DELAY_RANCH="exchange.delay.ranch";


	@Bean
	public Queue oneToOneQueue() {return new Queue(QUEUE_RANCH_ONE_TO_ONE, true,false,false);}

	@Bean
	public Queue oneToAllQueue(){return new Queue(QUEUE_RANCH_ONE_TO_ALL, true,false,false);}

	@Bean
	public Queue delayQueue(){return new Queue(QUEUE_RANCH_DELAY, true,false,false);}

	/**
	 * 广播交换机
	 * @return
	 */
	@Bean
	public FanoutExchange fanoutExchange(){
		return new FanoutExchange(EXCHANGE_FANOUT_RANCH);
	}

	/**
	 * 延时交换机
	 * @return
	 */
    @Bean
    public CustomExchange delayExchange(){
        Map<String, Object> args = new HashMap<>();
        args.put("x-delayed-type", "direct");
        return new CustomExchange(EXCHANGE_DELAY_RANCH,"x-delayed-message",true, false,args);
    }

	/**
	 * 广播交换机绑定
	 * @param oneToAllQueue
	 * @param fanoutExchange
	 * @return
	 */
	@Bean
	public Binding fanoutBuild(Queue oneToAllQueue, FanoutExchange fanoutExchange){
		return BindingBuilder.bind(oneToAllQueue).to(fanoutExchange);
	}

	/**
	 * 延时交换机绑定
	 * @param delayQueue
	 * @param delayExchange
	 * @return
	 */
    @Bean
    public Binding delayBuild(Queue delayQueue, CustomExchange delayExchange){
        return BindingBuilder.bind(delayQueue).to(delayExchange).with(QUEUE_RANCH_DELAY).noargs();
    }


	/**
	 * 统一监听
	 * @param msg
	 */
	@RabbitHandler
	@RabbitListener(queues = {QUEUE_RANCH_ONE_TO_ONE,QUEUE_RANCH_ONE_TO_ALL})
	private void news(MqMsg<?> msg, Message message, Channel channel) {
		final long deliveryTag = message.getMessageProperties().getDeliveryTag();
		MqListener listener = SpringUtil.getBean(msg.getListener());
		try {
			log.info("消息消费成功~~~~~~~~~");
			CachedThreadPool.execute(new MqListenerThread(listener,msg.getData()));
			// 通知 MQ 消息已被成功消费,可以ACK了
			channel.basicAck(deliveryTag, false);
		} catch (Exception e) {
			log.error("【消息队列监听者业务异常】位于：{},错误信息：{}",listener.getClass(),e.getMessage(),e);
			listener.onError(msg.getData(),e);
			try {
				// 处理失败,重新压入MQ
				channel.basicRecover();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
	}
}
