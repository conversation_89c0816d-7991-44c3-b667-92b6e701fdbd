package com.allcore.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * 消息类型表实体类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Data
@TableName("msg_message_type")
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "MessageType对象", description = "消息类型表")
public class MessageType extends Model<MessageType> {

	private static final long serialVersionUID = 1L;

		/**
		* 消息类型（工单，计划，缺陷...）
		*/
		@ApiModelProperty(value = "消息类型（工单，计划，缺陷...）")
		private String messageType;

		@JsonSerialize(
			using = ToStringSerializer.class
		)
		@ApiModelProperty("主键id")
		@TableId(
			value = "id",
			type = IdType.ASSIGN_UUID
		)
		private String id;
		@JsonSerialize(
			using = ToStringSerializer.class
		)
		@ApiModelProperty("创建人")
		private String createUser;
		@DateTimeFormat(
			pattern = "yyyy-MM-dd HH:mm:ss"
		)
		@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss"
		)
		@ApiModelProperty("创建时间")
		private Date createTime;
		@JsonSerialize(
			using = ToStringSerializer.class
		)
		@ApiModelProperty("更新人")
		private String updateUser;
		@DateTimeFormat(
			pattern = "yyyy-MM-dd HH:mm:ss"
		)
		@JsonFormat(
			pattern = "yyyy-MM-dd HH:mm:ss"
		)
		@ApiModelProperty("更新时间")
		private Date updateTime;

		@TableLogic
		@ApiModelProperty("是否已删除")
		private Integer isDeleted;


}
