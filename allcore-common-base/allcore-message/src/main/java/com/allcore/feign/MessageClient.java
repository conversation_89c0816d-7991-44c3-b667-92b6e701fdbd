package com.allcore.feign;

import cn.hutool.core.thread.GlobalThreadPool;
import com.allcore.entity.MessageInfo;
import com.allcore.entity.MessageSend;
import com.allcore.entity.MessageStruct;
import com.allcore.entity.MessageTypeEnum;
import com.allcore.listener.TestListener;
import com.allcore.service.IMessageInfoService;
import com.allcore.service.IMessageSendService;
import com.allcore.tool.MqTool;
import com.allcore.websocket.WebSocket;
import lombok.AllArgsConstructor;
import com.allcore.core.secure.utils.AuthUtil;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.core.tool.utils.Func;
import com.allcore.core.tool.utils.ObjectUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.system.cache.SysCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserSearchClient;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.core.RabbitAdmin;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.time.Duration;
import java.util.List;
import java.util.Properties;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@ApiIgnore()
@RestController
@AllArgsConstructor
public class MessageClient implements IMessageClient{

	private final MqTool mqTool;
	private final WebSocket webSocket;

	private final IMessageInfoService messageInfoService;

	private final IMessageSendService messageSendService;

	private final RabbitAdmin rabbitAdmin;

	private final IUserSearchClient userSearchClient;

	@Override
	public R sendMsg(MessageStruct msg) {
		mqTool.sendMsg(msg, TestListener.class);
		return R.status(true);
	}

	@Override
	public R sendMsgToAll(MessageStruct msg) {
		mqTool.sendMsgToAll(msg, TestListener.class);
		return R.status(true);
	}

	@Override
	public R sendDelayMsg(MessageStruct msg) {
		mqTool.sendDelayMsg(msg, TestListener.class, Duration.ZERO);
		return R.status(true);
	}

	@Override
	public R sendNotice(String param) {
		webSocket.sendToAll(param);
		return R.status(true);
	}

	@Override
	public R sendToMoreNotice(List<String> userIds,String param) {
		webSocket.sendToMore(userIds,param);
		return R.status(true);
	}

	@Override
	public R saveMessageInfo(MessageInfo messageInfo) {
		return R.status(messageInfoService.save(messageInfo));
	}

	@Override
	public R saveMessageSend(MessageSend messageSend) {
		return R.status(messageSendService.save(messageSend));
	}


	@Override
	public R createCustomQueue(String queueName, String message) {
		createMQIfNotExist(queueName,queueName);
		mqTool.sendCustomQueueMsg(queueName,message);
		return R.success("消息已发送到 "+queueName+" 队列。");
	}

	@Override
	public R packageMsginfoToDb(String msg) {
		//封装通知消息入库
		GlobalThreadPool.execute(()->{
			//获取子部门集合
			List<String> deptChildIds = SysCache.getDeptChildIds(AuthUtil.getDeptId());
			//根据单位id查询子集用户
			R<List<User>> listR = userSearchClient.listByDept(Func.join(deptChildIds));
			//群发
			//messageClient.sendNotice(param);
			List<String> userIds = null;
			if (listR.isSuccess() && ObjectUtil.isNotEmpty(listR.getData())) {
				userIds = listR.getData().stream().map(User::getId).collect(Collectors.toList());

				//测试用，加上推送给本人
//			    userIds.add(AuthUtil.getUserId());
				//1:n 通知
//				messageClient.sendToMoreNotice(userIds, param);

				//发送消息入库
				//封装消息
				MessageInfo messageInfo = new MessageInfo();
				String uuid = StringUtil.randomUUID();
				messageInfo.setId(uuid);
				messageInfo.setMessageBody(msg);
				messageInfo.setCreateUser(AuthUtil.getUserId());
				messageInfo.setCreateTime(DateUtil.now());
				messageInfoService.save(messageInfo);

				//封装发送消息
				if (ObjectUtil.isNotEmpty(userIds)) {
					String createUser = AuthUtil.getUserId();
					userIds.forEach(id -> {
						MessageSend messageSend = new MessageSend();
						messageSend.setMessageId(uuid);
						messageSend.setMessageTypeId(MessageTypeEnum.NOTICE.getType());
						messageSend.setCreateUser(createUser);
						messageSend.setCreateTime(DateUtil.now());
						messageSend.setUserId(id);
						messageSendService.save(messageSend);
					});
				}

			}
		});
		return R.status(true);
	}

	private void createMQIfNotExist(String queueName ,String exchangeName) {
		//判断队列是否存在
		Properties properties = rabbitAdmin.getQueueProperties(queueName);
		if(properties == null){
			Queue queue = new Queue(queueName, true, false, false, null);
			FanoutExchange fanoutExchange = new FanoutExchange(exchangeName);
			rabbitAdmin.declareQueue(queue);
			rabbitAdmin.declareExchange(fanoutExchange);
			rabbitAdmin.declareBinding(BindingBuilder.bind(queue).to(fanoutExchange));
		}
	}
}
