package com.allcore.service.impl;

import com.allcore.entity.MessageType;
import com.allcore.mapper.MessageTypeMapper;
import com.allcore.service.IMessageTypeService;
import com.allcore.vo.MessageTypeVO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 消息类型表 服务实现类
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
@Service
public class MessageTypeServiceImpl extends ServiceImpl<MessageTypeMapper, MessageType> implements IMessageTypeService {

	@Override
	public IPage<MessageTypeVO> selectMessageTypePage(IPage<MessageTypeVO> page, MessageTypeVO messageType) {
		return page.setRecords(baseMapper.selectMessageTypePage(page, messageType));
	}

}
