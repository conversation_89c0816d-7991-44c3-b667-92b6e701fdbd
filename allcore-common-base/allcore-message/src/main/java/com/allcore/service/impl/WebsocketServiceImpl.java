package com.allcore.service.impl;

import com.allcore.service.IWebsocketService;
import com.allcore.core.tool.utils.ObjectUtil;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.core.tool.utils.WebUtil;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;

/**
 * @Description websocket业务逻辑
 * <AUTHOR>
 * @Date 2022/12/14 17:37
 */
@Service
public class WebsocketServiceImpl implements IWebsocketService {
	@Override
	public String getFormTypeParam() {
		HttpServletRequest request = WebUtil.getRequest();
		return (ObjectUtil.isEmpty(request)||StringUtil.isBlank(request.getParameter("fromType")))?"web":request.getParameter("fromType");
	}
}
