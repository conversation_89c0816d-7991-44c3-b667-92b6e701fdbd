<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.mapper.MessageInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="messageInfoResultMap" type="com.allcore.entity.MessageInfo">
        <result column="id" property="id"/>
        <result column="create_user" property="createUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_time" property="updateTime"/>
        <result column="is_deleted" property="isDeleted"/>
        <result column="message_body" property="messageBody"/>
    </resultMap>

    <sql id="Base_Column_List" >
        id, message_body, create_user, create_time, update_user, update_time, is_deleted
    </sql>

    <select id="selectMessageInfoPage" resultMap="messageInfoResultMap">
        select <include refid="Base_Column_List"/>
        from msg_message_info where is_deleted = 0
    </select>

</mapper>
