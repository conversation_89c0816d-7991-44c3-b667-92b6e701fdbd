package com.allcore.mapper;

import com.allcore.entity.MessageInfo;
import com.allcore.vo.MessageInfoVO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * 消息表 Mapper 接口
 *
 * <AUTHOR>
 * @since 2022-08-08
 */
public interface MessageInfoMapper extends BaseMapper<MessageInfo> {

	/**
	 * 自定义分页
	 *
	 * @param page
	 * @param messageInfo
	 * @return
	 */
	List<MessageInfoVO> selectMessageInfoPage(IPage page, MessageInfoVO messageInfo);

}
