#服务器端口
server:
  port: 18083

#数据源配置
spring:
  datasource:
    url: ${allcore.datasource.dev.url}
    username: ${allcore.datasource.dev.username}
    password: ${allcore.datasource.dev.password}
    druid:
      time-between-eviction-runs-millis: 300000
allcore:
  log:
    request:
      enabled: false
  mybatis-plus:
    sql-log: false

#配置文件ENC加密
#jasypt:
#  encryptor:
#    bean: allcoreStringEncryptor