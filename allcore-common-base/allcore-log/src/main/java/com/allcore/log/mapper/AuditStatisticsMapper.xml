<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.allcore.log.mapper.AuditStatisticsMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="logResultMap" type="com.allcore.core.log.model.AuditStatisticsVo">
        <result column="loginName" property="loginName"/>
        <result column="unitName" property="unitName"/>
        <result column="eventType" property="eventType"/>
        <result column="eventCount" property="eventCount"/>
    </resultMap>

    <select id="auditStatisticsList" resultMap="logResultMap" parameterType="java.util.HashMap">
        SELECT l.create_by loginName,d.dept_name unitName,l.log_id eventType,COUNT(l.id) eventCount FROM sys_log_usual l
        LEFT JOIN sys_dept d ON l.create_dept = d.id
        <where>
            l.create_by IS not NULL AND l.create_by !=''
            <if test="log.tenantId != null and log.tenantId !=''">
                and l.tenant_id = #{log.tenantId}
            </if>
            <if test="log.loginName != null and log.loginName !=''">
                and l.create_by like  concat('%',#{log.loginName},'%')
            </if>
            <if test="log.system != null and log.system.size() > 0" >
                and service_id in
                <foreach collection="log.system" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="log.business != null and log.business.size() > 0" >
                and service_id not in
                <foreach collection="log.business" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="log.eventType != null and log.eventType !=''">
                and l.log_id = #{log.eventType}
            </if>
            <if test="log.logStartTime != null and log.logStartTime !=''">
                AND l.create_time <![CDATA[ >= ]]> #{log.logStartTime}
            </if>
            <if test="log.logEndTime != null and log.logEndTime !=''">
                AND l.create_time <![CDATA[ <= ]]> #{log.logEndTime}
            </if>
        </where>
        GROUP BY l.create_by,dept_name,l.log_id
        ORDER BY eventCount DESC
    </select>
    <select id="queryEventTypeList" resultType="java.lang.String">
        select distinct log_id
        from sys_log_usual
        where  log_id IS NOT NULL AND log_id !=''
    </select>
</mapper>
