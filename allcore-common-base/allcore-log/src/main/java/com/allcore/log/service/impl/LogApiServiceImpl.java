package com.allcore.log.service.impl;

import com.allcore.core.log.model.LogApi;
import com.allcore.log.mapper.LogApiMapper;
import com.allcore.log.service.ILogApiService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

/**
 * 服务实现类
 *
 * <AUTHOR>
 */
@Service
public class LogApiServiceImpl extends ServiceImpl<LogApiMapper, LogApi> implements ILogApiService {


}
