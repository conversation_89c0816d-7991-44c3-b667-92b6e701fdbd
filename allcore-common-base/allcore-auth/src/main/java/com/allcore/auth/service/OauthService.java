package com.allcore.auth.service;

import java.util.Map;

/**
 * （认证接口重写service）
 * <AUTHOR>
 * @date 2022/09/08 14:08
 */
public interface OauthService {

	/**
	 * （更新在线状态）
	 * <AUTHOR>
	 * @date 2022/09/02 10:56
	 * @param loginStatus
	 * @param account
	 * @param tenantId
	 * @return boolean
	 */
	boolean updateUserLoginInfo(String loginStatus,String account,String tenantId,String fromType);

	/**
	 * （校验登录管理配置）
	 * <AUTHOR>
	 * @date 2022/09/02 10:59
	 * @param parameters
	 * @return void
	 */
	void validateLoginManage(Map<String, String> parameters,String tenantId);

	/**
	 * （更新在线状态和code）
	 * <AUTHOR>
	 * @date 2022/09/20 09:16
	 * @param loginStatus
	 * @param account
	 * @param tenantId
	 * @param appCode
	 * @param fromType
	 * @return boolean
	 */
	boolean updateUserLoginInfoAndCode(String loginStatus,String account,String tenantId, String appCode,String fromType);
}
