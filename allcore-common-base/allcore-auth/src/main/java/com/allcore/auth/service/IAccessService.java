package com.allcore.auth.service;

import com.allcore.core.tool.api.R;

/**
 * @program: bl
 * @description: 权限对接服务类
 * @author: fanxiang
 * @create: 2025-06-14 14:15
 **/

public interface IAccessService {

    R<String> getPublicKey();

    boolean validateNrUser(String username, String password);

    R<Integer> checkUserAccount(String username,String tenantId);

    boolean validateLoginByToken(String tokenName,String token);

    R getNrUsers();

}
