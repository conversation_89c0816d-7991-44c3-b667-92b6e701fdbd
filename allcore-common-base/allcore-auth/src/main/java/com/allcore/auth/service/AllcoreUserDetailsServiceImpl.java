package com.allcore.auth.service;

import com.allcore.auth.constant.AuthConstant;
import com.allcore.auth.utils.IscHttpClientUtil;
import com.allcore.auth.utils.TokenUtil;
import com.allcore.common.cache.CacheNames;
import com.allcore.common.config.IndexConfig;
import com.allcore.common.constant.CommonConstant;
import com.allcore.core.jwt.JwtUtil;
import com.allcore.core.jwt.props.JwtProperties;
import com.allcore.core.log.auditlog.AuditLogPublisher;
import com.allcore.core.log.auditlog.DealTypeUtil;
import com.allcore.core.log.auditlog.RiskLevelEnum;
import com.allcore.core.log.model.LogUsual;
import com.allcore.core.redis.cache.AllcoreRedis;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.*;
import com.allcore.system.cache.ParamCache;
import com.allcore.system.entity.Dept;
import com.allcore.system.entity.Param;
import com.allcore.system.entity.Region;
import com.allcore.system.entity.Tenant;
import com.allcore.system.feign.ISysClient;
import com.allcore.user.entity.User;
import com.allcore.user.entity.UserInfo;
import com.allcore.user.enums.UserEnum;
import com.allcore.user.feign.IUserClient;
import io.jsonwebtoken.Claims;
import lombok.AllArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpStatus;
import org.springframework.security.core.authority.AuthorityUtils;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.security.oauth2.common.exceptions.UserDeniedAuthorizationException;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.time.Duration;
import java.util.List;

import static com.allcore.common.constant.LoginManageConstant.ACCOUNT_FAIL_COUNT_KEY;
import static com.allcore.common.constant.LoginManageConstant.LOGIN_ERROR_LOCK_TIME_KEY;

/**
 * 用户信息
 *
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
@Slf4j
public class AllcoreUserDetailsServiceImpl implements UserDetailsService {

	private final IUserClient userClient;
	private final ISysClient sysClient;

	private final AllcoreRedis allcoreRedis;
	private final JwtProperties jwtProperties;

	private final IndexConfig indexConfig;

	@Override
	@SneakyThrows
	public AllcoreUserDetails loadUserByUsername(String username) {
		/**
		 *用户名进行sm解密 如果是sm加密 走sm处理 否则按之前的逻辑处理
		 */
//		String decryptUsername = UserDecryptUtil.nameDecrypt(username);
//		if (!username.equals(decryptUsername)){
//			return handleSm(decryptUsername);
//		}


		HttpServletRequest request = WebUtil.getRequest();

		log.info("登录拦截信息：username:{},request: {}",username,request);

		Param param = sysClient.getParamObjectByKey("platform.authorization.code");
		if (ObjectUtil.isEmpty(param) || StringUtil.isBlank(param.getParamValue())){
			saveLog(request,"平台授权码不存在");
			throw new UserDeniedAuthorizationException("平台授权码不存在");
		}
		if (!DigestUtil.encrypt(param.getId()).equals(param.getParamValue())){
			saveLog(request,"非法平台授权码");
			throw new UserDeniedAuthorizationException("非法平台授权码");
		}

		String passWordEncoder = AuthConstant.ENCRYPT;
		// 获取header的ticket
		String ticketHeader = request.getHeader("ticket");
		String ticketParam = request.getParameter("ticket");
		String fromTypeParam = StringUtil.isBlank(request.getParameter("fromType"))?"web":request.getParameter("fromType");
		if(StringUtil.isNotBlank(ticketHeader)||StringUtil.isNotBlank(ticketParam)){
			passWordEncoder = "{novalidate}";
		}

		// 获取用户绑定ID
		String headerDept = request.getHeader(TokenUtil.DEPT_HEADER_KEY);
		String headerRole = request.getHeader(TokenUtil.ROLE_HEADER_KEY);
		// 获取租户ID
		String headerTenant = request.getHeader(TokenUtil.TENANT_HEADER_KEY);
		String paramTenant = request.getParameter(TokenUtil.TENANT_PARAM_KEY);
		String password = request.getParameter(TokenUtil.PASSWORD_KEY);
		String grantType = request.getParameter(TokenUtil.GRANT_TYPE_KEY);
		// 判断租户请求头
		if (StringUtil.isAllBlank(headerTenant, paramTenant)) {
			saveLog(request,TokenUtil.TENANT_NOT_FOUND);
			throw new UserDeniedAuthorizationException(TokenUtil.TENANT_NOT_FOUND);
		}
		// 判断令牌合法性
		if (!judgeRefreshToken(grantType, request)) {
			saveLog(request,TokenUtil.TOKEN_NOT_PERMISSION);
			throw new UserDeniedAuthorizationException(TokenUtil.TOKEN_NOT_PERMISSION);
		}

		// 指定租户ID
		String tenantId = StringUtil.isBlank(headerTenant) ? paramTenant : headerTenant;
		// 判断登录是否锁定
		int count = getFailCount(tenantId, username);
		int failCount = Func.toInt(ParamCache.getValue(ACCOUNT_FAIL_COUNT_KEY), Integer.parseInt(ParamCache.getValue(LOGIN_ERROR_LOCK_TIME_KEY)));
		if(AuthConstant.ENCRYPT.equals(passWordEncoder)){
			if (count >= failCount) {
				saveLog(request,TokenUtil.USER_HAS_TOO_MANY_FAILS);
				throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_TOO_MANY_FAILS);
			}
		}

		// 获取租户信息
		R<Tenant> tenant = sysClient.getTenantByTenantId(tenantId);
		if (tenant.isSuccess()) {
			if (TokenUtil.judgeTenant(tenant.getData())) {
				saveLog(request,TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
				throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
			}
		} else {
			saveLog(request,TokenUtil.USER_HAS_NO_TENANT);
			throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT);
		}

		// 远程调用返回数据
		R<UserInfo> result;
		// 获取用户类型
		String userType = Func.toStr(request.getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);


		// 根据不同用户类型调用对应的接口返回数据，用户可自行拓展
		if (userType.equals(UserEnum.WEB.getName())) {
			result = userClient.userInfo(tenantId, username, UserEnum.WEB.getName());
		} else if (userType.equals(UserEnum.APP.getName())) {
			result = userClient.userInfo(tenantId, username, UserEnum.APP.getName());
		} else{
			result = userClient.userInfo(tenantId, username, UserEnum.OTHER.getName());
		}
		// 判断是否是南瑞登录
		R<UserInfo> nrResult = userClient.userInfo(tenantId,username,UserEnum.NR.getName());
		if(nrResult.isSuccess()&&Func.isNotEmpty(result.getData())){
			result = nrResult;
		}

		String deptCode = "";
		String deptCategory = "";
		String longitude = "";
		String latitude = "";
		String elevation = "";
		String area = "";
		String areaName = "";

		// 判断返回信息
		if (result.isSuccess()) {
			UserInfo userInfo = result.getData();
			if(Func.isEmpty(userInfo)){
				throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
			}
			User user = userInfo.getUser();

			// 用户不存在,但提示用户名与密码错误并锁定账号
			if (user == null || user.getId() == null) {
				setFailCount(tenantId, username, count);
				saveLog(request,TokenUtil.USER_NOT_FOUND);
				throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
			}

			// 如果是南瑞用户，则赋予系统初始密码
			if(user.getUserType().equals(UserEnum.NR.getCategory())){
				password=DigestUtil.encrypt(AuthConstant.DEFAULT_PASSWORD);
			}

			// 用户存在但密码错误,超过次数则锁定账号
			if(AuthConstant.ENCRYPT.equals(passWordEncoder)){
				if ( Func.isNotEmpty(request.getParameter("grant_type_sub")) && request.getParameter("grant_type_sub").equals("userId")){
					//钉钉内部登录传参中包含grant_type_sub且值为userId,先跳过密码校验（new AllcoreUserDetails时会再次校验）
				} else if (grantType != null && !grantType.equals(TokenUtil.REFRESH_TOKEN_KEY)
						&& !user.getPassword().equals(DigestUtil.hex(password)) && !user.getPassword().equals(password)) {
					setFailCount(tenantId, username, count);
					saveLog(request,TokenUtil.USER_NOT_FOUND);
					throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
				}
			}
			// 用户角色不存在
			if (Func.isEmpty(userInfo.getRoles())) {
				saveLog(request,TokenUtil.USER_HAS_NO_ROLE);
				iscLogOut(user.getId());
				throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_ROLE);
			}
			// 多部门情况下指定单部门
			if (Func.isNotEmpty(headerDept) && user.getDeptId().contains(headerDept)) {
				user.setDeptId(headerDept);
			}
			if (Func.isNotEmpty(user.getDeptId())) {
				R<Dept> dept = sysClient.getDept(user.getDeptId());
				deptCode = dept.getData().getDeptCode();
				deptCategory = String.valueOf(dept.getData().getDeptCategory());
				longitude = dept.getData().getLongitude();
				latitude = dept.getData().getLatitude();
				elevation = dept.getData().getElevation();
				area = dept.getData().getArea();
				R<Region> regionR = sysClient.getRegion(dept.getData().getArea());
				if(regionR.isSuccess()&& StringUtil.isNotBlank(regionR.getData().getName())){
					areaName = regionR.getData().getName();
				}
			}
			// 多角色情况下指定单角色
			if (Func.isNotEmpty(headerRole) && user.getRoleId().contains(headerRole)) {
				R<List<String>> roleResult = sysClient.getRoleAliases(headerRole);
				if (roleResult.isSuccess()) {
					userInfo.setRoles(roleResult.getData());
				}
				user.setRoleId(headerRole);
			}
			// 成功则清除登录错误次数
			delFailCount(tenantId, username);
			log.info("AllcoreUserDetailsServiceImpl===========fromType:{}",fromTypeParam);
			return new AllcoreUserDetails(
				user.getId(), user.getTenantId(), StringPool.EMPTY, user.getName(), user.getRealName(),
				user.getDeptId(), deptCode, longitude, latitude, elevation, user.getPostId(),
				user.getRoleId(), Func.join(userInfo.getRoles()), Func.toStr(user.getAvatar(), TokenUtil.DEFAULT_AVATAR),
				username, passWordEncoder + user.getPassword(), userInfo.getDetail(), true,
				true, true, true,
				AuthorityUtils.commaSeparatedStringToAuthorityList(Func.join(result.getData().getRoles())),user.getAppCode(),
				user.getMajor(),Func.join(userInfo.getRoleNames()),fromTypeParam,
				user.getAccountType(),user.getIsFlyer(),DateUtil.formatDateTime(user.getCreateTime()),Func.join(userInfo.getRoleHomeConfigs()),
				area,areaName,deptCategory,indexConfig.getRefType());
		} else {
			saveLog(request,result.getMsg());
			throw new UsernameNotFoundException(result.getMsg());
		}
	}

	/**
	 * （登录异常时 页面跳到了isc登录页 需要清除登出状态）
	 * <AUTHOR>
	 * @date 2022/12/22 19:36
	 * @return void
	 */
	private void iscLogOut(String userId) {
		if(CommonConstant.ISC_INDEX_FLAG.equals(indexConfig.getIndexFlag())){
			log.info("=================退出登录调用isc logout接口=================start");
			//退出isc_url
			String iscUserId = userId;
			String logoutUrl = indexConfig.getServer() + "/logout?iscUserId=" + iscUserId;
			log.info("IscHttpClientUtil登出接口的调用==================={}",logoutUrl);
			IscHttpClientUtil.doPost(logoutUrl);
			log.info("=================退出登录调用isc logout接口=================success");
		}
	}


	/**
	 * 获取账号错误次数
	 *
	 * @param tenantId 租户id
	 * @param username 账号
	 * @return int
	 */
	private int getFailCount(String tenantId, String username) {
		return Func.toInt(allcoreRedis.get(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username)), 0);
	}

	/**
	 * 设置账号错误次数
	 *
	 * @param tenantId 租户id
	 * @param username 账号
	 * @param count    次数
	 */
	private void setFailCount(String tenantId, String username, int count) {
		allcoreRedis.setEx(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username), count + 1, Duration.ofMinutes(30));
	}

	/**
	 * 清空账号错误次数
	 *
	 * @param tenantId 租户id
	 * @param username 账号
	 */
	private void delFailCount(String tenantId, String username) {
		allcoreRedis.del(CacheNames.tenantKey(tenantId, CacheNames.USER_FAIL_KEY, username));
	}

	/**
	 * 校验refreshToken合法性
	 *
	 * @param grantType 认证类型
	 * @param request   请求
	 */
	private boolean judgeRefreshToken(String grantType, HttpServletRequest request) {
		if (jwtProperties.getState() && jwtProperties.getSingle() && StringUtil.equals(grantType, TokenUtil.REFRESH_TOKEN_KEY)) {
			String refreshToken = request.getParameter(TokenUtil.REFRESH_TOKEN_KEY);
			Claims claims = JwtUtil.parseJWT(refreshToken);
			String tenantId = String.valueOf(claims.get("tenant_id"));
			String userId = String.valueOf(claims.get("user_id"));
			String fromType = String.valueOf(claims.get("from_type"));
			String token = JwtUtil.getRefreshToken(tenantId, userId,fromType, refreshToken);
			return StringUtil.equalsIgnoreCase(token, refreshToken);
		}
		return true;
	}
	/**
	 *  登录 判断
	 *  保存审计日志记录
	 */
	private void saveLog(HttpServletRequest request, String msg){
		String[] uri = request.getRequestURI().split("/");
		String methodName = uri[uri.length - 1];
		if (StringUtil.isNotBlank(methodName)){
			LogUsual logUsual = new LogUsual();
			/**风险等级*/
			logUsual.setLogLevel(RiskLevelEnum.OPERATE_SERIOUS.getMessage());
			/**事件类型*/
			logUsual.setLogId(DealTypeUtil.eventType(methodName));
			/**返回数据*/
			logUsual.setLogData("{\"code\":\"" + HttpStatus.UNAUTHORIZED + "\",\"msg\":\"" + msg + "\"}");
			logUsual.setMethodName(methodName);
			AuditLogPublisher.publishEvent(logUsual);
		}
	}
//	private AllcoreUserDetails handleSm(String username){
//		HttpServletRequest request = WebUtil.getRequest();
//		// 获取用户绑定ID
//		String headerDept = request.getHeader(TokenUtil.DEPT_HEADER_KEY);
//		String headerRole = request.getHeader(TokenUtil.ROLE_HEADER_KEY);
//		// 获取租户ID
//		String headerTenant = request.getHeader(TokenUtil.TENANT_HEADER_KEY);
//		String paramTenant = request.getParameter(TokenUtil.TENANT_PARAM_KEY);
//		String password = UserDecryptUtil.pwdDecrypt(request.getParameter(TokenUtil.PASSWORD_KEY));
//		String grantType = request.getParameter(TokenUtil.GRANT_TYPE_KEY);
//		// 判断租户请求头
//		if (StringUtil.isAllBlank(headerTenant, paramTenant)) {
//			saveLog(request,TokenUtil.TENANT_NOT_FOUND);
//			throw new UserDeniedAuthorizationException(TokenUtil.TENANT_NOT_FOUND);
//		}
//		// 判断令牌合法性
//		if (!judgeRefreshToken(grantType, request)) {
//			saveLog(request,TokenUtil.TOKEN_NOT_PERMISSION);
//			throw new UserDeniedAuthorizationException(TokenUtil.TOKEN_NOT_PERMISSION);
//		}
//
//		// 指定租户ID
//		String tenantId = StringUtils.isBlank(headerTenant) ? paramTenant : headerTenant;
//		// 判断登录是否锁定
//		int count = getFailCount(tenantId, username);
//		int failCount = Func.toInt(ParamCache.getValue(ACCOUNT_FAIL_COUNT_KEY), Integer.parseInt(ParamCache.getValue(LOGIN_ERROR_LOCK_TIME_KEY)));
//		if (count >= failCount) {
//			saveLog(request,TokenUtil.USER_HAS_TOO_MANY_FAILS);
//			throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_TOO_MANY_FAILS);
//		}
//
//		// 获取租户信息
//		R<Tenant> tenant = sysClient.getTenantByTenantId(tenantId);
//		if (tenant.isSuccess()) {
//			if (TokenUtil.judgeTenant(tenant.getData())) {
//				saveLog(request,TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
//				throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT_PERMISSION);
//			}
//		} else {
//			saveLog(request,TokenUtil.USER_HAS_NO_TENANT);
//			throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_TENANT);
//		}
//
//		// 获取用户类型
//		String userType = Func.toStr(request.getHeader(TokenUtil.USER_TYPE_HEADER_KEY), TokenUtil.DEFAULT_USER_TYPE);
//
//		// 远程调用返回数据
//		R<UserInfo> result;
//		// 根据不同用户类型调用对应的接口返回数据，用户可自行拓展
//		if (userType.equals(UserEnum.WEB.getName())) {
//			result = userClient.userInfo(tenantId, username, UserEnum.WEB.getName());
//		} else if (userType.equals(UserEnum.APP.getName())) {
//			result = userClient.userInfo(tenantId, username, UserEnum.APP.getName());
//		} else {
//			result = userClient.userInfo(tenantId, username, UserEnum.OTHER.getName());
//		}
//		String deptCode = "";
//		String deptCategory = "";
//		String longitude = "";
//		String latitude = "";
//		String elevation = "";
//		String area = "";
//		String areaName = "";
//
//		// 判断返回信息
//		if (result.isSuccess()) {
//			UserInfo userInfo = result.getData();
//			User user = userInfo.getUser();
//			// 用户不存在,但提示用户名与密码错误并锁定账号
//			if (user == null || user.getId() == null) {
//				setFailCount(tenantId, username, count);
//				saveLog(request,TokenUtil.USER_NOT_FOUND);
//				throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
//			}
//			// 用户存在但密码错误,超过次数则锁定账号
//			if (grantType != null && !grantType.equals(TokenUtil.REFRESH_TOKEN_KEY) && !user.getPassword().equals(password)) {
//				setFailCount(tenantId, username, count);
//				saveLog(request,TokenUtil.USER_NOT_FOUND);
//				throw new UsernameNotFoundException(TokenUtil.USER_NOT_FOUND);
//			}
//			// 用户角色不存在
//			if (Func.isEmpty(userInfo.getRoles())) {
//				saveLog(request,TokenUtil.USER_HAS_NO_ROLE);
//				throw new UserDeniedAuthorizationException(TokenUtil.USER_HAS_NO_ROLE);
//			}
//			// 多部门情况下指定单部门
//			if (Func.isNotEmpty(headerDept) && user.getDeptId().contains(headerDept)) {
//				user.setDeptId(headerDept);
//			}
//			// 多角色情况下指定单角色
//			if (Func.isNotEmpty(headerRole) && user.getRoleId().contains(headerRole)) {
//				R<List<String>> roleResult = sysClient.getRoleAliases(headerRole);
//				if (roleResult.isSuccess()) {
//					userInfo.setRoles(roleResult.getData());
//				}
//				user.setRoleId(headerRole);
//			}
//			if (Func.isNotEmpty(user.getDeptId())) {
//				R<Dept> dept = sysClient.getDept(user.getDeptId());
//				deptCode = dept.getData().getDeptCode();
//				deptCategory = String.valueOf(dept.getData().getDeptCategory());
//				longitude = dept.getData().getLongitude();
//				latitude = dept.getData().getLatitude();
//				elevation = dept.getData().getElevation();
//				area = dept.getData().getArea();
//				areaName = sysClient.getRegion(dept.getData().getArea()).getData().getName();
//			}
//			// 成功则清除登录错误次数
//			delFailCount(tenantId, username);
//			return new AllcoreUserDetails(
//				user.getId(),
//				user.getTenantId(),
//				StringPool.EMPTY,
//				user.getName(),
//				user.getRealName(),
//				user.getDeptId(),
//				deptCode,
//				longitude,
//				latitude,
//				elevation,
//				user.getPostId(),
//				user.getRoleId(),
//				Func.join(userInfo.getRoles()),
//				Func.toStr(user.getAvatar(), TokenUtil.DEFAULT_AVATAR),
//				username,
//				"{noop}"+ request.getParameter(TokenUtil.PASSWORD_KEY),
//				userInfo.getDetail(),
//				true,
//				true,
//				true,
//				true,
//				AuthorityUtils.commaSeparatedStringToAuthorityList(Func.join(result.getData().getRoles())),user.getAppCode(),
//				user.getMajor(),Func.join(userInfo.getRoleNames()),user.getFromType(),
//				user.getAccountType(),user.getIsFlyer(),DateUtil.formatDateTime(user.getCreateTime()),
//				Func.join(userInfo.getRoleHomeConfigs()),
//				area,areaName,deptCategory,indexConfig.getRefType());
//		} else {
//			saveLog(request,result.getMsg());
//			throw new UsernameNotFoundException(result.getMsg());
//		}
//	}
}
