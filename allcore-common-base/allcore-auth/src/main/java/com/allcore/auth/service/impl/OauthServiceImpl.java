package com.allcore.auth.service.impl;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import com.allcore.auth.service.OauthService;
import com.allcore.common.constant.LoginManageConstant;
import com.allcore.core.log.exception.ServiceException;
import com.allcore.core.log.feign.ILogClient;
import com.allcore.core.tool.api.R;
import com.allcore.core.tool.utils.DateUtil;
import com.allcore.core.tool.utils.StringPool;
import com.allcore.core.tool.utils.StringUtil;
import com.allcore.core.tool.utils.WebUtil;
import com.allcore.system.cache.ParamCache;
import com.allcore.user.entity.User;
import com.allcore.user.feign.IUserClient;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2022/08/31 16:14
 **/
@Service
@AllArgsConstructor
@Slf4j
public class OauthServiceImpl implements OauthService {

	private static final String HEADER_TENANT_ID = "Tenant-Id";

	private final IUserClient userClient;

	private final ILogClient logClient;

	@Override
	public boolean updateUserLoginInfo(String loginStatus,String account,String tenantId,String fromType) {
		HttpServletRequest request = WebUtil.getRequest();
		User user = new User();
		user.setAccount(account);
		user.setTenantId(tenantId);
		user.setLoginStatus(loginStatus);
		user.setLastLoginTime(DateUtil.now());
		user.setLastLoginIp(WebUtil.getIpAddr(request));
		user.setFromType(fromType);
		R<Boolean> rst = userClient.updateUserLoginInfo(user);
		return rst.isSuccess();
	}

	@Override
	public boolean updateUserLoginInfoAndCode(String loginStatus,String account,String tenantId,String appCode,String fromType) {
		HttpServletRequest request = WebUtil.getRequest();
		User user = new User();
		user.setAccount(account);
		user.setTenantId(tenantId);
		user.setLoginStatus(loginStatus);
		user.setLastLoginTime(DateUtil.now());
		user.setLastLoginIp(WebUtil.getIpAddr(request));
		user.setAppCode(appCode);
		user.setFromType(fromType);
		R<Boolean> rst = userClient.updateUserLoginInfoAndCode(user);
		return rst.isSuccess();
	}

	/**
	 * （    //token用户会话超时 看代码之后备注
	 * 		// 1.持久化、并重写了官方 的客户端配置数据
	 * 		// AllcoreClientDetailsServiceImpl clientDetailsService = new AllcoreClientDetailsServiceImpl(dataSource);
	 * 		// clientDetailsService.setSelectClientDetailsSql(AuthConstant.DEFAULT_SELECT_STATEMENT);
	 * 		// clientDetailsService.setFindClientDetailsSql(AuthConstant.DEFAULT_FIND_STATEMENT);
	 * 		// 2.框架调用 AllcoreClientDetailsServiceImpl）
	 * <AUTHOR>
	 * @date 2022/09/05 14:16
	 * @param parameters
	 * @return void
	 */
	@Override
	public void validateLoginManage(Map<String, String> parameters,String tenantId) {
		log.info("validateLoginManage中的parameters:{}",parameters);
		log.info("validateLoginManage中的tenantId:{}",tenantId);
		//校验ip和IP段
		validateIp();
		//校验用户登陆时间
		validateTime();
		//校验同时在线人数
		validateUserThread();
		//校验口令有效期、账号有效期
		log.info("validateAccountAndPassword中的 parameters.get(\"username\"):{}",parameters.get("username"));
		validateAccountAndPassword(
			parameters.get("username"),
			tenantId
		);
		//校验审计日志大小
		validateAuditLogSize();

	}

	private void validateAuditLogSize() {
		R result = logClient.queryUsualLogSize();
		if(result.isSuccess()){
			BigDecimal configSize = new BigDecimal(ParamCache.getValue(LoginManageConstant.AUDIT_LOG_SAVE_SIZE_KEY));
			BigDecimal nowSize = new BigDecimal((String) result.getData());
			if( configSize.compareTo(nowSize) == -1){
				throw new ServiceException("审计日志大小超限");
			}
		}
	}

	private void validateAccountAndPassword(String account, String tenantId) {
		R<Boolean> result = userClient.validateAccountAndPassword(account, tenantId);
		if(result.isSuccess()){
			if(!result.getData()){
				throw new ServiceException("用户已禁用");
			}
		}
	}

	/**
	 * （校验同时在线人数）
	 * <AUTHOR>
	 * @date 2022/09/02 15:19
	 * @return void
	 */
	private void validateUserThread() {
		R<Long> result = userClient.nowUserLoginCount();
		if(result.isSuccess()){
			long allowThreadUserCount = Long.parseLong(ParamCache.getValue(LoginManageConstant.ALLOW_THREAD_USER_COUNT_KEY));
			if(result.getData()>allowThreadUserCount){
				throw new ServiceException("同时在线人数已超");
			}
		}
	}

	/**
	 * （校验用户登陆时间）
	 * <AUTHOR>
	 * @date 2022/09/02 14:53
	 * @return void
	 */
	private void validateTime() {
		long allowLoginStartTime = Long.parseLong(ParamCache.getValue(LoginManageConstant.ALLOW_LOGIN_START_TIME_KEY).replace(":",""));
		long allowLoginEndTime = Long.parseLong(ParamCache.getValue(LoginManageConstant.ALLOW_LOGIN_END_TIME_KEY).replace(":",""));
		long nowTime = Long.parseLong(DateUtil.formatTime(new Date()).replace(":",""));
		if(nowTime < allowLoginStartTime || nowTime>allowLoginEndTime){
			throw new ServiceException("登录时间校验失败");
		}
	}

	/**
	 * （校验ip和IP段）
	 * <AUTHOR>
	 * @date 2022/09/02 14:53
	 * @return void
	 */
	private void validateIp() {
		//校验ip和IP段
		String allowFlag = ParamCache.getValue(LoginManageConstant.ALLOW_FLAG_KEY);
		if(StringPool.ONE.equals(allowFlag)){
			//整个四位ip
			String allowIp = ParamCache.getValue(LoginManageConstant.ALLOW_IP_KEY);
			//第四位数字
			String allowIpRange = ParamCache.getValue(LoginManageConstant.ALLOW_IP_RANGE_KEY);
			HttpServletRequest request = WebUtil.getRequest();
			String loginIp = WebUtil.getIpAddr(request);
			//校验 前三位ip是否相同
			String allowIpPre3 = allowIp.substring(0,allowIp.lastIndexOf(StringPool.DOT));
			String loginIpPre3 = loginIp.substring(0,loginIp.lastIndexOf(StringPool.DOT));
			String allowIpEnd4 = allowIp.substring(allowIp.lastIndexOf(StringPool.DOT)+1);
			boolean validate4 = true;
			if(StringUtil.isNotBlank(allowIpRange) && !allowIpEnd4.equals(allowIpRange)){
				validate4 = false;
			}
			if(!allowIpPre3.equals(loginIpPre3) || !validate4){
				throw new ServiceException("登录IP校验失败");
			}
		}
	}

}
