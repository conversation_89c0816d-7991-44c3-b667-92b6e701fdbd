package com.allcore.auth.constant;

/**
 * 授权校验常量
 *
 * <AUTHOR>
 */
public interface AuthConstant {

	/**
	 * 密码加密规则
	 */
	String ENCRYPT = "{allcore}";

	/**
	 * sys_client表字段
	 */
	String CLIENT_FIELDS = "client_id, CONCAT('{noop}',client_secret) as client_secret, resource_ids, scope, authorized_grant_types, " +
		"web_server_redirect_uri, authorities, access_token_validity, " +
		"refresh_token_validity, additional_information, autoapprove";

	/**
	 * sys_client查询语句
	 */
	String BASE_STATEMENT = "select " + CLIENT_FIELDS + " from sys_client";

	/**
	 * sys_client查询排序
	 */
	String DEFAULT_FIND_STATEMENT = BASE_STATEMENT + " order by client_id";

	/**
	 * 查询client_id
	 */
	String DEFAULT_SELECT_STATEMENT = BASE_STATEMENT + " where client_id = ?";

	/**
	 *  初始默认密码
	 */
	String DEFAULT_PASSWORD = "Jsepc01!";


	/**
	 * 默认租户id
	 */
	String TENANT_ID ="000000";

	/**
	 * 南瑞角色权限id
	 */
	String ROLE_ID="1792134773921361923";

	/**
	 *  默认部门id(内蒙古区域公司)
	 */
	String DEPT_ID="1744281628411506689";

}
